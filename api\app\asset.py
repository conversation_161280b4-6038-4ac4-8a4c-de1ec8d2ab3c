from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from clientmodels import Asset, UserAsset

app_asset_api = Namespace('api_app_asset', description='Asset API')

parser = app_asset_api.parser()
## add pagnation
parser.add_argument('page', type=int, help='Page number', location='args', required=False, default=1)
parser.add_argument('limit', type=int, help='Limit number', location='args', required=False, default=10)
parser.add_argument('search', type=str, help='Search keyword', location='args', required=False)
parser.add_argument('is_starred', type=bool, help='Is starred', location='args', required=False)
parser.add_argument('type', type=str, help='Asset type', location='args', required=False)

@app_asset_api.doc(security='bearer')
@app_asset_api.route('/list', methods=['GET'])
class AssetList(Resource):
    @app_asset_api.expect(parser)
    def get(self):
        ## get user id
        user_id = g.user_id

        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        search = args.get('search', None)
        is_starred = args.get('is_starred', None)
        asset_type = args.get('type', None)

        ## get asset id list from user asset table
        user_assets = g.db_session.query(UserAsset).filter_by(user_id=user_id).all()

        if is_starred == True:
            user_assets = [asset for asset in user_assets if asset.is_starred == is_starred]

        asset_ids = [asset.asset_id for asset in user_assets]
        starred_asset_ids = [asset.asset_id for asset in user_assets if asset.is_starred]

        ## get asset list
        query = g.db_session.query(Asset).filter(Asset.id.in_(asset_ids), Asset.is_deleted == False)
        if search:
            query = query.filter(Asset.name.ilike(f"%{search}%"))

        if asset_type:
            query = query.filter(Asset.file_type == asset_type)

        total = query.count()
        offset = (page - 1) * limit
        assets = query.order_by(Asset.date_created.desc()).offset(offset).limit(limit).all()
        data = []
        for asset in assets:
            asset_dict = asset.to_dict()
            asset_dict['is_starred'] = asset.id in starred_asset_ids
            data.append(asset_dict)

        return create_response("api.success", data=data, total=total, page=page, limit=limit)



@app_asset_api.doc(security='bearer')
@app_asset_api.route('/<string:asset_id>', methods=['GET'])
class AssetItem(Resource):
    def get(self, asset_id):
        user_id = g.user_id

        # Check if the user has access to the asset
        user_asset = g.db_session.query(UserAsset).filter_by(user_id=user_id, asset_id=asset_id).first()
        if not user_asset:
            return create_response("Asset not found or access denied")

        # Get the asset
        asset = g.db_session.query(Asset).filter_by(id=asset_id).first()
        if not asset or asset.is_deleted:
            return create_response("Asset not found")
        
        data = asset.to_dict()
        data['is_starred'] = user_asset.is_starred

        return create_response("api.success", data=data)
    


star_model = app_asset_api.model('Star', {
    'is_starred': fields.Boolean(required=True)
})

@app_asset_api.doc(security='bearer')
@app_asset_api.route('/<string:asset_id>/star', methods=['POST'])
class AssetStar(Resource):
    @app_asset_api.expect(star_model)
    def post(self, asset_id):
        user_id = g.user_id

        # Check if the user has access to the asset
        user_asset = g.db_session.query(UserAsset).filter_by(user_id=user_id, asset_id=asset_id).first()
        if not user_asset:
            return create_response("Asset not found or access denied")

        data = request.json
        is_starred = data.get('is_starred', False)

        user_asset.is_starred = is_starred
        g.db_session.commit()

        return create_response("api.success")