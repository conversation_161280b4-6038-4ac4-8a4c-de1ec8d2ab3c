<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[XAPA] {{ notificationTitle }}</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* Header */
        .header {
            text-align: center;
            padding: 30px 30px 20px;
            background-color: #ffffff;
        }
        
        .logo {
            max-width: 200px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        
        /* Content */
        .content {
            padding: 10px 30px 30px;
            color: #333333;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333333;
        }
        
        .intro-text {
            margin-bottom: 20px;
            font-size: 16px;
            color: #555555;
        }
        
        /* Message box */
        .message-box {
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #6700bb;
            border-radius: 4px;
            font-size: 16px;
            line-height: 1.5;
        }
        
        /* CTA Button */
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .cta-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #6700bb;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        
        .cta-button:hover {
            background-color: #5200a0;
        }
        
        /* Divider */
        .divider {
            border: none;
            border-top: 1px solid #dddddd;
        }
        
        /* Footer */
        .footer {
            padding: 20px 30px 30px;
            font-size: 14px;
            color: #666666;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        .footer p {
            margin-bottom: 15px;
        }
        
        .footer a {
            color: #6700bb;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .signature {
            font-weight: bold;
            margin-top: 20px;
            font-size: 16px;
            color: #333333;
        }
        
        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header,
            .content,
            .footer {
                padding-left: 20px;
                padding-right: 20px;
            }
            
            .logo {
                max-width: 150px;
            }
            
            .greeting {
                font-size: 16px;
            }
            
            .cta-button {
                display: block;
                text-align: center;
                margin: 20px auto;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header with Logo -->
        <div class="header">
            <img src="https://xapabackend.blob.core.windows.net/public/xapa.png" alt="Xapa" class="logo">
        </div>
        
        <!-- Main Content -->
        <div class="content">
            <div class="greeting">Hello {{ firstName }}!</div>
            
            <p class="intro-text">
                You're receiving this message because there's a notification for you from the Xapa App!
            </p>
            
            <!-- Notification Message -->
            <div class="message-box">
                {{ notificationBody }}
            </div>
            
            <!-- Call to Action Button -->
            <div class="cta-container">
                <a href="{{ clickHereLink }}" class="cta-button">Click Here</a>
            </div>
        </div>
        
        <!-- Divider -->
        <hr class="divider">
        
        <!-- Footer -->
        <div class="footer">
            <p>
                To adjust your preferences for receiving these email notifications,
                please visit the <a href="{{ unsubscribeLink }}">edit profile page</a> in the Xapa App.
            </p>
            
            <p>
                If you have questions or would like more information about receiving these emails,
                please <a href="https://xapa.freshdesk.com/support/tickets/new">contact support</a>.
            </p>
            
            <div class="signature">
                <p>- The Xapa Team</p>
            </div>
            <br>
            <p style="font-size: 12px; color: #999999;">
                © Xapa World, Inc. | All Rights Reserved | Proprietary and Confidential
            </p>
        </div>
    </div>
</body>
</html>