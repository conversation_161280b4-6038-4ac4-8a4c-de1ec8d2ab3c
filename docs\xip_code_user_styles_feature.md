# Xip Code (User Styles) Update Feature

## Overview

The Xip Code (User Styles) Update feature automatically updates user style values (called "xip codes") when a user completes a quest. This feature allows the system to track and update user preferences or characteristics based on their quest completion patterns.

## How It Works

### 1. Quest Completion Trigger
When a user completes a quest via the `CompleteQuest` API endpoint (`POST /api/app/quest/{quest_id}/complete`), the system:
- Marks the quest as completed
- Processes rewards (chests, badges)
- **NEW**: Checks for and updates user styles (xip codes)

### 2. Style Mapping Check
The system checks the `user_style_mapping` table for a record where:
- `mapping_quest_id` matches the completed quest ID

### 3. Configuration Processing
If a mapping is found, the system:
- Retrieves the `mapping_configuration` field (JSON array)
- Parses the configuration which contains objects like: `[{"node_id": "node-1", "value": "value1"}, {"node_id": "node-2", "value": "value2"}]`

### 4. User History Analysis
The system queries the `user_node_history` table to find:
- All completed nodes by the user that match node IDs from the configuration
- The **latest** completed node based on `date_completed` timestamp

### 5. Style Update
Based on the latest matching node, the system:
- Gets the corresponding value from the mapping configuration
- Creates a new record or updates an existing record in the `user_style` table
- Sets `style_id` to the mapping record ID and `style_value` to the configuration value

## Database Schema

### user_style_mapping
- `id`: Primary key (used as style_id in user_style)
- `style_name`: Human-readable name for the style
- `mapping_quest_id`: Quest ID that triggers this style update
- `mapping_configuration`: JSON array of {node_id, value} objects

### user_style
- `id`: Primary key
- `user_id`: User who owns this style
- `style_id`: References user_style_mapping.id
- `style_value`: Current value for this user's style (the "xip code")
- Constraint: One record per user per style_id

### user_node_history
- Contains completion history for all quest nodes
- `date_completed`: Used to determine the latest matching node

## API Response

When a style is updated during quest completion, the response includes:

```json
{
  "message": "Quest completed",
  "data": {"quest_id": "...", "user_id": "..."},
  "rewards": {
    "chests": [...],
    "badges": [...],
    "style_update": {
      "action": "created" | "updated",
      "style_id": "mapping-id",
      "style_name": "Style Name",
      "style_value": "new-value",
      "latest_node_id": "node-id",
      "quest_id": "quest-id"
    }
  }
}
```

## Implementation Files

- `services/user_style_service.py`: Core logic for style updates
- `api/app/quest.py`: Integration point in CompleteQuest endpoint
- `tests/test_user_style_service.py`: Unit tests
- `tests/test_quest_completion_integration.py`: Integration tests

## Example Scenario

1. **Setup**: A quest "Personality Assessment" has a mapping with configuration:
   ```json
   [
     {"node_id": "personality-question-1", "value": "introvert"},
     {"node_id": "personality-question-2", "value": "extrovert"}
   ]
   ```

2. **User Journey**: User completes the quest, answering both personality questions at different times

3. **Processing**: System finds the latest answered question was "personality-question-2" 

4. **Result**: User's style is set to "extrovert" in the user_style table

## Error Handling

The feature includes robust error handling for:
- Missing style mappings (quest not configured for style updates)
- Invalid JSON configurations
- Empty or malformed mapping configurations
- No matching nodes in user history
- Database errors (with rollback)

All errors are logged appropriately and don't interrupt the quest completion flow.

## Future Enhancements

Potential improvements could include:
- Style versioning/history
- Batch style updates for multiple quests
- Style dependencies and rules
- Admin interface for managing style mappings