import logging

from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from api.common.scheduler import SchedulerFunction
from clientmodels import get_db_session, Scheduler

logger = logging.getLogger(__name__)

scheduler_execution_api = Namespace('scheduler_execution_api', description='Scheduler execution operations')

@scheduler_execution_api.route('/execute', methods=['PUT'])
class SchedulerExecutionObject(Resource):
    def put(self):
        """
        Process all scuhedulers.
        Returns 200 for successful processing of all scuhedulers.
        Returns 400 for invalid requests and 500 for processing errors.
        """
        try:
            db_session = get_db_session('global')
            g.db_session = db_session

            sf = SchedulerFunction()
            sf.execute_scheduler()
            return create_response("Execute scheduler successfully", 200)

        except Exception as e:
            logger.error(f"Error processing scheduler: {str(e)}")
            return create_response(f"Execute scheduler failed: {str(e)}", 500)


scheduler_model = scheduler_execution_api.model('Scheduler', {
    'name': fields.String(required=True),
    'func': fields.String(required=True),
    'cron': fields.String(required=True),
    'data': fields.Raw(required=True),
})

@scheduler_execution_api.route('/', methods=['POST'])
@scheduler_execution_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class SchedulerObject(Resource):
    def get(self, id):
        """
        Get a scheduler by ID.
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        scheduler = g.db_session.query(Scheduler).filter(Scheduler.id == id, Scheduler.is_deleted == False).first()
        if not scheduler:
            return create_response("Scheduler not found", 404)
        return create_response("Scheduler retrieved successfully", 200, data=scheduler.to_dict())
    
    @scheduler_execution_api.expect(scheduler_model)
    def post(self):
        """
        Create a new scheduler.
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        data = request.json
        name = data.get('name')
        func = data.get('func')
        cron = data.get('cron')
        scheduler_data = data.get('data')

        sf = SchedulerFunction()
        scheduler = sf.create_scheduler(name, func, cron, scheduler_data)
        return create_response("Scheduler created successfully", 200, data=scheduler.to_dict())

    @scheduler_execution_api.expect(scheduler_model)
    def put(self, id):
        """
        Update a scheduler by ID.
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        data = request.json
        name = data.get('name')
        func = data.get('func')
        cron = data.get('cron')
        scheduler_data = data.get('data')

        sf = SchedulerFunction()
        scheduler = sf.update_scheduler(id, name, func, cron, scheduler_data)
        return create_response("Scheduler updated successfully", 200, data=scheduler)

    def delete(self, id):
        """
        Delete a scheduler by ID.
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        sf = SchedulerFunction()
        sf.delete_scheduler(id)
        return create_response("Scheduler deleted successfully", 200)