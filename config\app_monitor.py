import psutil
import threading
import time
import logging
from flask import current_app

logger = logging.getLogger(__name__)

class ApplicationMonitor:
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
        self.monitoring = True
        
    def start_monitoring(self):
        """Start background monitoring thread"""
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        logger.info("Application monitoring started")
        
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring:
            try:
                # Get memory usage
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / (1024 * 1024)
                
                # Log if memory usage is high
                if memory_mb > 800:  # 800MB threshold
                    logger.warning(f"High memory usage detected: {memory_mb:.1f}MB")
                
                # Get CPU usage
                cpu_percent = process.cpu_percent()
                
                # Log stats every 5 minutes
                uptime = time.time() - self.start_time
                if int(uptime) % 300 == 0:  # Every 5 minutes
                    logger.info(f"App Stats - Uptime: {uptime/3600:.1f}h, Memory: {memory_mb:.1f}MB, "
                              f"CPU: {cpu_percent:.1f}%, Requests: {self.request_count}, Errors: {self.error_count}")
                
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(60)
                
    def record_request(self):
        """Record a request"""
        self.request_count += 1
        
    def record_error(self):
        """Record an error"""
        self.error_count += 1

# Global monitor instance
app_monitor = ApplicationMonitor()
