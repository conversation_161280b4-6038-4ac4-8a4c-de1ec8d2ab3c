import unittest
import json
import datetime

from api.account.authentication import generate_tokens
from app import create_app
from clientmodels import *
from models import *


class TestUserManagerUpdate(unittest.TestCase):
    def setUp(self):
        # Create an instance of the app with the testing configuration
        self.app = create_app('config.test')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        with self.app.app_context():
            db.create_all()

        # Load all the bindings
        for bind in app.config.get('SQLALCHEMY_BINDS', {}):
            # Get the engine for the bind
            engine = db.get_engine(bind=bind)
            # Reflect the models to the metadata
            db.Model.metadata.reflect(bind=engine)
            # Create all tables in the metadata
            db.Model.metadata.create_all(bind=engine)

        self.db_session = get_db_session("test")

        # Create test users: one regular user and one manager
        self.manager_user = User(
            email='<EMAIL>', 
            first_name='Manager', 
            last_name='User'
        )
        self.db_session.add(self.manager_user)
        self.db_session.commit()

        self.regular_user = User(
            email='<EMAIL>', 
            first_name='Regular', 
            last_name='User',
            manager_id=self.manager_user.id  # Initially has a manager
        )
        self.db_session.add(self.regular_user)
        self.db_session.commit()

        # Create admin user for testing the CMS endpoints
        self.admin_user = User(
            email='<EMAIL>',
            first_name='Admin',
            last_name='User'
        )
        self.db_session.add(self.admin_user)
        self.db_session.commit()

        # Create admin record
        admin_record = Admin(
            user_id=self.admin_user.id,
            role='super_admin',
            is_active=True
        )
        self.db_session.add(admin_record)
        self.db_session.commit()

        # Generate token for admin user
        token = generate_tokens(self.admin_user.id, 'test')
        self.admin_token = token[0]
        
        self.admin_user_token = UserToken(
            user_id=self.admin_user.id,
            token=self.admin_token,
            refresh_token=token[1],
            date_expired=datetime.datetime.utcnow() + datetime.timedelta(days=1),
            device_id="test_device",
            login_type="test"
        )
        self.db_session.add(self.admin_user_token)
        self.db_session.commit()

    def tearDown(self):
        # Tear down the database
        self.db_session.close()
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_update_user_manager_id(self):
        """Test that we can set and update a user's manager_id"""
        headers = {
            'Authorization': self.admin_token,
            'Content-Type': 'application/json'
        }

        # Test 1: Update user to have a different manager
        new_manager = User(
            email='<EMAIL>',
            first_name='New',
            last_name='Manager'
        )
        self.db_session.add(new_manager)
        self.db_session.commit()

        update_data = {
            'first_name': self.regular_user.first_name,
            'last_name': self.regular_user.last_name,
            'email': self.regular_user.email,
            'manager_id': new_manager.id
        }

        response = self.client.put(
            f'/api/cms/users/{self.regular_user.id}',
            data=json.dumps(update_data),
            headers=headers
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify the manager was updated
        updated_user = self.db_session.query(User).filter_by(id=self.regular_user.id).first()
        self.assertEqual(updated_user.manager_id, new_manager.id)

    def test_remove_user_manager_id(self):
        """Test that we can remove a user's manager by sending empty manager_id"""
        headers = {
            'Authorization': self.admin_token,
            'Content-Type': 'application/json'
        }

        # Verify user initially has a manager
        user_before = self.db_session.query(User).filter_by(id=self.regular_user.id).first()
        self.assertIsNotNone(user_before.manager_id)
        self.assertEqual(user_before.manager_id, self.manager_user.id)

        # Test: Remove manager by sending empty manager_id
        update_data = {
            'first_name': self.regular_user.first_name,
            'last_name': self.regular_user.last_name,
            'email': self.regular_user.email,
            'manager_id': ''  # Empty string to remove manager
        }

        response = self.client.put(
            f'/api/cms/users/{self.regular_user.id}',
            data=json.dumps(update_data),
            headers=headers
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify the manager was removed (set to None)
        updated_user = self.db_session.query(User).filter_by(id=self.regular_user.id).first()
        self.assertIsNone(updated_user.manager_id)

    def test_update_user_without_manager_id_field(self):
        """Test that when manager_id is not included in the update, it doesn't change"""
        headers = {
            'Authorization': self.admin_token,
            'Content-Type': 'application/json'
        }

        # Get initial manager_id
        user_before = self.db_session.query(User).filter_by(id=self.regular_user.id).first()
        initial_manager_id = user_before.manager_id

        # Update user without including manager_id field
        update_data = {
            'first_name': 'Updated',
            'last_name': self.regular_user.last_name,
            'email': self.regular_user.email
            # No manager_id field included
        }

        response = self.client.put(
            f'/api/cms/users/{self.regular_user.id}',
            data=json.dumps(update_data),
            headers=headers
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify the manager_id hasn't changed
        updated_user = self.db_session.query(User).filter_by(id=self.regular_user.id).first()
        self.assertEqual(updated_user.manager_id, initial_manager_id)
        self.assertEqual(updated_user.first_name, 'Updated')


if __name__ == '__main__':
    unittest.main()