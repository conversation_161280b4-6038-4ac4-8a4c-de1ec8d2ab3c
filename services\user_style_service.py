import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List


def update_user_style_on_quest_completion(quest_id: str, user_id: str) -> Optional[Dict[str, Any]]:
    """
    Update user style (xip code) when a user completes a quest.
    
    Steps:
    1. Check if the quest has a mapping in user_style_mapping table
    2. If found, get mapping_configuration (list of {node_id, value} dicts)
    3. Check user_node_history for completed nodes from mapping_configuration
    4. Get the latest completed node based on date_completed
    5. Create or update user_style record with the style_id and corresponding value
    
    Args:
        quest_id: ID of the completed quest
        user_id: ID of the user who completed the quest
        
    Returns:
        Dictionary with updated style information, or None if no update was made
    """
    try:
        # Import here to avoid circular import issues and allow for testing
        from flask import g
        from clientmodels import UserStyleMapping, UserStyle, UserNodeHistory
        
        # Step 1: Check if this quest has a mapping in user_style_mapping table
        style_mapping = g.db_session.query(UserStyleMapping).filter_by(
            mapping_quest_id=quest_id
        ).first()
        
        if not style_mapping:
            logging.debug(f"No style mapping found for quest_id: {quest_id}")
            return None
            
        # Step 2: Get the mapping configuration
        mapping_configuration = style_mapping.mapping_configuration
        if not mapping_configuration:
            logging.warning(f"Empty mapping_configuration for quest_id: {quest_id}, style_mapping_id: {style_mapping.id}")
            return None
            
        # Parse mapping_configuration if it's a string
        if isinstance(mapping_configuration, str):
            try:
                mapping_configuration = json.loads(mapping_configuration)
            except json.JSONDecodeError:
                logging.error(f"Invalid JSON in mapping_configuration for style_mapping_id: {style_mapping.id}")
                return None
        
        if not isinstance(mapping_configuration, list):
            logging.error(f"mapping_configuration should be a list for style_mapping_id: {style_mapping.id}")
            return None
            
        # Extract node_ids from mapping_configuration
        node_ids = []
        node_value_map = {}
        for config in mapping_configuration:
            if isinstance(config, dict) and 'node_id' in config and 'value' in config:
                node_ids.append(config['node_id'])
                node_value_map[config['node_id']] = config['value']
        
        if not node_ids:
            logging.warning(f"No valid node_ids found in mapping_configuration for style_mapping_id: {style_mapping.id}")
            return None
            
        # Step 3: Check user_node_history for completed nodes
        # Get the latest completed node from the mapping_configuration nodes
        latest_node_history = g.db_session.query(UserNodeHistory).filter(
            UserNodeHistory.user_id == user_id,
            UserNodeHistory.node_id.in_(node_ids)
        ).order_by(UserNodeHistory.date_completed.desc()).first()
        
        if not latest_node_history:
            logging.debug(f"No completed nodes found in user_node_history for user_id: {user_id}, node_ids: {node_ids}")
            return None
            
        # Step 4: Get the value for the latest completed node
        latest_node_id = latest_node_history.node_id
        style_value = node_value_map.get(latest_node_id)
        
        if not style_value:
            logging.error(f"No value found for node_id: {latest_node_id} in mapping_configuration")
            return None
            
        # Step 5: Create or update user_style record
        # Check if user already has a style record for this style_id
        existing_user_style = g.db_session.query(UserStyle).filter_by(
            user_id=user_id,
            style_id=style_mapping.id
        ).first()
        
        if existing_user_style:
            # Update existing record
            old_value = existing_user_style.style_value
            existing_user_style.style_value = style_value
            existing_user_style.date_updated = datetime.utcnow()
            action = "updated"
            logging.info(f"Updated user_style for user_id: {user_id}, style_id: {style_mapping.id}, old_value: {old_value}, new_value: {style_value}")
        else:
            # Create new record
            new_user_style = UserStyle(
                user_id=user_id,
                style_id=style_mapping.id,
                style_value=style_value,
                date_created=datetime.utcnow(),
                date_updated=datetime.utcnow()
            )
            g.db_session.add(new_user_style)
            action = "created"
            logging.info(f"Created new user_style for user_id: {user_id}, style_id: {style_mapping.id}, style_value: {style_value}")
        
        # Commit the changes
        g.db_session.commit()
        
        return {
            "action": action,
            "style_id": style_mapping.id,
            "style_name": style_mapping.style_name,
            "style_value": style_value,
            "latest_node_id": latest_node_id,
            "quest_id": quest_id
        }
        
    except Exception as e:
        # Import here to avoid issues if flask is not available
        try:
            from flask import g
            g.db_session.rollback()
        except:
            pass
        logging.error(f"Error updating user style for quest_id: {quest_id}, user_id: {user_id}: {str(e)}")
        return None