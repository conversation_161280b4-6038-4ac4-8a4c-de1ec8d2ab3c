import os
import logging

from flask import Flask
from elasticapm.contrib.flask import ElasticAPM

logger = logging.getLogger(__name__)

# Elastic APM Configuration
def init_apm(app: Flask) -> None:
    # Get environment from app config
    is_production = app.config.get('IS_PRODUCTION', False)
    if not is_production:
        return None

    # Get environment from app config
    # environment = app.config.get('ENVIRONMENT', 'dev')
    
    # Uncomment to enable Elastic APM
    # app.config.update(
    #     ELASTIC_APM={
    #         'SERVICE_NAME': 'xapa-backend',
    #         'SECRET_TOKEN': '5/NyE3RFyeTdStzyw7m8wC/9lSDjYKAKyak6HPT17UI=',
    #         'SERVER_URL': 'http://52.255.135.118:8200',
    #         'ENVIRONMENT': environment,
    #         'CAPTURE_HEADERS': True,
    #         'DEBUG': environment == 'dev'
    #     }
    # )

    # # Initialize Elastic APM
    # apm = ElasticAPM(app)

    # return apm

    return None