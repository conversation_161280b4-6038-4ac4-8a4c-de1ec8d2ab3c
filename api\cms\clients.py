import copy
import datetime
import secrets
import string

from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from api.common.decorator import check_user_permission
from api.common.file import FileService
from api.common.helper import create_response
from api.common.publish_utils import (
    publish_cast_to_client, publish_xapa_xircle, public_all_xapa_feed,
    publish_database_structure, publish_attribute_to_client 
)
from clientmodels import Client, Package, SSOConfiguration, User, UserAssignment, get_db_session, create_client_db

cms_clients_api = Namespace('api_cms_clients', description='Client management related operations')

## load default client settings from static/files/client_settings.json
client_settings = {}
try:
    with open('static/files/client_settings.json', 'r') as f:
        client_settings = json.load(f)
except:
    pass


def ensure_ai_preferences(client_settings, db_session):
    """Ensure AI preferences are present, fallback to global settings if missing"""
    try:
        # Check if AI preferences exist and have all required fields
        ai_config = client_settings.get('feature_configuration', {}).get('ai', {})
        ai_preferences = ai_config.get('preferences', {})
        
        required_fields = ['agreement_title', 'agreement_terms_body', 'checkbox_label', 'button_label', 'version']
        missing_fields = [field for field in required_fields if not ai_preferences.get(field)]
        
        if missing_fields:
            # Get global client settings as fallback
            global_client = db_session.query(Client).filter(Client.id_key == "global").first()
            if global_client and global_client.settings:
                global_ai_config = global_client.settings.get('feature_configuration', {}).get('ai', {})
                global_ai_preferences = global_ai_config.get('preferences', {})
                
                # Create a deep copy of client settings to avoid modifying the original
                updated_settings = copy.deepcopy(client_settings)
                
                # Ensure the structure exists
                if 'feature_configuration' not in updated_settings:
                    updated_settings['feature_configuration'] = {}
                if 'ai' not in updated_settings['feature_configuration']:
                    updated_settings['feature_configuration']['ai'] = {}
                if 'preferences' not in updated_settings['feature_configuration']['ai']:
                    updated_settings['feature_configuration']['ai']['preferences'] = {}
                
                # Fill in missing fields from global settings
                for field in missing_fields:
                    if global_ai_preferences.get(field):
                        updated_settings['feature_configuration']['ai']['preferences'][field] = global_ai_preferences[field]
                
                return updated_settings
        
        return client_settings
        
    except Exception as e:
        # If there's any error in fallback logic, return original settings
        return client_settings

client_model = cms_clients_api.model('Client', {
    'name': fields.String(required=True, description='Client Name'),
    'image': fields.String(required=False, description='Client Image'),
    'email_suffix': fields.List(fields.Nested(cms_clients_api.model('EmailSuffix', {
        'domain': fields.String(required=True, description='Email Domain'),
        'sso_id': fields.String(required=True, description='SSO Type ID')
    })), required=False, description='Email Suffix for SSO'),
    'contact_first_name': fields.String(required=False, description='Contact First Name'),
    'contact_last_name': fields.String(required=False, description='Contact Last Name'),
    'contact_email': fields.String(required=False, description='Contact Email'),
    'contact_phone': fields.String(required=False, description='Contact Phone'),
    'contact_title': fields.String(required=False, description='Contact Title'),
    'address1': fields.String(required=False, description='Address 1'),
    'address2': fields.String(required=False, description='Address 2'),
    'city': fields.String(required=False, description='City'),
    'state': fields.String(required=False, description='State'),
    'zip': fields.String(required=False, description='Zip'),
    'country': fields.String(required=False, description='Country'),
    'timezone': fields.String(required=False, description='Timezone'),
    'language': fields.String(required=False, description='Language'),
    'annual_license_fee': fields.String(required=False, description='Annual License Fee'),
    'contract_term': fields.String(required=False, description='Contract Term'),
    'client_type': fields.String(required=False, description='Client Type'),
    'support_plan': fields.String(required=False, description='Support Plan [standard, premium]'),
    'next_renewal_date': fields.String(required=False, description='Next Renewal Date'),
    'allocated_users': fields.Integer(required=False, description='Allocated Users'),
    'allocated_packages': fields.Integer(required=False, description='Allocated Packages'),
    'allocated_programs': fields.Integer(required=False, description='Allocated Programs'),
    'allocated_xperiences': fields.Integer(required=False, description='Allocated Xperiences'),
    'allocated_quests': fields.Integer(required=False, description='Allocated Quests'),
    'settings': fields.Raw(required=False, description='Settings', default=client_settings),
    'is_active': fields.Boolean(required=False, description='Client Status', default=True),
})

client_list_model = cms_clients_api.model('ClientList', {
    'clients': fields.List(fields.Nested(client_model), description='List of Clients')
})


@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/', methods=['POST'])
@cms_clients_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class ClientItem(Resource):
    @cms_clients_api.expect(client_model)
    @check_user_permission("super_admin")
    def post(self):
        data = request.json
        db_session = get_db_session()

        client = Client()
        client.name = data.get('name', '').strip()
        if not client.name:
            return create_response("Client name is required", status=400)
        
        # Handle image upload
        image = data.get('image', '')
        FileService.process_entity_image(client, image, 'client', client.id)

        # Basic information
        try:
            client.email_suffix = json.dumps(data['email_suffix']).strip()
        except:
            client.email_suffix = json.dumps([{"domain": "", "sso_id": ""}]).strip()

        client.contact_first_name = data.get('contact_first_name', '').strip()
        client.contact_last_name = data.get('contact_last_name', '').strip()
        client.contact_email = data.get('contact_email', '').strip()
        client.contact_phone = data.get('contact_phone', '').strip()
        client.contact_title = data.get('contact_title', '').strip()
        
        # Address information
        client.address1 = data.get('address1', '').strip()
        client.address2 = data.get('address2', '').strip()
        client.city = data.get('city', '').strip()
        client.state = data.get('state', '').strip()
        client.zip = data.get('zip', '').strip()
        client.country = data.get('country', '').strip()
        
        # Localization
        client.timezone = data.get('timezone', '').strip()
        client.language = data.get('language', '').strip()
        
        # License information
        client.annual_license_fee = data.get('annual_license_fee', '').strip()
        client.contract_term = data.get('contract_term', '').strip()
        client.client_type = data.get('client_type', '').strip()
        client.support_plan = data.get('support_plan', '').strip()
        next_renewal_date = data.get('next_renewal_date', '').strip()
        if next_renewal_date:
            try:
                client.next_renewal_date = datetime.datetime.strptime(next_renewal_date, "%Y-%m-%d")
            except ValueError:
                return create_response("Invalid next_renewal_date format. Use YYYY-MM-DD", status=400)

        # Allocation limits
        client.allocated_users = data.get('allocated_users', 0)
        client.allocated_packages = data.get('allocated_packages', 0)
        client.allocated_programs = data.get('allocated_programs', 0)
        client.allocated_xperiences = data.get('allocated_xperiences', 0)
        client.allocated_quests = data.get('allocated_quests', 0)

        # Settings and status
        client.settings = data.get('settings', client_settings)
        client.is_active = data.get('is_active', True)

        # Generate unique ID key
        random_part = ''.join(secrets.choice(string.ascii_lowercase + string.digits) for i in range(15))
        client.id_key = f'x{random_part}'

        db_session.add(client)
        db_session.commit()

        # Client database and initial data
        try:
            create_client_db(client.id_key)
            publish_xapa_xircle(client.id_key)
            public_all_xapa_feed(client.id_key)
        except Exception as e:
            db_session.rollback()
            return create_response(f"Failed to create client database: {str(e)}", status=500)

        return create_response("Client created successfully", data=client.to_dict())

    @cms_clients_api.expect(client_model)
    @check_user_permission("super_admin")
    def put(self, id):
        data = request.json
        db_session = get_db_session()
        client = db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)
        
        client.name = data.get('name', client.name).strip()
        if not client.name:
            return create_response("Client name is required", status=400)
        
        # Handle image upload
        image = data.get('image', '')
        FileService.process_entity_image(client, image, 'client', client.id)

        # Basic information
        try:
            client.email_suffix = json.dumps(data['email_suffix']).strip()
        except:
            client.email_suffix = json.dumps([{"domain": "", "sso_id": ""}]).strip()
                                             
        client.contact_first_name = data.get('contact_first_name', client.contact_first_name).strip() if data.get('contact_first_name') else client.contact_first_name
        client.contact_last_name = data.get('contact_last_name', client.contact_last_name).strip() if data.get('contact_last_name') else client.contact_last_name
        client.contact_email = data.get('contact_email', client.contact_email).strip() if data.get('contact_email') else client.contact_email
        client.contact_phone = data.get('contact_phone', client.contact_phone).strip() if data.get('contact_phone') else client.contact_phone
        client.contact_title = data.get('contact_title', client.contact_title).strip() if data.get('contact_title') else client.contact_title
        
        # Address information
        client.address1 = data.get('address1', client.address1).strip() if data.get('address1') else client.address1
        client.address2 = data.get('address2', client.address2).strip() if data.get('address2') else client.address2
        client.city = data.get('city', client.city).strip() if data.get('city') else client.city
        client.state = data.get('state', client.state).strip() if data.get('state') else client.state
        client.zip = data.get('zip', client.zip).strip() if data.get('zip') else client.zip
        client.country = data.get('country', client.country).strip() if data.get('country') else client.country
        
        # Localization
        client.timezone = data.get('timezone', client.timezone).strip() if data.get('timezone') else client.timezone
        client.language = data.get('language', client.language).strip() if data.get('language') else client.language
        
        # License information
        client.annual_license_fee = data.get('annual_license_fee', client.annual_license_fee).strip() if data.get('annual_license_fee') else client.annual_license_fee
        client.contract_term = data.get('contract_term', client.contract_term).strip() if data.get('contract_term') else client.contract_term
        client.client_type = data.get('client_type', client.client_type).strip() if data.get('client_type') else client.client_type
        client.support_plan = data.get('support_plan', client.support_plan).strip() if data.get('support_plan') else client.support_plan
        next_renewal_date = data.get('next_renewal_date', '').strip() if data.get('next_renewal_date') else ''
        if next_renewal_date:
            try:
                client.next_renewal_date = datetime.datetime.strptime(next_renewal_date, "%Y-%m-%d")
            except ValueError:
                return create_response("Invalid next_renewal_date format. Use YYYY-MM-DD", status=400)

        # Allocation limits
        client.allocated_users = data.get('allocated_users', client.allocated_users)
        client.allocated_packages = data.get('allocated_packages', client.allocated_packages)
        client.allocated_programs = data.get('allocated_programs', client.allocated_programs)
        client.allocated_xperiences = data.get('allocated_xperiences', client.allocated_xperiences)
        client.allocated_quests = data.get('allocated_quests', client.allocated_quests)

        # Settings and status
        client.settings = data.get('settings', client.settings)
        client.is_active = data.get('is_active', client.is_active)

        db_session.commit()
        return create_response("Client updated successfully", data=client.to_dict())

    
    @check_user_permission("super_admin")
    def get(self, id=None):
        db_session = get_db_session()
        client = db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)
        
        data = client.to_dict()
        try:
            data['email_suffix'] = json.loads(data['email_suffix'])
        except Exception as e:
            print(e)
            data['email_suffix'] = [{"domain": "", "sso_id": ""}]
        
        # Ensure AI preferences are present with fallback to global settings
        if data.get('settings'):
            data['settings'] = ensure_ai_preferences(data['settings'], db_session)
        
        return create_response("Client found", data=data)
    

    @check_user_permission("super_admin")
    def delete(self, id):
        db_session = get_db_session()
        client = db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)
        
        db_session.delete(client)
        db_session.commit()
        return create_response("Client deleted")


clients_parser = cms_clients_api.parser()
clients_parser.add_argument('page', type=int, location='args', default=1)
clients_parser.add_argument('limit', type=int, location='args', default=10)
clients_parser.add_argument('search', type=str, location='args', default='')
clients_parser.add_argument('sort', type=str, location='args', default='')


@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/list', methods=['GET'])
class ClientList(Resource):
    @cms_clients_api.expect(clients_parser)
    @check_user_permission("super_admin")
    def get(self):
        args = clients_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        search = args.get('search', '')
        sort = args.get('sort', None)
        offset = (page - 1) * limit

        query = g.db_session.query(Client).filter(Client.id_key != "global")

        if search:
            query = query.filter(Client.name.like(f"%{search}%"))

        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(Client, sort[1:]).desc())
            else:
                query = query.order_by(getattr(Client, sort))
        else:
            query = query.order_by(Client.name)

        clients = query.offset(offset).limit(limit).all()

        total = query.count()

        data = []
        for client in clients:
            item = client.to_dict(["id", "name", "image", "annual_license_fee", "contract_term", "next_renewal_date", "support_plan", "is_active"])
            data.append(item)

        return create_response("Clients found", data=data, total=total, page=page, limit=limit)


@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/<string:id>/statistics', methods=['GET'])
class ClientStatistics(Resource):
    @check_user_permission("super_admin")
    def get(self, id):
        db_session = get_db_session()
        
        ## get the client
        client = db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)
        
        packages_count = client.packages.count() if client.packages else 0
        data = {
            "packages": packages_count
        }

        return create_response("Client statistics", data=data)


@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/at-a-glance', methods=['GET'])
class ClientAtAGlance(Resource):
    @check_user_permission("super_admin")
    def get(self):
        """Get client statistics at a glance"""
        from sqlalchemy import func, case, cast, Float
        db_session = get_db_session()
        
        ## get active clients count
        active_clients = db_session.query(func.count(Client.id)).filter(Client.is_active == True, Client.id_key != 'global').scalar()
        active_sites = db_session.query(func.count(Client.id)).filter(Client.is_active == True).scalar()

        ## get all client using stand support and premium support
        standard_support = db_session.query(func.count(Client.id)).filter(Client.support_plan == 'standard').scalar()
        premium_support = db_session.query(func.count(Client.id)).filter(Client.support_plan == 'premium').scalar()
        
        statistics = {
            'active_clients': active_clients or 0,
            'active_sites': active_sites or 0,
            'annual_revenue': 0,
            'standard_support': standard_support or 0,
            'premium_support': premium_support or 0
        }
        
        return create_response("Client statistics retrieved successfully", data=statistics)


client_package_model = cms_clients_api.model('ClientPackage', {
    'package_ids': fields.List(fields.String, required=True, description='Package IDs')
})

@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/<string:id>/packages', methods=['GET', 'PUT'])
class ClientPackage(Resource):
    def get(self, id):
        client = g.db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)
        
        data = {
            "packages": [package.to_dict(["id", "name", "description", "is_active"]) for package in client.packages]
        }

        return create_response("Client packages found", data=data)

    @cms_clients_api.expect(client_package_model)
    @check_user_permission("super_admin")
    def put(self, id):
        db_session = get_db_session()
        client = db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)

        data = request.json
        package_ids = data.get('package_ids', [])

        packages = []
        for package_id in package_ids:
            package = db_session.query(Package).filter_by(id=package_id).first()
            if package is not None:
                packages.append(package)

        client.packages = packages
        db_session.commit()
        return create_response("Packages assigned successfully")



## sync contents in client
@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/<string:id>/sync', methods=['GET'])
class ClientSync(Resource):
    @check_user_permission("super_admin")
    def get(self, id):
        db_session = get_db_session()
        client = db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)
        
        try:
            publish_database_structure(client.id_key)
            publish_attribute_to_client(client)
            publish_cast_to_client(client)
        except Exception as e:
            return create_response(f"Failed to sync client: {str(e)}", status=500)

        return create_response("Client synced successfully")

client_sync_parser = cms_clients_api.parser()
client_sync_parser.add_argument('client_id', type=str, location='args', required=False)

## api to sync db for all clients
@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/sync-db-all', methods=['GET'])
class SyncDBAllClients(Resource):
    @check_user_permission("super_admin")
    def get(self):
        args = client_sync_parser.parse_args()
        client_id = args.get('client_id', None)
        
        db_session = get_db_session()
        if client_id:
            clients = db_session.query(Client).filter(Client.id == client_id).all()
        else:
            clients = db_session.query(Client).all()

        for client in clients:
            try:
                publish_database_structure(client.id_key)
            except Exception as e:
                return create_response(f"Failed to sync client: {str(e)}", status=500)

        return create_response("All clients synced successfully")
    

## api to sync attribute for all clients
@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/sync-attribute-all', methods=['GET'])
class SyncAttributeAllClients(Resource):
    @check_user_permission("super_admin")
    def get(self):
        db_session = get_db_session()
        clients = db_session.query(Client).all()
        for client in clients:
            try:
                publish_attribute_to_client(client)
                publish_cast_to_client(client)
            except Exception as e:
                return create_response(f"Failed to sync client: {str(e)}", status=500)

        return create_response("All clients synced successfully")    


client_user_sync_parser = cms_clients_api.parser()
client_user_sync_parser.add_argument('client_id', type=str, location='args', required=False)

@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/sync-client-users', methods=['GET'])
class SyncClientUsers(Resource):
    @check_user_permission("super_admin")
    @cms_clients_api.expect(client_user_sync_parser)
    def get(self):
        from api.common.users_utils import sync_user, delete_client_user

        client_id = request.args.get('client_id', None)
        db_session = get_db_session()

        if client_id:
            clients = db_session.query(Client).filter_by(id=client_id).all()
        else:
            clients = db_session.query(Client).all()

        for client in clients:
            try:
                client_db_session = get_db_session(client.id_key)

                ## compare users from db_session user list with user_assignment and add/remove users in client db
                users = client_db_session.query(User).all()
                user_assignments = db_session.query(UserAssignment).filter_by(client_id=client.id).all()

                assigned_user_ids = {ua.user_id for ua in user_assignments}
                existing_user_ids = {user.id for user in users}

                # Add missing users
                for user_id in assigned_user_ids - existing_user_ids:
                    user = db_session.query(User).filter_by(id=user_id).first()
                    if user:
                        sync_user(user, client.id_key)

                # Remove extra users
                for user_id in existing_user_ids - assigned_user_ids:
                    user = client_db_session.query(User).filter_by(id=user_id).first()
                    if user:
                        delete_client_user(user, client.id_key)

                client_db_session.commit()

            except Exception as e:
                return create_response(f"Failed to sync client: {str(e)}", status=500)

        return create_response("All clients synced successfully")


@cms_clients_api.doc(security='bearer')
@cms_clients_api.route('/<string:id>/sso', methods=['GET'])
class ClientSSO(Resource):
    @check_user_permission("super_admin")
    def get(self, id):
        sso_configurations = g.db_session.query(SSOConfiguration).all()
        sso_data = []

        client = g.db_session.query(Client).filter_by(id=id).first()
        if client is None:
            return create_response("Client not found", status=404)
        
        client_id = client.id_key

        for sso in sso_configurations:
            # if "global" in sso.id_key or client_id in sso.id_key:
            if "auth0" in sso.id_key:
                item = sso.to_dict(["id", "name", "is_active", "id_key"])
                sso_data.append(item)
        
        return create_response("Client SSO data", data=sso_data)