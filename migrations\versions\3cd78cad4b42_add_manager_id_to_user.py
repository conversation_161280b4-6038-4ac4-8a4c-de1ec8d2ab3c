"""add manager_id to user

Revision ID: add_manager_id_001
Revises: 3b2d2fd84832
Create Date: 2025-01-23 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3cd78cad4b42'
down_revision = '3b2d2fd84832'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('manager_id', sa.String(length=36), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('manager_id')

    # ### end Alembic commands ###