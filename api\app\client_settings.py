import copy
from flask import g
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from clientmodels import Client

app_client_settings_api = Namespace('api_app_client_settings', description='Client settings operations for app')

@app_client_settings_api.doc(security='bearer')
@app_client_settings_api.route('/settings')
class ClientSettings(Resource):
    def get(self):
        """Get current client settings for the authenticated user"""
        try:
            # Get the client based on the tenant ID from the JWT token
            client = g.db_session.query(Client).filter(Client.id_key == g.tenant_id).first()
            
            if not client:
                return create_response("Client not found", status=404)
            
            # Get client settings
            client_settings = client.settings if client.settings else {}
            
            # Check if AI preferences are missing and get global fallback if needed
            client_settings = self._ensure_ai_preferences(client_settings)
            
            # Return client information
            data = {
                'client_id': client.id,
                'client_name': client.name if client.name else "",
                'client_settings': client_settings
            }
            
            return create_response("Client settings retrieved successfully", data=data)
            
        except Exception as e:
            return create_response(f"Failed to retrieve client settings: {str(e)}", status=500)
    
    def _ensure_ai_preferences(self, client_settings):
        """Ensure AI preferences are present, fallback to global settings if missing"""
        try:
            # Check if AI preferences exist and have all required fields
            ai_config = client_settings.get('feature_configuration', {}).get('ai', {})
            ai_preferences = ai_config.get('preferences', {})
            
            required_fields = ['agreement_title', 'agreement_terms_body', 'checkbox_label', 'button_label', 'version']
            missing_fields = [field for field in required_fields if not ai_preferences.get(field)]
            
            if missing_fields:
                # Get global client settings as fallback
                global_client = g.db_session.query(Client).filter(Client.id_key == "global").first()
                if global_client and global_client.settings:
                    global_ai_config = global_client.settings.get('feature_configuration', {}).get('ai', {})
                    global_ai_preferences = global_ai_config.get('preferences', {})
                    
                    # Create a deep copy of client settings to avoid modifying the original
                    updated_settings = copy.deepcopy(client_settings)
                    
                    # Ensure the structure exists
                    if 'feature_configuration' not in updated_settings:
                        updated_settings['feature_configuration'] = {}
                    if 'ai' not in updated_settings['feature_configuration']:
                        updated_settings['feature_configuration']['ai'] = {}
                    if 'preferences' not in updated_settings['feature_configuration']['ai']:
                        updated_settings['feature_configuration']['ai']['preferences'] = {}
                    
                    # Fill in missing fields from global settings
                    for field in missing_fields:
                        if global_ai_preferences.get(field):
                            updated_settings['feature_configuration']['ai']['preferences'][field] = global_ai_preferences[field]
                    
                    return updated_settings
            
            return client_settings
            
        except Exception as e:
            # If there's any error in fallback logic, return original settings
            return client_settings