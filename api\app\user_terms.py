import copy
from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from clientmodels import Client, User, UserAgreement

app_user_terms_api = Namespace('api_app_user_terms', description='User terms and agreements operations')

# Define the request model for recording agreement
agreement_request_model = app_user_terms_api.model('AgreementRequest', {
    'agreement_type': fields.String(required=True, description='Type of agreement (e.g., AI)')
})

@app_user_terms_api.doc(security='bearer')
@app_user_terms_api.route('/terms/<string:terms_type>')
class UserTerms(Resource):
    def get(self, terms_type):
        """Get current terms status for the authenticated user"""
        try:
            # Get the client based on the tenant ID from the JWT token
            client = g.db_session.query(Client).filter(Client.id_key == g.tenant_id).first()
            
            if not client:
                return create_response("Client not found", status=404)
            
            # Get client settings with fallback to global
            client_settings = self._get_client_settings_with_fallback(client, terms_type.lower())
            
            if not client_settings:
                return create_response(f"Settings for {terms_type} not found", status=404)
            
            # Extract preferences for the specific terms type
            preferences = self._extract_preferences(client_settings, terms_type.lower())
            
            if not preferences:
                return create_response(f"Preferences for {terms_type} not found", status=404)
            
            # Check if user has agreed to current version
            user_agreement_status = self._check_user_agreement_status(g.user_id, terms_type, preferences.get('version'))
            
            # Return terms information
            data = {
                'agreement_type': terms_type,
                'agreement_title': preferences.get('agreement_title', ''),
                'agreement_terms_body': preferences.get('agreement_terms_body', ''),
                'checkbox_label': preferences.get('checkbox_label', ''),
                'button_label': preferences.get('button_label', ''),
                'user_agreement_status': user_agreement_status
            }
            
            return create_response("Terms retrieved successfully", data=data)
            
        except Exception as e:
            return create_response(f"Failed to retrieve terms: {str(e)}", status=500)
    
    def _get_client_settings_with_fallback(self, client, terms_type):
        """Get client settings with fallback to global client"""
        client_settings = client.settings if client.settings else {}
        
        # Check if the specific terms type preferences exist and have all required fields
        terms_config = client_settings.get('feature_configuration', {}).get(terms_type, {})
        terms_preferences = terms_config.get('preferences', {})
        
        required_fields = ['agreement_title', 'agreement_terms_body', 'checkbox_label', 'button_label', 'version']
        missing_fields = [field for field in required_fields if not terms_preferences.get(field)]
        
        if missing_fields:
            # Get global client settings as fallback
            global_client = g.db_session.query(Client).filter(Client.id_key == "global").first()
            if global_client and global_client.settings:
                global_settings = copy.deepcopy(client_settings)
                global_terms_config = global_client.settings.get('feature_configuration', {}).get(terms_type, {})
                global_terms_preferences = global_terms_config.get('preferences', {})
                
                # Ensure the structure exists
                if 'feature_configuration' not in global_settings:
                    global_settings['feature_configuration'] = {}
                if terms_type not in global_settings['feature_configuration']:
                    global_settings['feature_configuration'][terms_type] = {}
                if 'preferences' not in global_settings['feature_configuration'][terms_type]:
                    global_settings['feature_configuration'][terms_type]['preferences'] = {}
                
                # Fill in missing fields from global settings
                for field in missing_fields:
                    if global_terms_preferences.get(field):
                        global_settings['feature_configuration'][terms_type]['preferences'][field] = global_terms_preferences[field]
                
                return global_settings
        
        return client_settings
    
    def _extract_preferences(self, client_settings, terms_type):
        """Extract preferences for the specific terms type"""
        return client_settings.get('feature_configuration', {}).get(terms_type, {}).get('preferences', {})
    
    def _check_user_agreement_status(self, user_id, terms_type, version):
        """Check if user has agreed to the current version"""
        if not version:
            return False
            
        agreement = g.db_session.query(UserAgreement).filter(
            UserAgreement.user_id == user_id,
            UserAgreement.terms_type == terms_type,
            UserAgreement.terms_version == version,
            UserAgreement.is_deleted == False
        ).first()
        
        return agreement is not None


@app_user_terms_api.doc(security='bearer')
@app_user_terms_api.route('/agreement')
class UserAgreementRecord(Resource):
    @app_user_terms_api.expect(agreement_request_model)
    def post(self):
        """Record user agreement to terms"""
        try:
            data = request.get_json()
            terms_type = data.get('agreement_type')
            
            if not terms_type:
                return create_response("agreement_type is required", status=400)
            
            # Get the client based on the tenant ID from the JWT token
            client = g.db_session.query(Client).filter(Client.id_key == g.tenant_id).first()
            
            if not client:
                return create_response("Client not found", status=404)
            
            # Get client settings with fallback to global
            user_terms_resource = UserTerms()
            client_settings = user_terms_resource._get_client_settings_with_fallback(client, terms_type.lower())
            
            if not client_settings:
                return create_response(f"Settings for {terms_type} not found", status=404)
            
            # Extract preferences for the specific terms type
            preferences = user_terms_resource._extract_preferences(client_settings, terms_type.lower())
            
            if not preferences:
                return create_response(f"Preferences for {terms_type} not found", status=404)
            
            version = preferences.get('version')
            if not version:
                return create_response("Version not found in preferences", status=404)
            
            # Check if user already has an agreement for this version
            existing_agreement = g.db_session.query(UserAgreement).filter(
                UserAgreement.user_id == g.user_id,
                UserAgreement.terms_type == terms_type,
                UserAgreement.terms_version == version,
                UserAgreement.is_deleted == False
            ).first()
            
            if existing_agreement:
                return create_response("User has already agreed to this version", data={'agreement_id': existing_agreement.id})
            
            # Create new agreement record
            agreement = UserAgreement(
                user_id=g.user_id,
                client_id=g.tenant_id if g.tenant_id != "global" else None,
                terms_type=terms_type,
                terms_version=version
            )
            
            g.db_session.add(agreement)
            g.db_session.commit()
            
            return create_response("Agreement recorded successfully", data={'agreement_id': agreement.id})
            
        except Exception as e:
            g.db_session.rollback()
            return create_response(f"Failed to record agreement: {str(e)}", status=500)