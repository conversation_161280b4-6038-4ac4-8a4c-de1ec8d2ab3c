import datetime
import logging

from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from api.common.task import TaskFunction
from clientmodels import Task
from clientmodels import get_db_session
from services.azure_queue import AzureQueue

logger = logging.getLogger(__name__)

task_execution_api = Namespace('task_execution_api', description='Task execution operations')

@task_execution_api.route('execute', methods=['PUT'])
class TaskExecutionObject(Resource):
    def put(self):
        """
        Process a task task from the queue.
        Returns 200 for successful processing or when task should be removed from queue.
        Returns 400 for invalid requests and 500 for processing errors.
        """
        try:
            # 1. Validate request
            data = request.json
            queue_id = request.headers.get('QueueId')

            if not queue_id:
                logger.error("No QueueId header found in request headers")
                return create_response("QueueId header is required", status=400)

            logger.error(f"api/task/task received header: {queue_id}")

            if not data:
                logger.error("No data found in request body")
                return create_response("No data found in request body", status=400)
            
            logger.error(f"api/task/task received data: {data}")

            # 2. Setup database session
            task_id = data.get('task_id')
            db_session = get_db_session('global')
            g.db_session = db_session

            # 3. Get task
            task = g.db_session.query(Task).filter(
                Task.id == task_id
            ).first()

            # 4. Handle task states
            # Case 1: Task doesn't exist
            if not task:
                logger.error(f"Task not found - task does not exist: {task_id}")
                return create_response("Task not found - task does not exist", status=200)
            
            # Case 2: Task already sent or failed
            if task.status == 'succeeded':
                logger.error(f"Task already execute successfully - task execute successfully: {task_id}")
                return create_response("Task already execute successfully", status=200)
            
            if task.status == 'failed':
                logger.error(f"Task previously failed - task previously failed: {task_id}")
                return create_response("Task previously failed", status=200)
            
            # Case 3: Task is deleted but still in queue
            if task.is_deleted and task.queue_id is not None:
                queue = AzureQueue()
                queue.delete_message_by_id("task-queue", task.queue_id)
                logger.error(f"Task not found - task was deleted: {task_id}")
                return create_response("Task not found - task was deleted", status=200)

            # Case 4: Queue message ID mismatch
            if task.queue_id != queue_id:
                queue = AzureQueue()
                queue.delete_message_by_id("task-queue", queue_id)
                logger.error(f"Task not found - queue ID mismatch: {task_id}")
                return create_response("Task not found - queue ID mismatch", status=200)

            # 5. Process valid task
            tf = TaskFunction()
            result = tf.execute_task(task_id)

            if not result:
                task.status = 'failed'
                g.db_session.commit()
                logger.error(f"Failed to execute task: {task_id}")
                return create_response("Failed to execute task", status=200)
            else:
                task.status = 'succeeded'
                g.db_session.commit()
                logger.info(f"Task executed successfully: {task_id}")
                return create_response("Task executed successfully", status=200)

        except Exception as e:
            logger.error(f"Error processing task task: {str(e)}")
            return create_response(f"Execute task failed: {str(e)}", 500)


task_model = task_execution_api.model('task', {
    'name': fields.String(required=True),
    'func': fields.String(required=True),
    'data': fields.Raw(required=True),
    'scheduled_for': fields.String(required=True),
})

@task_execution_api.route('', methods=['POST'])
@task_execution_api.route('<string:id>', methods=['GET', 'PUT', 'DELETE'])
class TaskObject(Resource):
    def get(self, id=None):
        """
        Get a task
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        task = g.db_session.query(Task).filter(Task.id == id, Task.is_deleted == False).first()
        if not task:
            return create_response("Task not found", status=404)

        return create_response("Task retrieved successfully", status=200, data=task.to_dict())

    @task_execution_api.expect(task_model)
    def post(self):
        """
        Create a task
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        data = request.json
        name = data.get('name')
        func = data.get('func')
        task_data = data.get('data')
        scheduled_for = datetime.datetime.strptime(data.get('scheduled_for'), '%Y-%m-%d %H:%M:%SZ') if data.get('scheduled_for', '') else None
        
        if not scheduled_for:
            return create_response("Scheduled for is required", status=400)

        tf = TaskFunction()
        task = tf.create_task(name, func, task_data, scheduled_for)
        return create_response("Task created successfully", status=200, data=task.to_dict())

    @task_execution_api.expect(task_model)
    def put(self, id):
        """
        Update a task
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        data = request.json
        name = data.get('name')
        func = data.get('func')
        task_data = data.get('data')
        scheduled_for = datetime.datetime.strptime(data.get('scheduled_for'), '%Y-%m-%d %H:%M:%SZ') if data.get('scheduled_for', '') else None

        tf = TaskFunction()
        task = tf.update_task(id, name, func, task_data, scheduled_for)
        return create_response("Task updated successfully", status=200, data=task)

    def delete(self, id):
        """
        Delete a task
        """
        db_session = get_db_session("global")
        g.db_session = db_session

        tf = TaskFunction()
        tf.delete_task(id)
        return create_response("Task deleted successfully", status=200)
