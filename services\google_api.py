from flask import session
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build


class GoogleDocsService:
    def __init__(self):
        self.credentials = Credentials(**session['credentials'])
        # Check if the credentials are expired and refresh if necessary
        if self.credentials.expired and self.credentials.refresh_token:
            self.credentials.refresh(Request())
            # Update the session with the new credentials
            session['credentials'] = self.credentials_to_dict(self.credentials)

        self.service = build('docs', 'v1', credentials=self.credentials)
        print("Service created")

    def get_document(self, document_id):
        try:
            document = self.service.documents().get(documentId=document_id).execute()
            print("Document retrieved")
            return document
        except Exception as e:
            print(e)
            return None



