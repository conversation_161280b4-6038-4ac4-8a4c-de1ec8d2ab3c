import datetime
from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from clientmodels import Node, NodeOption, NodeTranscript, NodeBranch, NodeOptionMatching, Quest, UserNode, \
    UserNodeHistory

cms_nodes_api = Namespace('api_cms_nodes', description='Nodes related operations')

node_model = cms_nodes_api.model('Node', {
    'quest_id': fields.String(required=True, description='The quest identifier'),
    'name': fields.String(required=False, description='The node name'),
    'screen_type': fields.String(required=True, description='The node screen type'),
    'quest_type': fields.String(required=False, description='The quest type', default='', enum=['', 'quiz']),
    'title': fields.String(required=False, description='The node title', default=''),
    'sub_title': fields.String(required=False, description='The node sub title', default=''),
    'points': fields.Integer(required=False, description='The node points', default=0),
    'next_node_id': fields.String(required=False, description='The next node identifier', default=''),
    'transcripts': fields.List(fields.Nested(cms_nodes_api.model('NodeTranscripts', {
        'character_id': fields.String(required=True, description='The character identifier', default=''),
        'animation': fields.String(required=True, description='The animation key', default=''),
        'show_icon': fields.Boolean(required=False, description='Show the character icon', default=False),
        'text': fields.String(required=True, description='The transcript text', default=''),
        'audio_en': fields.String(required=False, description='The audio file path', default=''),
        'duration': fields.Integer(required=False, description='The duration of the transcript', default=0),
    }))),
    'branches_from_ids': fields.List(fields.String, required=False, description='The list of node IDs from which branches originate', default=[]),
    'branches': fields.List(fields.Nested(cms_nodes_api.model('NodeBranches', {
        'condition': fields.String(required=True, description='The condition, eq, gt, lt, between, most, least', default='eq'),
        'condition_value': fields.String(required=True, description='The value', default=''),
        'next_node_id': fields.String(required=True, description='The next node identifier', default=''),
    }))),
    'quest_character_id': fields.String(required=False, description='The node question character identifier', default=''),
    'quest_character_animation': fields.String(required=False, description='The node question character animation', default=''),
    'quest_character_audio': fields.String(required=False, description='The node question character audio', default=''),
    'quest_text': fields.String(required=False, description='The node question text', default=''),
    'answer_placeholder': fields.String(required=False, description='The node answer default placeholder', default=''),
    'button_text': fields.String(required=False, description='The node button text', default='Continue'),
    'previous_node_id': fields.String(required=False, description='The previous node identifier', default=''),
})

@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/', methods=['POST'])
@cms_nodes_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class NodeItem(Resource):
    @cms_nodes_api.expect(node_model)
    def post(self):
        '''Create a new node'''
        data = request.json
        
        quest_id = data.get('quest_id', '')
        name = data.get('name', '')
        screen_type = data.get('screen_type', '')
        title = data.get('title', '')
        sub_title = data.get('sub_title', '')
        points = data.get('points', 0)
        next_node_id = data.get('next_node_id', '')
        transcripts = data.get('transcripts', [])
        branches_from_ids = data.get('branches_from_ids', [])
        branches = data.get('branches', [])
        quest_character_id = data.get('quest_character_id', '')
        quest_character_animation = data.get('quest_character_animation', '')
        quest_character_audio = data.get('quest_character_audio', '')
        quest_text = data.get('quest_text', '')
        answer_placeholder = data.get('answer_placeholder', '')
        button_text = data.get('button_text', 'Continue')
        quest_type = data.get('quest_type', '')
        

        node = Node(
            quest_id=quest_id,
            name=name,
            screen_type=screen_type,
            title=title,
            sub_title=sub_title,
            points=points,
            quest_character_id=quest_character_id,
            quest_character_animation=quest_character_animation,
            quest_character_audio=quest_character_audio,
            quest_text=quest_text,
            answer_placeholder=answer_placeholder,
            button_text=button_text,
            quest_type=quest_type
        )

        if next_node_id:
            node.next_node_id = next_node_id

        ## save the branches from ids
        branches_from_ids = data.get('branches_from_ids', [])
        node.branches_from_ids = json.dumps(branches_from_ids)

        g.client_db_session.add(node)
        g.client_db_session.commit()

        ## save the transcripts

        default_show_icon = False
        ## if screen_type is banter, show_icon should be default to true
        if screen_type == 'banter':
            default_show_icon = True

        for transcript in transcripts:
            character_id = transcript.get('character_id', '')
            animation = transcript.get('animation', '')
            show_icon = transcript.get('show_icon', default_show_icon)
            text = transcript.get('text', '')
            audio_en = transcript.get('audio_en', '')
            duration = transcript.get('duration', 0)

            if text or node.screen_type == 'intro_transition':
                transcript = NodeTranscript(
                    node_id=node.id,
                    character_id=character_id,
                    animation=animation,
                    show_icon=show_icon,
                    text=text,
                    audio_en=audio_en,
                    duration=duration
                )

                g.client_db_session.add(transcript)
    
        ## save the branches
        for branch in branches:
            condition = branch.get('condition', '')
            value = branch.get('value', '')
            next_node_id = branch.get('next_node_id', '')

            branch = NodeBranch(
                node_id=node.id,
                condition=condition,
                value=value,
                next_node_id=next_node_id
            )

            g.client_db_session.add(branch)

        ## if previous node id is provided, save the next node id for the previous node
        previous_node_id = data.get('previous_node_id', '')
        if previous_node_id:
            previous_node = g.client_db_session.query(Node).filter(Node.id == previous_node_id).first()
            if previous_node:
                previous_node.next_node_id = node.id
                g.client_db_session.add(previous_node)


        g.client_db_session.commit()

        ## update the quest last modified date
        quest = g.client_db_session.query(Quest).filter(Quest.id == quest_id).first()
        if quest:
            quest.date_updated = datetime.datetime.utcnow()
            g.client_db_session.add(quest)
            g.client_db_session.commit()

        data = {
            'id': node.id
        }

        return create_response('Node created successfully', data=data)
            

    def get(self, id):
        '''Get a node'''
        node = g.client_db_session.query(Node).filter(Node.id == id).first()
        if not node:
            return create_response('Node not found', 404)
        
        data = node.to_dict()

        ## get the transcripts
        transcripts = g.client_db_session.query(NodeTranscript).filter(NodeTranscript.node_id == node.id).all()
        data['transcripts'] = [transcript.to_dict() for transcript in transcripts]

        ## get the branches
        branches = g.client_db_session.query(NodeBranch).filter(NodeBranch.node_id == node.id).all()
        data['branches'] = [branch.to_dict() for branch in branches]

        ## get node options
        node_options = node.options
        if node_options:
            data['options'] = [option.to_dict() for option in node_options]
            for option in data['options']:
                ## get the matching options
                matching_options = g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_option_id == option['id']).all()
                option['matching_option_ids'] = [matching_option.matching_node_option_id for matching_option in matching_options]
        else:
            data['options'] = []

        return create_response('Node found', data=data)
    

    @cms_nodes_api.expect(node_model)
    def put(self, id):
        '''Update a node'''
        node = g.client_db_session.query(Node).filter(Node.id == id).first()
        if not node:
            return create_response('Node not found', 404)
        
        data = request.json
        quest_id = data.get('quest_id', '')
        name = data.get('name', '')
        screen_type = data.get('screen_type', '')
        title = data.get('title', '')
        sub_title = data.get('sub_title', '')
        points = data.get('points', 0)
        next_node_id = data.get('next_node_id', '')
        transcripts = data.get('transcripts', [])
        branches_from_ids = data.get('branches_from_ids', [])
        branches = data.get('branches', [])
        quest_character_id = data.get('quest_character_id', '')
        quest_character_animation = data.get('quest_character_animation', '')
        quest_character_audio = data.get('quest_character_audio', '')
        quest_text = data.get('quest_text', '')
        answer_placeholder = data.get('answer_placeholder', '')
        button_text = data.get('button_text', 'Continue')
        quest_type = data.get('quest_type', '')

        # node.quest_id = quest_id
        node.name = name
        # node.screen_type = screen_type
        node.title = title
        node.sub_title = sub_title
        node.points = points
        node.quest_character_id = quest_character_id
        node.quest_character_animation = quest_character_animation
        node.quest_character_audio = quest_character_audio
        node.quest_text = quest_text
        node.answer_placeholder = answer_placeholder
        node.button_text = button_text
        node.quest_type = quest_type

        if next_node_id:
            node.next_node_id = next_node_id
        else:
            node.next_node_id = None

        ## save the branches from ids
        node.branches_from_ids = json.dumps(branches_from_ids)

        ## remove the existing transcripts
        g.client_db_session.query(NodeTranscript).filter(NodeTranscript.node_id == node.id).delete()

        ## save the transcripts
        default_show_icon = False
        ## if screen_type is banter, show_icon should be default to true
        if node.screen_type == 'banter':
            default_show_icon = True

        for transcript in transcripts:
            character_id = transcript.get('character_id', '')
            animation = transcript.get('animation', '')
            show_icon = transcript.get('show_icon', default_show_icon)
            text = transcript.get('text', '')
            audio_en = transcript.get('audio_en', '')
            duration = transcript.get('duration', 0)

            if text or node.screen_type == 'intro_transition':
                transcript = NodeTranscript(
                    node_id=node.id,
                    character_id=character_id,
                    animation=animation,
                    show_icon=show_icon,
                    text=text,
                    audio_en=audio_en,
                    duration=duration
                )

                g.client_db_session.add(transcript)

        ## remove the existing branches
        g.client_db_session.query(NodeBranch).filter(NodeBranch.node_id == node.id).delete()

        ## save the branches
        for branch in branches:
            condition = branch.get('condition', '')
            condition_value = branch.get('condition_value', '')
            next_node_id = branch.get('next_node_id', '')

            branch = NodeBranch(
                node_id=node.id,
                condition=condition,
                condition_value=condition_value,
                next_node_id=next_node_id
            )

            g.client_db_session.add(branch)

        g.client_db_session.commit()

        ## if node is edited, also update the last modified date of that quest
        quest = g.client_db_session.query(Quest).filter(Quest.id == node.quest_id).first()
        if quest:
            quest.date_updated = datetime.datetime.utcnow()
            g.client_db_session.add(quest)
            g.client_db_session.commit()

        return create_response('Node updated successfully')
    
    def delete(self, id):
        '''Delete a node'''
        node = g.client_db_session.query(Node).filter(Node.id == id).first()
        if not node:
            return create_response('Node not found', 404)
        
        ## remove the node transcripts
        g.client_db_session.query(NodeTranscript).filter(NodeTranscript.node_id == node.id).delete()

        ## remove the node branches
        g.client_db_session.query(NodeBranch).filter(NodeBranch.node_id == node.id).delete()

        ## remove the node options matchings
        g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_id == node.id).delete()

        ## remove the node options
        g.client_db_session.query(NodeOption).filter(NodeOption.node_id == node.id).delete()

        ## remove all next_node_id with this node to null
        related_nodes = g.client_db_session.query(Node).filter(Node.next_node_id == node.id).all()
        for related_node in related_nodes:
            related_node.next_node_id = None
            g.client_db_session.add(related_node)

        g.client_db_session.commit()

        ## remove all the branches from this node
        g.client_db_session.query(NodeBranch).filter(NodeBranch.next_node_id == node.id).delete()

        ## remove all the user node records
        g.client_db_session.query(UserNode).filter(UserNode.node_id == node.id).delete()

        ## remove all the user node history records
        g.client_db_session.query(UserNodeHistory).filter(UserNodeHistory.node_id == node.id).delete()

        related_user_nodes = g.client_db_session.query(UserNode).filter(UserNode.next_node_id == node.id).all()
        for related_user_node in related_user_nodes:
            related_user_node.next_node_id = None
            g.client_db_session.add(related_user_node)

        related_user_node_histories = g.client_db_session.query(UserNodeHistory).filter(UserNodeHistory.next_node_id == node.id).all()
        for related_user_node_history in related_user_node_histories:
            related_user_node_history.next_node_id = None
            g.client_db_session.add(related_user_node_history)

        related_previous_nodes = g.client_db_session.query(Node).filter(Node.next_node_id == node.id).all()
        for related_previous_node in related_previous_nodes:
            related_previous_node.next_node_id = None
            g.client_db_session.add(related_previous_node)

        related_prevous_node_histories = g.client_db_session.query(UserNodeHistory).filter(UserNodeHistory.next_node_id == node.id).all()
        for related_prevous_node_history in related_prevous_node_histories:
            related_prevous_node_history.next_node_id = None
            g.client_db_session.add(related_prevous_node_history)
        
        g.client_db_session.delete(node)
        g.client_db_session.commit()

        return create_response('Node deleted successfully')
    

new_node_model = cms_nodes_api.model('NewNode', {
    'quest_id': fields.String(required=True, description='The quest identifier'),
    'screen_type': fields.String(required=True, description='The node screen type'),
    'previous_node_id': fields.String(required=False, description='The previous node identifier', default=''),
})

@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/create', methods=['POST'])
class CreateNodeItem(Resource):
    @cms_nodes_api.expect(new_node_model)
    def post(self):
        '''Create a new node'''
        data = request.json
        quest_id = data.get('quest_id', '')
        screen_type = data.get('screen_type', '')

        if not quest_id or not screen_type:
            return create_response('Invalid request data', 400)
        
        node = Node(quest_id=quest_id, screen_type=screen_type)
        g.client_db_session.add(node)

        ## if previous node id is provided, save the next node id for the previous node
        previous_node_id = data.get('previous_node_id', '')
        if previous_node_id:
            previous_node = g.client_db_session.query(Node).filter(Node.id == previous_node_id).first()
            if previous_node:
                previous_node.next_node_id = node.id
                g.client_db_session.add(previous_node)

        g.client_db_session.commit()
        

        data = {
            'id': node.id
        }

        return create_response('Node created successfully', data=data)
    



node_option_model = cms_nodes_api.model('NodeOption', {
    'node_id': fields.String(required=True, description='The node identifier'),
    'label': fields.String(required=True, description='The option label'),
    'value': fields.String(required=False, description='The option value'),
    'position': fields.String(required=False, description='The option position for matching or slider screen, left or right.', default=''),
    'is_correct': fields.Boolean(required=False, description='The option is correct', default=False),
    'index': fields.Integer(required=False, description='The option index, for sorting order'),
    'next_node_id': fields.String(required=False, description='The next node identifier'),
    'points': fields.Integer(required=False, description='The option points', default=0),
    'matching_option_ids': fields.List(fields.String, required=False, description='The matching option identifiers'),
    'feedback': fields.Nested(cms_nodes_api.model('Feedback', {
        'correct_feedback': fields.String(required=False, description='The feedback for correct answer', default=''),
        'incorrect_feedback': fields.String(required=False, description='The feedback for incorrect answer', default=''),
    }))
})

@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/option', methods=['POST'])
@cms_nodes_api.route('/option/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class NodeOptionItem(Resource):
    @cms_nodes_api.expect(node_option_model)
    def post(self):
        '''Create a new node option'''
        data = request.json
        node_id = data.get('node_id', '')
        if not node_id:
            return create_response('Invalid request data', 400)
        
        label = data.get('label', '')
        value = data.get('value', '')

        position = data.get('position', '')
        is_correct = data.get('is_correct', False)
        index = data.get('index', 0)
        matching_option_ids = data.get('matching_option_ids', [])
        next_node_id = data.get('next_node_id', '')
        points = data.get('points', 0)
        feedback = data.get('feedback', {})

        option = NodeOption(node_id=node_id, label=label, value=value, position=position, is_correct=is_correct, index=index, points=points)

        if next_node_id:
            option.next_node_id = next_node_id

        if feedback:
            option.feedback = json.dumps(feedback)

        g.client_db_session.add(option)
        g.client_db_session.commit()

        ## save the matching options
        for matching_option_id in matching_option_ids:
            matching_option = NodeOptionMatching(node_id=option.node_id, node_option_id=option.id, matching_node_option_id=matching_option_id)
            g.client_db_session.add(matching_option)

        g.client_db_session.commit()

        data = {
            'id': option.id
        }

        return create_response('Node option created successfully', data=data)
    
    def get(self, id):
        '''Get a node option'''
        option = g.client_db_session.query(NodeOption).filter(NodeOption.id == id).first()
        if not option:
            return create_response('Node option not found', 404)
        
        data = option.to_dict()
        ## get the matching options
        matching_options = g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_option_id == option.id).all()
        data['matching_option_ids'] = [matching_option.matching_node_option_id for matching_option in matching_options]
        return create_response('Node option found', data=data)
    

    @cms_nodes_api.expect(node_option_model)
    def put(self, id):
        '''Update a node option'''
        option = g.client_db_session.query(NodeOption).filter(NodeOption.id == id).first()
        if not option:
            return create_response('Node option not found', 404)
        
        data = request.json
        label = data.get('label', '')
        value = data.get('value', '')
        position = data.get('position', '')
        is_correct = data.get('is_correct', False)
        index = data.get('index', 0)
        matching_option_ids = data.get('matching_option_ids', '')
        next_node_id = data.get('next_node_id', '')
        points = data.get('points', 0)
        feedback = data.get('feedback', {})

        option.label = label
        option.value = value
        option.position = position
        option.is_correct = is_correct
        option.index = index
        option.points = points

        if next_node_id:
            option.next_node_id = next_node_id
        else:
            option.next_node_id = None

        if feedback:
            option.feedback = json.dumps(feedback)

        g.client_db_session.commit()

        ## remove the existing matching options
        g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_option_id == option.id).delete()

        ## save the matching options
        for matching_option_id in matching_option_ids:
            matching_option = NodeOptionMatching(node_id=option.node_id, node_option_id=option.id, matching_node_option_id=matching_option_id)
            g.client_db_session.add(matching_option)

        g.client_db_session.commit()

        return create_response('Node option updated successfully')
    
    def delete(self, id):
        '''Delete a node option'''
        option = g.client_db_session.query(NodeOption).filter(NodeOption.id == id).first()
        if not option:
            return create_response('Node option not found', 404)
        
        ## remove the matching options
        g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_option_id == option.id).delete()
        
        g.client_db_session.delete(option)
        g.client_db_session.commit()

        return create_response('Node option deleted successfully')

node_option_order_model = cms_nodes_api.model('NodeOptionOrder', {
    'id': fields.String(required=True, description='The node option identifier'),
    'order': fields.Integer(required=True, description='The node option order')
})

node_option_orders_model = cms_nodes_api.model('NodeOptionOrders', {
    'options': fields.List(fields.Nested(node_option_order_model), required=True, description='The list of node options with their order'),
    'node_id': fields.String(required=True, description='The node identifier')
})

@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/option/order', methods=['PATCH'])
class NodeOptionOrder(Resource):
    @cms_nodes_api.expect(node_option_orders_model)
    def patch(self):
        '''Update node option order'''
        data = request.json
        options = data.get('options', [])

        if not options:
            return create_response('Invalid request data', 400)

        for option_data in options:
            option_id = option_data.get('id', '')
            order = option_data.get('order', 0)

            option = g.client_db_session.query(NodeOption).filter(NodeOption.id == option_id).first()
            if option:
                option.index = order
                g.client_db_session.add(option)

        g.client_db_session.commit()

        ## return all node optiosn and their order
        options = g.client_db_session.query(NodeOption).filter(NodeOption.node_id == data.get('node_id', '')).all()
        data = []
        for option in options:
            data.append({
                'id': option.id,
                'order': option.index
            })

        return create_response('Node options order updated successfully', data=data)


## get node options by node id
node_options_parser = cms_nodes_api.parser()
node_options_parser.add_argument('node_id', type=str, required=True, help='The node identifier')

@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/options', methods=['GET'])
class NodeOptions(Resource):
    @cms_nodes_api.expect(node_options_parser)
    def get(self):
        '''Get the node options'''
        args = node_options_parser.parse_args()
        node_id = args.get('node_id', '')

        options = g.client_db_session.query(NodeOption).filter(NodeOption.node_id == node_id).all()
        data = []
        for option in options:
            option_data = option.to_dict()
            ## get the matching options
            matching_options = g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_option_id == option.id).all()
            option_data['matching_option_ids'] = [matching_option.matching_node_option_id for matching_option in matching_options]
            data.append(option_data)

        return create_response('Node options found', data=data)


## get node list by quest id
node_list_parser = cms_nodes_api.parser()
node_list_parser.add_argument('quest_id', type=str, required=True, help='The quest identifier')
## add pagination
node_list_parser.add_argument('page', type=int, required=False, default=1, help='The page number')
node_list_parser.add_argument('limit', type=int, required=False, default=100, help='The page limit')
## add search
node_list_parser.add_argument('search', type=str, required=False, help='The search keyword')

@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/list', methods=['GET'])
class NodeList(Resource):
    @cms_nodes_api.expect(node_list_parser)
    def get(self):
        '''Get the nodes list'''
        args = node_list_parser.parse_args()
        quest_id = args.get('quest_id', '')
        page = args.get('page', 1)
        limit = args.get('limit', 100)
        search = args.get('search', '')

        query = g.client_db_session.query(Node).filter(Node.quest_id == quest_id)
        if search:
            query = query.filter(Node.name.ilike(f'%{search}%'))

        total = query.count()
        data = []
        nodes = query.order_by(Node.date_created).offset((page - 1) * limit).limit(limit).all()
        for node in nodes:
            node_dict = node.to_dict()

            ## get node transcripts
            character_ids = []
            node_transcripts = node.transcripts
            if node_transcripts:
                node_dict['transcripts'] = [transcript.to_dict() for transcript in node_transcripts]
            else:
                node_dict['transcripts'] = []

            ## get node options
            node_options = node.options
            if node_options:
                node_dict['options'] = [option.to_dict() for option in node_options]
            else:
                node_dict['options'] = []

            ## get node branches
            node_branches = node.branches
            if node_branches:
                node_dict['branches'] = [branch.to_dict() for branch in node_branches]
            else:
                node_dict['branches'] = []

            data.append(node_dict)

        return create_response('Nodes found', data=data, total=total, page=page, limit=limit)






node_quest_model = cms_nodes_api.model('NodeQuest', {
    'node_ids': fields.List(fields.String, required=True, description='The list of node identifiers')
})

@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/quests', methods=['PATCH'])
class GetNodeQuest(Resource):
    @cms_nodes_api.expect(node_quest_model)
    def patch(self):
        '''Get quests by node ids'''
        data = request.json
        node_ids = data.get('node_ids', [])

        if not node_ids:
            return create_response('Invalid request data', 400)

        nodes = g.client_db_session.query(Node).filter(Node.id.in_(node_ids)).all()
        if not nodes:
            return create_response('Nodes not found', 404)

        quest_ids = list(set(node.quest_id for node in nodes))
        quests = g.client_db_session.query(Quest).filter(Quest.id.in_(quest_ids)).all()

        quests = [quest.to_dict(["id", "name"]) for quest in quests]
        for quest in quests:
            quest['node_ids'] = [node.id for node in nodes if node.quest_id == quest['id']]

        return create_response('Quests found', data={'quests': quests})



## copy node
copy_node_model = cms_nodes_api.model('CopyNode', {
    'name': fields.String(required=False, description='The new node name'),
    'previous_node_id': fields.String(required=False, description='The previous node identifier', default=''),
})
@cms_nodes_api.doc(security='bearer')
@cms_nodes_api.route('/copy/<string:id>', methods=['POST'])
class CopyNode(Resource):
    @cms_nodes_api.expect(copy_node_model)
    def post(self, id):
        '''Copy a node'''
        node = g.client_db_session.query(Node).filter(Node.id == id).first()
        if not node:
            return create_response('Node not found', 404)
        
        name = request.json.get('name', '')
        if not name:
            name = f'Copy of {node.name}'

        previous_node_id = request.json.get('previous_node_id', '')
        
        ## copy the node
        new_node = Node(
            quest_id=node.quest_id,
            name=name,
            screen_type=node.screen_type,
            title=node.title,
            sub_title=node.sub_title,
            points=node.points,
            quest_character_id=node.quest_character_id,
            quest_character_animation=node.quest_character_animation,
            quest_character_audio=node.quest_character_audio,
            quest_text=node.quest_text,
            answer_placeholder=node.answer_placeholder,
            button_text=node.button_text,
            quest_type=node.quest_type
        )

        g.client_db_session.add(new_node)
        g.client_db_session.commit()

        if previous_node_id:
            previous_node = g.client_db_session.query(Node).filter(Node.id == previous_node_id).first()
            if previous_node:
                previous_node.next_node_id = new_node.id
                g.client_db_session.add(previous_node)
                g.client_db_session.commit()

        ## copy the transcripts
        for transcript in node.transcripts:
            new_transcript = NodeTranscript(
                node_id=new_node.id,
                character_id=transcript.character_id,
                animation=transcript.animation,
                show_icon=transcript.show_icon,
                text=transcript.text,
                audio_en=transcript.audio_en,
                duration=transcript.duration
            )

            g.client_db_session.add(new_transcript)
       
        g.client_db_session.commit()

        ## copy the options
        for option in node.options:
            new_option = NodeOption(
                node_id=new_node.id,
                label=option.label,
                value=option.value,
                position=option.position,
                is_correct=option.is_correct,
                index=option.index,
                points=option.points
            )

            g.client_db_session.add(new_option)
            g.client_db_session.commit()

            ## copy the matching options
            for matching_option in option.matching_options:
                new_matching_option = NodeOptionMatching(
                    node_id=new_node.id,
                    node_option_id=new_option.id,
                    matching_node_option_id=matching_option.matching_node_option_id
                )

                g.client_db_session.add(new_matching_option)

        g.client_db_session.commit()
        
        return create_response('Node copied successfully', data={'id': new_node.id})