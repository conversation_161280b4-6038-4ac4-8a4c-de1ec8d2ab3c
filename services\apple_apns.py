import asyncio
import time
from typing import Dict, Any, Optional
import logging
import os
import json

from aioapns import APNs, NotificationRequest, PushType
from services.azure_key_vault import AzureKeyVault

logger = logging.getLogger(__name__)

class AppleAPNSService:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AppleAPNSService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            # Configuration - retrieved from Azure Key Vault or environment variables
            self.use_sandbox = False
            
            # Get the key from Azure Key Vault
            try:
                # Get the Apple APNS key from the key vault
                cred_json = AzureKeyVault().get_secret('apple-apns-credentials')
                self.cred = json.loads(cred_json)
                
                if not self.cred:
                    logger.error('Failed to retrieve Apple APNS key from Key Vault')
                else:
                    logger.info('Successfully initialized Apple APNS client')
            except Exception as e:
                logger.error(f'Error initializing Apple APNS service: {str(e)}')

            self._initialized = True
    
    async def _send_notification(
        self, 
        request: NotificationRequest
    ) -> bool:
        '''
        Internal method to send a live activity notification using APNs.
        
        Args:
            request: The notification request to send
            
        Returns:
            bool: True if the notification was sent successfully, False otherwise
        '''
        try:
            apn_client = APNs(
                key=self.cred.get('key', None),
                key_id=self.cred.get('key_id', None),
                team_id=self.cred.get('team_id', None),
                topic=self.cred.get('bundle_id')+'.push-type.liveactivity',  # Bundle ID
                use_sandbox=self.use_sandbox,
            )
            await apn_client.send_notification(request)
            return True
        except Exception as e:
            logger.error(f'Error sending APNs notification: {str(e)}')
            return False
    
    def push_live_activity_notification(
        self,
        live_activity_token: str,  # Live activity token
        is_update: bool,  # True for update, False for end
        content_state: Optional[Dict[str, Any]] = None  # Optional content state for updates
    ) -> bool:
        '''
        Send a live activity notification using APNs.
        
        Args:
            token: APNs device token (not used for live activity, kept for API compatibility)
            live_activity_token: The token for the specific live activity
            is_update: True for update, False for end
            content_state: Optional content state for updates
            
        Returns:
            bool: True if the notification was sent successfully, False otherwise
        '''
        try:
            if not self.cred:
                logger.error('APNS client not initialized, cannot send live activity notification')
                return False
            
            # Prepare the notification payload
            current_timestamp = int(time.time())
            
            # Base payload structure
            payload = {
                'aps': {
                    'timestamp': current_timestamp,
                    'content-state': content_state or {}
                }
            }
            
            # Set event type based on is_update flag
            if is_update:
                payload['aps']['event'] = 'update'
            else:
                payload['aps']['event'] = 'end'
                payload['aps']['dismissal-date'] = current_timestamp
            
            # Create the notification request with the live activity push type
            request = NotificationRequest(
                device_token=live_activity_token,  # Use the live activity token here
                message=payload,
                push_type=PushType.LIVEACTIVITY
            )
            
            # Send the notification
            result = asyncio.run(self._send_notification(request))
            
            return result
            
        except Exception as e:
            logger.error(f'Error preparing APNs live activity notification: {str(e)}')
            return False


# Example usage
if __name__ == '__main__':
    # Test device token
    token = ''
    
    # Initialize the service
    apns = AppleAPNSService()
    
    # Test live activity update notification
    print("Testing live activity update notification:")
    result = apns.push_live_activity_notification(
        live_activity_token=token,
        is_update=True
    )
    print(f"Live activity update result: {result}")
    
    # Test live activity end notification
    print("Testing live activity end notification:")
    result = apns.push_live_activity_notification(
        live_activity_token=token,
        is_update=False
    )
    print(f'Live activity end result: {result}')
