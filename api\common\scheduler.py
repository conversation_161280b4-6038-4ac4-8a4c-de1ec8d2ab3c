import datetime
import logging
from typing import Dict, Any, Optional, List
from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy import and_, or_, exists, select

import pytz
from croniter import croniter
from flask import g

from clientmodels import Client, Scheduler, SchedulerLog, Task, TaskLog, User, UserChest, Notification, UserNotification, UserStreak, UserLiveActivity
from api.common.notification import NotificationFunction
from services.apple_apns import AppleAPNSService

logger = logging.getLogger(__name__)

class SchedulerFunction:
    def create_scheduler(
        self,
        name: str, 
        func: str,
        cron: str,
        data: Dict[Any, Any]
    ) -> Optional[Scheduler]:
        """Create a new scheduler with cron expression.
        
        Args:
            name: Name of the scheduler
            func: Function name to be executed
            cron: Cron expression for scheduling
            data: Task data in dictionary format
        
        Returns:
            Scheduler object if successful, None otherwise
        
        Raises:
            ValueError: If cron expression is invalid or scheduler name exists
        """
        try:
            # Validate cron expression
            if not croniter.is_valid(cron):
                raise ValueError(f"Invalid cron expression: {cron}")

            # Check if scheduler with same name exists
            scheduler = g.db_session.query(Scheduler).filter(
                Scheduler.name == name,
                Scheduler.is_deleted == False
            ).first()
            if scheduler:
                raise ValueError(f"Scheduler with name {name} already exists")

            # Generate the next run time
            now = datetime.datetime.utcnow()
            next_run_time = croniter(cron, now).get_next(datetime.datetime)

            scheduler = Scheduler()
            scheduler.name = name
            scheduler.func = func
            scheduler.cron = cron
            scheduler.data = data
            scheduler.next_run_time = next_run_time
            g.db_session.add(scheduler)
            g.db_session.commit()
            
            logger.info(f"Created scheduler {name} with cron {cron}")
            return scheduler
            
        except Exception as e:
            logger.error(f"Failed to create scheduler: {str(e)}")
            g.db_session.rollback()
            return None

    def update_scheduler(
        self,
        scheduler_id: str, 
        name: Optional[str] = None, 
        func: Optional[str] = None,
        cron: Optional[str] = None,
        data: Optional[Dict] = None
    ) -> bool:
        """Update an existing scheduler.
        
        Args:
            scheduler_id: ID of the scheduler to update
            name: Optional new name
            func: Optional new function name
            cron: Optional new cron expression
            data: Optional new task data
        
        Returns:
            bool indicating success or failure
        
        Raises:
            ValueError: If cron expression is invalid or scheduler not found
        """
        try:
            scheduler = g.db_session.query(Scheduler).filter(
                Scheduler.id == scheduler_id,
                Scheduler.is_deleted == False
            ).first()
            if not scheduler:
                raise ValueError(f"Scheduler {scheduler_id} not found")
            
            # Update other fields if provided
            if func:
                scheduler.func = func
            if data:
                scheduler.data = data

            # Validate cron expression if provided
            if cron and cron != scheduler.cron:
                if not croniter.is_valid(cron):
                    raise ValueError(f"Invalid cron expression: {cron}")
                scheduler.cron = cron
                now = datetime.datetime.utcnow()
                next_run_time = croniter(cron, now).get_next(datetime.datetime)
                scheduler.next_run_time = next_run_time
            
            # Check name uniqueness if updating name
            if name and name != scheduler.name:
                existing = g.db_session.query(Scheduler).filter(
                    Scheduler.name == name,
                    Scheduler.is_deleted == False,
                    Scheduler.id != scheduler_id
                ).first()
                if existing:
                    raise ValueError(f"Scheduler with name {name} already exists")
                scheduler.name = name
            
            g.db_session.commit()
            logger.info(f"Updated scheduler {scheduler_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update scheduler: {str(e)}")
            g.db_session.rollback()
            return False

    def delete_scheduler(
        self, 
        scheduler_id: str
    ) -> bool:
        """Soft delete a scheduler.
        
        Args:
            scheduler_id: ID of the scheduler to delete
        
        Returns:
            bool indicating success or failure
        
        Raises:
            ValueError: If scheduler not found
        """
        try:
            scheduler = g.db_session.query(Scheduler).filter(
                Scheduler.id == scheduler_id,
                Scheduler.is_deleted == False
            ).first()
            if not scheduler:
                raise ValueError(f"Scheduler {scheduler_id} not found")
            
            # Soft delete the scheduler
            scheduler.is_deleted = True
            g.db_session.commit()
            logger.info(f"Deleted scheduler {scheduler_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete scheduler: {str(e)}")
            g.db_session.rollback()
            return False

    def list_schedulers(self) -> List[Scheduler]:
        """Get all active schedulers.
        
        Returns:
            List of active scheduler objects
        """
        try:
            return g.db_session.query(Scheduler).filter(
                Scheduler.is_deleted == False
            ).all()
        except Exception as e:
            logger.error(f"Failed to list schedulers: {str(e)}")
            return []

    def execute_scheduler(
        self
    ) -> bool:
        """
        Execute schedulers based on their cron expressions. If scheduler_id is provided,
        only execute that specific scheduler. Otherwise, execute all active schedulers.
        
        Args:
            scheduler_id (str, optional): ID of specific scheduler to execute. Defaults to None.
        """
        try:
            # Query active schedulers
            schedulers = g.db_session.query(Scheduler).filter(
                Scheduler.is_deleted == False,
                Scheduler.status == 'active'
            ).all()
        
            if not schedulers:
                logger.info(f"No active schedulers found")
                return True
        
            now = datetime.datetime.utcnow()
        
            for scheduler in schedulers:
                try:
                    # Initialize croniter with scheduler's cron expression
                    cron = croniter(scheduler.cron, now)
                
                    # Calculate previous and next run times
                    prev_run = cron.get_prev(datetime.datetime)
                    next_run = cron.get_next(datetime.datetime)
                
                    # Skip if it's not time to run yet
                    if scheduler.last_run_time and scheduler.last_run_time > prev_run:
                        logger.info(f"Skipping scheduler {scheduler.id} - {scheduler.name} as it's not time to run yet")
                        continue
                    
                    # Create scheduler log entry
                    scheduler_log = SchedulerLog(
                        scheduler_id=scheduler.id,
                        start_time=now,
                        status='running'
                    )
                    g.db_session.add(scheduler_log)
                    g.db_session.commit()
                
                    # Get the function to execute
                    func = getattr(self, scheduler.func, None)
                    if not func:
                        error_msg = f"Function {scheduler.func} not found in scheduler object"
                        raise ValueError(error_msg)
                    if not callable(func):
                        error_msg = f"Function {scheduler.func} is not callable"
                        raise ValueError(error_msg)
                
                    # Execute the function with scheduler data
                    result = func(scheduler.data)
                
                    # Update scheduler run times
                    scheduler.last_run_time = now
                    scheduler.next_run_time = next_run
                    
                    # Update scheduler log with success
                    scheduler_log.status = 'succeeded'
                    scheduler_log.end_time = datetime.datetime.utcnow()
                    scheduler_log.result = result if result else None
                    g.db_session.commit()
                
                    logger.info(f"Successfully executed scheduler {scheduler.id} - {scheduler.name}")
                
                except Exception as e:
                    error_msg = f"Error executing scheduler {scheduler.id} - {scheduler.name}: {str(e)}"
                    logger.error(error_msg)
                    
                    # Update scheduler log with error
                    scheduler_log.status = 'failed'
                    scheduler_log.end_time = datetime.datetime.utcnow()
                    scheduler_log.error_message = str(e)
                    g.db_session.commit()
                    continue
                
            return True
            
        except Exception as e:
            logger.error(f"Unexpected error in execute_scheduler: {str(e)}")
            g.db_session.rollback()
            return False

    def process_scheduled_feeds(
        self,
        data: Dict[Any, Any]
    ) -> str:
        """
        Process tasks that are scheduled to be executed.
            
        This function:
        1. Gets the current UTC time
        2. Finds all tasks that are scheduled to be executed at or before the current time
        3. Execute these tasks
        
        Args:
            data: Additional data for processing (optional)
            
        Returns:
            str: Status message indicating success or failure
        """
        try:
            utc_now = datetime.datetime.utcnow()
            
            tasks = g.db_session.query(Task).filter(
                Task.status == 'active', 
                Task.is_deleted == False,
                Task.scheduled_for <= utc_now
            ).all()
            if not tasks:
                return 'No scheduled tasks due for processing'
            
            from api.common.task import TaskFunction

            for task in tasks:
                func = getattr(TaskFunction(), task.func, None)
                if not func:
                    raise ValueError(f"Function {task.func} not found in task object")
                if not callable(func):
                    raise ValueError(f"Function {task.func} is not callable")
                
                task_log = TaskLog()
                task_log.task_id = task.id
                try:
                    # Execute the function with task data
                    result = func(task.data)
                    task_log.result = result if result else None
                    
                    # Update task status
                    task.status = 'succeeded' if result else 'failed'
                    g.db_session.add(task_log)
                    g.db_session.commit()
                except Exception as e:
                    error_msg = f"Error executing task {task.id}: {str(e)}"
                    logger.error(error_msg)
                    
                    # Update task status
                    task.status = 'failed'
                    task_log.error_message = str(e)
                    g.db_session.add(task_log)
                    g.db_session.commit()
                    continue
            
            return 'Tasks processed successfully'
        except Exception as e:
            g.db_session.rollback()
            error_msg = f"Error processing scheduled tasks: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def process_scheduled_notifications(
        self,
        data: Dict[Any, Any]
    ) -> str:
        """Process notifications that are scheduled to be sent.
        
        This function:
        1. Gets the current UTC time
        2. Finds all notifications that are scheduled to be sent at or before the current time
        3. Sends these notifications
        
        Args:
            data: Additional data for processing (optional)
            
        Returns:
            str: Status message indicating success or failure
        """
        try:
            # Get current UTC time
            utc_now = datetime.datetime.utcnow()
            
            # Find all scheduled notifications that are due
            notifications = g.db_session.query(Notification).filter(
                Notification.is_deleted == False,
                Notification.status == 'pending',
                Notification.scheduled_for.isnot(None),
                Notification.scheduled_for <= utc_now
            ).all()
            
            if not notifications:
                return "No scheduled notifications due for processing"
            
            # Initialize notification function
            notification_func = NotificationFunction()
            
            # Process each notification
            success_count = 0
            failure_count = 0
            
            for notification in notifications:
                try:
                    # Send the notification
                    if notification_func.send_notification(notification.id):
                        success_count += 1
                    else:
                        failure_count += 1
                except Exception as e:
                    logger.error(f"Error sending scheduled notification {notification.id}: {str(e)}")
                    failure_count += 1
            
            result_msg = f"Processed {len(notifications)} scheduled notifications: {success_count} succeeded, {failure_count} failed"
            logger.info(result_msg)
            return result_msg
            
        except Exception as e:
            error_msg = f"Error processing scheduled notifications: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def continue_streak_reminder(
        self,
        data: Dict[Any, Any]
    ) -> str:
        """Send notifications to users whose local time is 6 PM based on their timezone.
        
        This function:
        1. Gets the current UTC time
        2. Efficiently identifies users whose local time is 6 PM based on timezone offsets
        3. Checks if they have a UserStreak record for their local today
        4. Sends streak continuation reminders to users without a streak record today
        
        Args:
            data: Additional data for the notification (optional)
            
        Returns:
            str: Status message indicating success or failure
        """
        try:
            # Get current UTC time
            utc_now = datetime.datetime.utcnow()
            
            # Target local hour (6 PM = 18:00)
            target_hour = 18
            
            # Initialize notification function and get client_id once
            notification_func = NotificationFunction()
            client_id = g.db_session.query(Client).filter(Client.id_key == "global").first().id
            
            # Calculate which timezone offsets would have 6 PM local time now
            target_timezone_offsets = []
            
            # For each possible hour offset (-12 to +14), check if it would be exactly 6 PM local time now
            for offset in range(-12, 15):
                # Calculate what hour it would be with this offset now
                local_hour_now = (utc_now.hour + offset) % 24
                
                # Include offset only if local time is exactly 6 PM now
                if local_hour_now == target_hour:
                    target_timezone_offsets.append(offset)
            
            # If no offsets match, return early
            if not target_timezone_offsets:
                return "No timezone offsets currently have 6 PM local time in this hour"
            
            # Build a list of timezone names that match our target offsets
            target_timezone_names = []
            
            # Populate timezone_offsets dictionary
            for tz_name in pytz.all_timezones:
                try:
                    tz = pytz.timezone(tz_name)
                    # Get current offset in hours
                    offset_seconds = tz.utcoffset(utc_now).total_seconds()
                    offset_hours = int(offset_seconds / 3600)
                    
                    # Store the timezone name if it matches our target offsets
                    if offset_hours in target_timezone_offsets:
                        target_timezone_names.append(tz_name)
                        
                except Exception:
                    continue
            
            # If no matching timezone names, return early
            if not target_timezone_names:
                return "No matching timezone names for 6 PM local time"
            
            # For each timezone, we need to calculate what date it is in that timezone
            timezone_dates = {}
            for tz_name in target_timezone_names:
                tz = pytz.timezone(tz_name)
                local_now = utc_now.astimezone(tz)
                timezone_dates[tz_name] = {
                    'today': local_now.date(),
                    'yesterday': (local_now - datetime.timedelta(days=1)).date()
                }
            
            # Build a query for each timezone group since they may have different local dates
            users_to_notify = []
            for tz_name in target_timezone_names:
                local_today = timezone_dates[tz_name]['today']
                local_yesterday = timezone_dates[tz_name]['yesterday']
                
                timezone_users = (
                    g.db_session.query(User.id)
                    .outerjoin(
                        UserStreak,
                        and_(
                            UserStreak.user_id == User.id,
                            UserStreak.date == local_today  # Check against local today
                        )
                    )
                    .filter(
                        User.is_deleted == False,
                        User.is_active == True,
                        User.timezone == tz_name,  # Only users in this specific timezone
                        # Notification preferences check using single OR condition
                        or_(
                            User.settings.is_(None),
                            User.settings['notification_preference'].is_(None),
                            User.settings['notification_preference']['continue_streak_reminder'].is_(None),
                            User.settings['notification_preference']['continue_streak_reminder'].cast(Boolean).is_(True)
                        ),
                        # No streak today (from the outer join)
                        UserStreak.id.is_(None),
                        # Had a streak yesterday (using correlate for better performance)
                        exists(
                            select(1)
                            .where(
                                and_(
                                    UserStreak.user_id == User.id,
                                    UserStreak.date == local_yesterday
                                )
                            )
                            .correlate(User)
                        ),
                        # No recent reminder (using correlate)
                        ~exists(
                            select(1)
                            .select_from(Notification)
                            .join(
                                UserNotification,
                                and_(
                                    UserNotification.notification_id == Notification.id,
                                    UserNotification.user_id == User.id
                                )
                            )
                            .where(
                                and_(
                                    Notification.notification_type == 'continue_streak_reminder',
                                    Notification.date_created >= (utc_now - datetime.timedelta(hours=24))
                                )
                            )
                        )
                    )
                ).all()
                
                users_to_notify.extend([user_id for (user_id,) in timezone_users])
            
            # Track notification counts
            streak_notification_count = 0
            
            # Send streak continuation reminders if there are any users without streaks today
            if users_to_notify:
                # Prepare notification content for streak reminder
                title = "Continue Your Streak!"
                body = "Don't break your streak! Open the app now to continue your progress."
                notification_data = {
                    'type': 'continue_streak_reminder',
                    'action': '/home',
                }

                # Send the notification to streak reminder users
                notification = notification_func.create_notification(
                    client_id=client_id,
                    recipient_type='individual',
                    recipient_ids=users_to_notify,
                    title=title,
                    body=body,
                    data=notification_data,
                    notification_type="continue_streak_reminder",
                    enable_notification_push=True,
                    update_badge=True,
                    scheduled_for=None  # Send immediately
                )
                
                if notification:
                    streak_notification_count = len(users_to_notify)
            
            # Prepare result message
            if streak_notification_count > 0:
                result_msg = f"Successfully sent {streak_notification_count} streak continuation reminders"
                return result_msg
            else:
                return "No users currently in 6 PM local time window or all users already have streaks today"
            
        except Exception as e:
            error_msg = f"Error sending continue streak reminder notifications: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def chest_reminder(
        self,
        data: Dict[Any, Any]
    ) -> str:
        """Send notifications to users for each unopened chest that is ready to be opened.
        
        This function:
        1. Finds all unopened chests that are ready to be opened
        2. Checks if a notification has already been sent for each specific chest
        3. Sends a notification for each unopened chest that hasn't been notified about yet
        
        Args:
            data: Additional data for the notification (optional)
            
        Returns:
            str: Status message indicating success or failure
        """
        try:
            # Get current UTC time
            utc_now = datetime.datetime.utcnow()

            # Initialize notification function and get client_id once
            notification_func = NotificationFunction()
            client_id = g.db_session.query(Client).filter(Client.id_key == "global").first().id

            # Get the results with user settings in a single query
            query = (
                g.db_session.query(
                    User.id,
                    UserChest.id
                )
                .join(
                    UserChest,
                    and_(
                        UserChest.user_id == User.id,
                        UserChest.is_opened == False,
                        UserChest.date_openable <= utc_now
                    )
                )
                .filter(
                    User.is_deleted == False,
                    User.is_active == True,
                    or_(
                        User.settings.is_(None),
                        User.settings['notification_preference'].is_(None),
                        User.settings['notification_preference']['chest_reminders'].is_(None),
                        User.settings['notification_preference']['chest_reminders'].cast(Boolean).is_(True)
                    ),
                    ~exists(
                        select(1)
                        .select_from(Notification)
                        .join(
                            UserNotification,
                            and_(
                                UserNotification.notification_id == Notification.id,
                                UserNotification.user_id == User.id
                            )
                        )
                        .where(
                            and_(
                                Notification.notification_type == 'chest_reminder',
                                Notification.data['user_chest_id'].astext == UserChest.id
                            )
                        )
                        .correlate(User, UserChest)
                    )
                )
            ).all()
            
            if not query:
                return "No unopened chests found that need notifications"
            
            # Track successful notifications
            successful_notifications = 0
            
            # Send notifications for each chest
            for user_id, chest_id in query:
                notification_data = {
                    'type': 'chest_reminder',
                    'action': '/home',
                    'user_chest_id': chest_id
                }
                
                # Send the notification for this specific chest
                notification = notification_func.create_notification(
                    client_id=client_id,
                    recipient_type='individual',
                    recipient_ids=[user_id],
                    title="Chest Ready to Open!",
                    body="You have a chest waiting to be opened. Claim your rewards now!",
                    data=notification_data,
                    notification_type="chest_reminder",
                    enable_notification_push=True,
                    update_badge=True,
                    scheduled_for=None
                )
                
                if notification:
                    successful_notifications += 1
            
            # Report results
            total_chests = len(query)
            total_users = len({user_id for user_id, _ in query})
            
            if successful_notifications > 0:
                return f"Successfully sent {successful_notifications} chest reminder notifications for {total_chests} unopened chests to {total_users} users"
            else:
                return "Failed to send chest reminder notifications"            
        except Exception as e:
            error_msg = f"Error sending chest reminder notifications: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def absence_reminder(
        self,
        data: Dict[Any, Any]
    ) -> str:
        """Send notifications to users who haven't opened the app for specific periods.
        
        This function:
        1. Gets the current UTC time
        2. Identifies users who haven't had activity (based on UserStreak records)
        3. Sends a notification based on their longest absence period
        
        Args:
            data: Additional data for the notification (optional)
            
        Returns:
            str: Status message indicating success or failure
        """
        try:
            # Get current UTC time
            utc_now = datetime.datetime.utcnow()
            utc_today = utc_now.date()
            
            # Define absence thresholds in days (in ascending order)
            absence_thresholds = [7, 14, 30, 60, 90]

            # Track notification counts
            notification_counts = {threshold: 0 for threshold in absence_thresholds}
            
            # Track total notifications sent
            total_notifications = 0

            # Get the global client ID and initialize notification function once
            client_id = g.db_session.query(Client).filter(Client.id_key == "global").first().id
            notification_func = NotificationFunction()
            
            # Get all users who haven't had activity and meet notification criteria
            base_query = (
                g.db_session.query(User.id)
                .filter(
                    User.is_deleted == False,
                    User.is_active == True,
                    # Notification preferences check
                    or_(
                        User.settings.is_(None),  # No settings at all
                        User.settings['notification_preference'].is_(None),  # No notification preferences
                        User.settings['notification_preference']['absence_reminders'].is_(None),  # Setting not specified
                        User.settings['notification_preference']['absence_reminders'].cast(Boolean).is_(True)  # Explicitly enabled
                    )
                )
            )

            # Process users for each threshold from highest to lowest
            for days in absence_thresholds:
                cutoff_date = utc_today - datetime.timedelta(days=days)
                
                target_users = (
                    base_query.filter(
                        # No streaks since cutoff date
                        ~exists(
                            select(1).where(
                                and_(
                                    UserStreak.user_id == User.id,
                                    UserStreak.date >= cutoff_date
                                )
                            )
                        ),
                        # Has had streaks before cutoff date
                        exists(
                            select(1).where(
                                and_(
                                    UserStreak.user_id == User.id,
                                    UserStreak.date < cutoff_date
                                )
                            )
                        ),
                        # Check that no absence reminder has been sent since the cutoff date
                        ~exists(
                            select(1).where(
                                and_(
                                    UserNotification.user_id == User.id,
                                    Notification.id == UserNotification.notification_id,
                                    Notification.notification_type == f'absence_reminder_{days}',
                                    Notification.date_created >= cutoff_date
                                )
                            )
                        )
                    )
                ).all()
                
                target_user_ids = [user.id for user in target_users]
                
                if target_user_ids:
                    
                    # Prepare notification content based on absence duration
                    if days == 7:
                        title = "7 days has passed!"
                        body = "It's been a week since you last visited. Come back and see what's new!"
                    elif days == 14:
                        title = "Two weeks have passed!"
                        body = "It's been two weeks since your last visit. Your journey is waiting for you!"
                    elif days == 30:
                        title = "A month has passed!"
                        body = "It's been a month since we last saw you. Come back and continue your progress!"
                    elif days == 60:
                        title = "Two months have passed!"
                        body = "It's been two months since your last visit. We've got exciting content waiting for you!"
                    else:  # 90 days
                        title = "Three months have passed!"
                        body = "It's been three months since you visited. Come back and reconnect with your journey!"
                    
                    notification_data = {
                        'type': f'absence_reminder_{days}',
                        'action': '/home',
                    }
                    
                    # Send the notification to all target users
                    notification = notification_func.create_notification(
                        client_id=client_id,
                        recipient_type='individual',
                        recipient_ids=target_user_ids,
                        title=title,
                        body=body,
                        data=notification_data,
                        notification_type=f'absence_reminder_{days}',
                        enable_notification_push=True,
                        update_badge=True,
                        scheduled_for=None  # Send immediately
                    )
                    
                    if notification:
                        notification_counts[days] = len(target_user_ids)
                        total_notifications += len(target_user_ids)
            
            # Prepare result message
            if total_notifications > 0:
                result_details = ", ".join([f"{count} users at {days} days" for days, count in notification_counts.items() if count > 0])
                return f"Successfully sent absence reminder notifications to {total_notifications} users ({result_details})"
            else:
                return "No users found matching absence reminder criteria"
            
        except Exception as e:
            error_msg = f"Error sending absence reminder notifications: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def end_stale_live_activities(
        self,
        data: Dict[Any, Any]
    ) -> str:
        """End live activities that have been inactive for too long.
        
        This function:
        1. Gets the current UTC time
        2. Groups active activities by device token
        3. For devices with multiple activities:
           - If one activity ends -> send update notification
        4. For devices with single activity:
           - If the activity ends -> send end notification
        
        Args:
            data: Additional data for processing (optional)
            
        Returns:
            str: Status message indicating success or failure
        """
        try:
            from sqlalchemy import func, case
            now = datetime.datetime.utcnow()
            
            # Get all active activities grouped by device_id
            device_groups = g.db_session.query(
                UserLiveActivity.device_id,
                UserLiveActivity.activity_token,
                func.count(UserLiveActivity.id).label('count')
            ).filter(
                UserLiveActivity.status == 'active',
                UserLiveActivity.device_id.isnot(None)
            ).group_by(
                UserLiveActivity.device_id,
                UserLiveActivity.activity_token
            ).having(
                # Only get groups that have at least one ended activity
                func.sum(case((UserLiveActivity.end_time <= now, 1), else_=0)) > 0
            ).all()
            
            updated_count = 0
            ended_count = 0
            apns = AppleAPNSService()  # Initialize Apple APNS service
            
            activity_to_succeeded = []
            activity_to_failed = []
            # Process each device group
            for device_id, activity_token, count in device_groups:
                result = False
                
                # Use Apple APNS for all live activity notifications
                if count > 1:
                    result = apns.push_live_activity_notification(
                        live_activity_token=activity_token,
                        is_update=True
                    )
                    updated_count += 1
                else:
                    result = apns.push_live_activity_notification(
                        live_activity_token=activity_token,
                        is_update=False
                    )
                    ended_count += 1
                
                if result:
                    activity_to_succeeded.append(activity_token)
                else:
                    activity_to_failed.append(activity_token)
            
            g.db_session.query(
                UserLiveActivity
            ).filter(
                UserLiveActivity.activity_token.in_(activity_to_succeeded),
                UserLiveActivity.end_time <= now
            ).update({
                'status': 'succeeded'
            })
            
            g.db_session.query(
                UserLiveActivity
            ).filter(
                UserLiveActivity.activity_token.in_(activity_to_failed),
                UserLiveActivity.end_time <= now
            ).update({
                'status': 'failed'
            })
            g.db_session.commit()
            
            result_messages = []
            if updated_count > 0:
                result_messages.append(f"Updated {updated_count} devices with multiple activities")
            if ended_count > 0:
                result_messages.append(f"Ended {ended_count} single activities")
            if activity_to_failed:
                result_messages.append(f"Failed to process {len(activity_to_failed)} devices")
            
            if result_messages:
                return "\n".join(result_messages)
            return "No activities needed processing"
            
        except Exception as e:
            g.db_session.rollback()
            error_msg = f"Error processing live activities: {str(e)}"
            logger.error(error_msg)
            return error_msg
