import json

from flask import g, request
from flask_restx import Namespace, Resource, fields
from sqlalchemy import String, text

from api.common.file import FileService
from api.common.helper import create_response
from clientmodels import Badge

cms_badges_api = Namespace('api_cms_badges', description='Badges management operations')


# Badge model for API documentation and validation
badge_model = cms_badges_api.model('BadgeItem', {
    'name': fields.String(required=True, description='Badge Name'),
    'description': fields.String(required=True, description='Badge Description'),
    'image': fields.String(required=True, description='Badge Image URL'),
    'achievement_message': fields.String(required=True, description='Badge Achievement Message'),
    'achievement_type': fields.String(required=True, description='Badge Achievement Type'),
    'achievement_conditions': fields.Raw(required=True, description='Badge Achievement Conditions', example={}),
    'rewards': fields.Raw(required=True, description='Badge Reward', example={}),
})


@cms_badges_api.doc(security='bearer')
@cms_badges_api.route('/', methods=['POST'])
@cms_badges_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class BadgeItem(Resource):
    def get(self, id):
        """Get a specific badge"""
        badge = g.db_session.query(Badge).filter_by(id=id).first()
        if badge is None:
            return create_response("Badge not found", status=404)

        return create_response("Badge retrieved successfully", data=badge.to_dict())
    
    @cms_badges_api.expect(badge_model)
    def post(self):
        """Create a new badge"""
        data = request.json

        name = data.get('name').strip()
        description = data.get('description').strip()
        image = data.get('image', '').strip()
        achievement_message = data.get('achievement_message').strip()
        achievement_type = data.get('achievement_type')
        achievement_conditions = data.get('achievement_conditions', {})
        rewards = data.get('rewards', {})
        
        badge = Badge()
        # Handle file upload
        FileService.process_entity_image(badge, image, 'badge', badge.id)

        # Create new badge
        badge.name=name
        badge.description=description
        badge.achievement_message=achievement_message
        badge.achievement_type=achievement_type
        badge.achievement_conditions=json.dumps(achievement_conditions)
        badge.rewards=json.dumps(rewards)

        g.db_session.add(badge)
        g.db_session.commit()

        return create_response("Badge created successfully", data=badge.to_dict())

    @cms_badges_api.expect(badge_model)
    def put(self, id):
        """Update a badge"""
        data = request.json

        badge = g.db_session.query(Badge).filter_by(id=id).first()
        if badge is None:
            return create_response("Badge not found", status=404)

        image = request.json.get('image', '').strip()
        FileService.process_entity_image(badge, image, 'badge', badge.id)

        # Update badge fields
        badge.name = data.get('name', badge.name).strip()
        badge.description = data.get('description', badge.description).strip()
        badge.achievement_message = data.get('achievement_message', badge.achievement_message).strip()
        badge.achievement_type = data.get('achievement_type', badge.achievement_type)
        badge.achievement_conditions = json.dumps(data.get('achievement_conditions', badge.achievement_conditions))

        ## update rewards if it is in data
        if 'rewards' in data:
            badge.rewards = json.dumps(data.get('rewards', {}))

        g.db_session.add(badge)
        g.db_session.commit()

        return create_response("Badge updated successfully", data=badge.to_dict())

    def delete(self, id):
        """Delete a badge"""
        badge = g.db_session.query(Badge).filter_by(id=id).first()
        if badge is None:
            return create_response("Badge not found", status=404)

        g.db_session.delete(badge)
        g.db_session.commit()

        return create_response("Badge deleted successfully")


# Parser for list endpoints
badge_list_parser = cms_badges_api.parser()
badge_list_parser.add_argument('page', type=int, help='Page number', default=1)
badge_list_parser.add_argument('limit', type=int, help='Items per page', default=20)
badge_list_parser.add_argument('search', type=str, help='Search query', default='')
badge_list_parser.add_argument('filter', type=str, help='Achievement Type', default='')
badge_list_parser.add_argument('sort', type=str, help='Sort by field', default='')

@cms_badges_api.doc(security='bearer')
@cms_badges_api.route('/list')
class BadgeList(Resource):
    @cms_badges_api.expect(badge_list_parser)
    def get(self):
        """Get list of badges"""
        args = badge_list_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        sort = args.get('sort', None)
        offset = (page - 1) * limit

        # Build query
        query = g.db_session.query(Badge)
        
        # Apply filters
        if search:
            query = query.filter(Badge.name.ilike(f'%{search}%'))

        ## if filter is applied, search with archievement type filter
        ## and has no condition
        filter = args.get('filter', '')
        if filter:
            query = query.filter(
                Badge.achievement_type == filter
            )

            condition_str = "first"
            query = query.filter(
                ~Badge.achievement_conditions.cast(String).ilike(f'%{condition_str}%')
            )

        sort = args.get('sort', '')
        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(Badge, sort[1:]).desc())
            else:
                query = query.order_by(getattr(Badge, sort))
        else:
            query = query.order_by(Badge.name)

        # Get total count
        total = query.count()
        
        # Get paginated results
        badges = query.offset(offset).limit(limit).all()
        
        # Format response
        data = [badge.to_dict() for badge in badges]
        
        return create_response("Badge list retrieved successfully", data=data, total=total, page=page, limit=limit)
