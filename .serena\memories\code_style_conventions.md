# XAPA Backend Code Style & Conventions

## Python Style
- **PEP 8 compliant**: Standard Python formatting
- **Snake_case**: Variable and function names
- **CamelCase**: Class names
- **UPPER_CASE**: Constants and environment variables

## Import Organization
- Standard library imports first
- Third-party imports second  
- Local application imports last
- Grouped with blank lines between sections

Example:
```python
import datetime
import json

from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from clientmodels import Client
```

## Database Models
- **BaseModel**: All models inherit from BaseModel with common fields:
  - `id`: UUID7 primary key
  - `date_created`, `date_updated`: Timestamps
  - `create_user`, `update_user`: Audit fields
  - `is_deleted`: Soft delete flag
  - `status`: Record status
- **Multi-tenant**: Models use `ClientBase` for client-specific data
- **SQLAlchemy events**: Automatic user tracking on insert/update

## API Structure
- **Flask-RestX**: Used for API documentation and validation
- **Namespaces**: Organize endpoints by feature area
- **Resource classes**: Each endpoint is a Resource class
- **Model definitions**: Input/output models defined with `fields`
- **Decorators**: `@check_user_permission` for authorization
- **Response format**: Consistent response structure via `create_response()`

## Naming Conventions
- **API files**: Feature-based (e.g., `clients.py`, `users.py`)
- **Namespaces**: Prefixed with area (e.g., `cms_clients_api`)
- **Endpoints**: RESTful patterns (`/list`, `/create`, `/update/{id}`)
- **Models**: Descriptive names matching database tables

## Configuration
- **Environment-based**: Separate config files for dev/prod
- **Azure Key Vault**: Production secrets management
- **Environment variables**: Local development configuration
- **Feature flags**: `IS_PRODUCTION` flag for environment detection

## Error Handling
- **Consistent responses**: Use `create_response()` helper
- **Status codes**: Proper HTTP status codes
- **Validation**: Flask-RestX model validation