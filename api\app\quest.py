import datetime
import random
import logging

from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from api.app.badge import check_badge
from api.app.chest import get_chest
from api.common.helper import create_response
from services.user_style_service import update_user_style_on_quest_completion
from clientmodels import MASTER_TENANT, Chest, ChestAssetAssociation, Quest, QuestCategoryAssociation, QuestTagAssociation, \
    QuestFacetAssociation, Category, Tag, Facet, UserProgram, UserXperience, UserStyleMapping, UserStyle
from clientmodels import Node, NodeBranch, NodeOptionMatching
from clientmodels import Program, Xperience, Asset
from clientmodels import UserQuest, UserNode, UserNodeHistory
from clientmodels import PublishedNode, PublishedNodeBranch, PublishedNodeOptionMatching, PublishedNodeOption, PublishedNodeTranscript
from clientmodels import PublishedQuest

app_quest_api = Namespace('api_app_quest', description='Quest related API')


## get quest item by id with all its nodes
@app_quest_api.doc(security='bearer')
@app_quest_api.route('/<string:id>')
@app_quest_api.param('id', 'Quest ID')

class QuestItem(Resource):
    def get(self, id):
        ## if tenant_id is MASTER_TENANT, then get the latest revision of the quest, otherwise get is_latest = True
        if g.tenant_id == MASTER_TENANT:     
            quest = g.db_session.query(PublishedQuest).filter_by(quest_id=id).order_by(PublishedQuest.revision.desc()).first()
        else:
            quest = g.db_session.query(PublishedQuest).filter_by(quest_id=id, is_latest=True).first()
            
        if quest is None:
            return create_response('Quest not found', 404)
        
        quest_dict = quest.to_dict(["id", "name", "description", "image", "level", "learning_objective"])
        quest_dict['id'] = quest.quest_id

        ## get quest categories
        quest_dict['categories'] = []
        # Instead of querying categories in a loop:
        quest_categories = g.db_session.query(QuestCategoryAssociation, Category).join(Category, QuestCategoryAssociation.category_id == Category.id).filter(QuestCategoryAssociation.quest_id == quest.id).all()
        quest_dict['categories'] = [category.to_dict() for _, category in quest_categories]

        ## get quest tags
        quest_dict['tags'] = []
        quest_tags = g.db_session.query(QuestTagAssociation, Tag).join(Tag, QuestTagAssociation.tag_id == Tag.id).filter(QuestTagAssociation.quest_id == quest.id).all()
        quest_dict['tags'] = [tag.to_dict() for _, tag in quest_tags]

        ## get quest facets
        quest_dict['facets'] = []
        quest_facets = g.db_session.query(QuestFacetAssociation, Facet).join(Facet, QuestFacetAssociation.facet_id == Facet.id).filter(QuestFacetAssociation.quest_id == quest.id).all()
        quest_dict['facets'] = [facet.to_dict() for _, facet in quest_facets]

        ## get quest badges
        quest_dict['badges'] = []
        

        ## get quest chest and its assets
        if quest.chest_id:
            chest = g.db_session.query(Chest).filter_by(id=quest.chest_id).first()
            if chest:
                chest_dict = chest.to_dict()
                chest_dict['assets'] = []
                chest_assets = g.db_session.query(ChestAssetAssociation).filter_by(chest_id=chest.id).all()
                for chest_asset in chest_assets:
                    asset = g.db_session.query(Asset).filter_by(id=chest_asset.asset_id).first()
                    if asset:
                        chest_dict['assets'].append(asset.to_dict())
                quest_dict['chest'] = chest_dict

        quest_dict['nodes'] = []

        nodes = g.db_session.query(PublishedNode).filter_by(published_quest_id=quest.id).order_by(PublishedNode.date_created).all()

        for node in nodes:
            node_dict = node.to_dict(["title", "sub_title", "points", "button_text", "next_node_id", "branches_from_ids"])
            node_dict['node_id'] = node.node_id
            node_dict['node_type'] = node.screen_type
            node_dict['character_ids'] = []
            node_dict['transcripts'] = []
            node_dict['value_list'] = {}
            node_dict['quest'] = {}
            node_dict['is_quiz'] = True if node.quest_type == "quiz" else False

            ## get node quest
            if node.quest_text:
                node_dict['quest'] = {
                    'character_id': node.quest_character_id if node.quest_character_id else '',
                    'text': node.quest_text,
                    'actions': []
                }

                if node.quest_character_animation:
                    node_dict['quest']['actions'].append(node.quest_character_animation)

            ## get node transcripts
            character_ids = []
            node_transcripts = g.db_session.query(PublishedNodeTranscript)\
                .filter_by(published_node_id=node.id)\
                .order_by(PublishedNodeTranscript.date_created).all()

            ## for intro type there will be multiple characters with one text
            if node.screen_type == 'intro_transition':
                show_icon = False
                transcript_character_id = ''
                transcript_dict = {
                    'actions': [],
                    'dialog': {
                        'show_icon': show_icon,
                        'text': '',
                        'audio_en': '',
                        'character_id': ''
                    },
                    'duration': 0
                }
                
                for node_transcript in node_transcripts:
                    if node_transcript.character_id:
                        action = {
                            'character_id': node_transcript.character_id,
                            'animation_key': node_transcript.animation
                        }
                        transcript_dict['actions'].append(action)
                        character_ids.append(node_transcript.character_id)

                    if node_transcript.text:
                        transcript_dict['dialog']['text'] = node_transcript.text

                node_dict['transcripts'].append(transcript_dict)

            else:
                for node_transcript in node_transcripts:
                    show_icon = node_transcript.show_icon
                    transcript_character_id = node_transcript.character_id
                        
                    transcript_dict = {
                        'actions': [],
                        'dialog': {
                            'show_icon': show_icon,
                            'text': node_transcript.text,
                            'audio_en': node_transcript.audio_en,
                            'character_id': transcript_character_id
                        },
                        'duration': node_transcript.duration
                    }

                    if node_transcript.character_id:
                        action = {
                            'character_id': node_transcript.character_id,
                            'animation_key': node_transcript.animation
                        }
                        transcript_dict['actions'].append(action)
                        character_ids.append(node_transcript.character_id)

                    node_dict['transcripts'].append(transcript_dict)

            ## get node options
            node_options = g.db_session.query(PublishedNodeOption).filter_by(published_node_id=node.id).all()
            
            if node_options:
                node_dict['value_list'] = {
                    'max_selection': 0,
                    'min_selection': 0,
                    'options': []
                }

                ## if screen type is single answer, max_selection and min_selection is 1
                if node.screen_type == 'single_answer':
                    node_dict['value_list']['max_selection'] = 1
                    node_dict['value_list']['min_selection'] = 1

                ## if screen type is multiple answer, min_selection is 1
                if node.screen_type == 'multi_answer':
                    node_dict['value_list']['min_selection'] = 1

                ## if screen type is matching, add min selection to 1
                if node.screen_type == 'matching':
                    node_dict['value_list']['min_selection'] = 1

                    ## if quest type is quiz, maximum selection is 1
                    if node.quest_type == 'quiz':
                        node_dict['value_list']['max_selection'] = 1
                
                for node_option in node_options:
                    option_dict = node_option.to_dict(["id", "label", "value", "points", "position", "is_correct", "feedback", "index"])
                    option_dict['id'] = node_option.node_option_id
                    option_dict['next_node_id'] = node_option.next_node_id if node_option.next_node_id else ''
                    if not option_dict['value']:
                         option_dict['value'] = option_dict['label']

                    ## if node type is matching, try to find the matching nodes
                    if node.screen_type == 'matching':
                        matching_options = g.db_session.query(PublishedNodeOptionMatching).filter(PublishedNodeOptionMatching.node_option_id == node_option.node_option_id, PublishedNodeOptionMatching.published_node_id == node.id).all()
                        option_dict['matching_option_ids'] = [matching_option.matching_node_option_id for matching_option in matching_options]

                    node_dict['value_list']['options'].append(option_dict)

                ## sort the options by index, if screen type is sorting, then randomize the options
                if node.screen_type == 'sorting':
                    random.shuffle(node_dict['value_list']['options'])
                ## if screen type is slider, make sure the option with position = "left" is the first and position = "right" is the last
                elif node.screen_type == 'slider':
                    left_option = next((option for option in node_dict['value_list']['options'] if option['position'] == 'left'), None)
                    right_option = next((option for option in node_dict['value_list']['options'] if option['position'] == 'right'), None)
                    if left_option:
                        node_dict['value_list']['options'].remove(left_option)
                        node_dict['value_list']['options'].insert(0, left_option)
                    if right_option:
                        node_dict['value_list']['options'].remove(right_option)
                        node_dict['value_list']['options'].append(right_option)
                else:
                    node_dict['value_list']['options'] = sorted(node_dict['value_list']['options'], key=lambda x: x['index'])

            node_dict['character_ids'] = list(set(character_ids))

            ## get node branches
            branches = g.db_session.query(PublishedNodeBranch).filter(PublishedNodeBranch.published_node_id == node.id).all()
            node_dict['branches'] = []
            for branch in branches:
                branch_dict = {
                    "condition": branch.condition,
                    "condition_value": branch.condition_value,
                    "next_node_id": branch.next_node_id
                }
                node_dict['branches'].append(branch_dict)

            quest_dict['nodes'].append(node_dict)

        try:
            # Get previous_node_id for each node
            for node_dict in quest_dict['nodes']:
                node_id = node_dict['node_id']
                previous_node = next((node for node in quest_dict['nodes'] if node['next_node_id'] == node_id), None)
                if not previous_node:
                    previous_node = next((node for node in quest_dict['nodes'] if any(option['next_node_id'] == node_id for option in node.get('value_list', {}).get('options', []))), None)
                if not previous_node:
                    previous_node = next((node for node in quest_dict['nodes'] if any(branch['next_node_id'] == node_id for branch in node.get('branches', []))), None)
                node_dict['previous_node_id'] = previous_node['node_id'] if previous_node else None

            # Find the node without previous_node_id and move it to the first place
            first_node = next((node for node in quest_dict['nodes'] if node['previous_node_id'] is None), None)
            if first_node:
                quest_dict['nodes'].remove(first_node)
                quest_dict['nodes'].insert(0, first_node)
        except Exception as e:
            logging.error(e)

        return create_response('Quest found', data=quest_dict)
    

quest_model = app_quest_api.model('Quest', {
    'program_id': fields.String(required=True, description='The ID of the program'),
    'xperience_id': fields.String(required=True, description='The ID of the xperience'),
})


@app_quest_api.doc(security='bearer')
@app_quest_api.route('/<string:quest_id>/start')
@app_quest_api.param('quest_id', 'Quest ID')
class StartQuest(Resource):
    @app_quest_api.expect(quest_model)
    def post(self, quest_id):
        ## check if program_id or xperience_id is valid
        payload = request.json
        program_id = payload.get('program_id', '')
        xperience_id = payload.get('xperience_id', '')

        user_id = g.user_id  
        quest = g.db_session.query(Quest).filter_by(id=quest_id).first()
        if quest is None:
            return create_response('Quest not found', 404)
        
        # Check if the user already has a record for this quest
        existing_user_quest = g.db_session.query(UserQuest).filter_by(user_id=user_id, quest_id=quest_id).order_by(UserQuest.date_started.desc()).first()
        if existing_user_quest:
            user_quest = existing_user_quest
        else:
            # Record the start of the quest for the user
            user_quest = UserQuest(user_id=user_id, quest_id=quest_id, status='incomplete', date_started=datetime.datetime.utcnow(), process_percentage=0)
            g.db_session.add(user_quest)
            g.db_session.commit()

        ## if program id is provided, check if the user has started the program
        ## get all programs that the quest is associated with
        programs = []
        program_associations = g.db_session.query(Program).filter(Program.quests.any(Quest.id == quest_id)).all()
        for program in program_associations:
            program_id = program.id
            user_program = g.db_session.query(UserProgram).filter_by(user_id=user_id, program_id=program_id).order_by(UserProgram.date_started.desc()).first()
            if not user_program:
                new_user_program = UserProgram(user_id=user_id, program_id=program_id)
                new_user_program.date_started = datetime.datetime.utcnow()
                g.db_session.add(new_user_program)
                g.db_session.commit()

        ## if xperience id is provided, check if the user has started the xperience
        xperiences = []
        xperience_associations = g.db_session.query(Xperience).filter(Xperience.quests.any(Quest.id == quest_id)).all()
        for xperience in xperience_associations:
            xperience_id = xperience.id
            user_xperience = g.db_session.query(UserXperience).filter_by(user_id=user_id, xperience_id=xperience_id).order_by(UserXperience.date_started.desc()).first()
            if not user_xperience:
                new_user_xperience = UserXperience(user_id=user_id, xperience_id=xperience_id)
                new_user_xperience.date_started = datetime.datetime.utcnow()
                g.db_session.add(new_user_xperience)
                g.db_session.commit()

        ## get quest information
        quest_dict = quest.to_dict(["id", "name", "description", "image", "level", "learning_objective"])

        quest_dict['nodes'] = []
        ## get quest nodes from user node records
        user_nodes = g.db_session.query(UserNode).filter_by(user_id=user_id, quest_id=quest_id).order_by(UserNode.date_completed).all()
        for user_node in user_nodes:
            node_dict = {}
            node_dict['node_id'] = user_node.node_id
            node_dict['status'] = 'completed'
            node_dict['user_data'] = {
                'node_type': user_node.node_type,
                'value': user_node.value,
                'points': user_node.points,
                'duration': user_node.duration,
                'options': [],
                'previous_node_id': user_node.previous_node_id,
                'next_node_id': user_node.next_node_id
            }
            ## get options from raw_data if it's not empty
            if user_node.raw_data:
                raw_data = json.loads(user_node.raw_data)
                options = raw_data.get('options', [])
                node_dict['user_data']['options'] = options

            quest_dict['nodes'].append(node_dict)


        return create_response('Quest started', data=quest_dict)




## Add a user history api, which will return the user node history for a specific quest
@app_quest_api.doc(security='bearer')
@app_quest_api.route('/<string:quest_id>/history')
@app_quest_api.param('quest_id', 'Quest ID')
class QuestHistory(Resource):
    def get(self, quest_id):
        user_id = g.user_id
        quest = g.db_session.query(Quest).filter_by(id=quest_id).first()
        if quest is None:
            return create_response('Quest not found', 404)

        # Get the user node history for the quest
        user_node_history = g.db_session.query(UserNodeHistory).filter_by(user_id=user_id, quest_id=quest_id).order_by(UserNodeHistory.date_completed.desc()).all()

        history_list = []
        seen_node_ids = set()
        for history in reversed(user_node_history):  # reversed to get latest first
            if history.node_id in seen_node_ids:
                continue

            seen_node_ids.add(history.node_id)
            history_dict = {
                'node_id': history.node_id,
                'node_type': history.node_type,
                'value': history.value,
                'points': history.points,
                'duration': history.duration,
                'raw_data': json.loads(history.raw_data) if history.raw_data else {},
                'is_retry': history.is_retry,
                'date_created': history.date_completed.isoformat()
            }
            if history.previous_node_id:
                history_dict['previous_node_id'] = history.previous_node_id
            if history.next_node_id:
                history_dict['next_node_id'] = history.next_node_id
            history_list.append(history_dict)

        return create_response('User node history found', data=history_list)




@app_quest_api.doc(security='bearer')
@app_quest_api.route('/<string:quest_id>/complete')
@app_quest_api.param('quest_id', 'Quest ID')
class CompleteQuest(Resource):
    @app_quest_api.expect(quest_model)
    def post(self, quest_id):
        ## check if program_id or xperience_id is valid
        payload = request.json
        program_id = payload.get('program_id', '')
        xperience_id = payload.get('xperience_id', '')

        user_id = g.user_id
        quest = g.db_session.query(Quest).filter_by(id=quest_id).first()
        if quest is None:
            return create_response('Quest not found', 404)
        
        # Check if the user has started the quest
        # Get the latest record from user quest table
        latest_user_quest = g.db_session.query(UserQuest).filter_by(user_id=user_id, quest_id=quest_id).order_by(UserQuest.date_started.desc()).first()
        
        if latest_user_quest and latest_user_quest.status != 'completed':
            user_quest = latest_user_quest
        else:
            # Create a new user quest
            user_quest = UserQuest(user_id=user_id, quest_id=quest_id, status='incomplete')
            g.db_session.add(user_quest)
            g.db_session.commit()

        # Mark the quest as completed for the user
        user_quest.status = 'completed'
        user_quest.process_percentage = 100
        user_quest.date_completed = datetime.datetime.utcnow()
        g.db_session.commit()

        # Update user style (xip code) based on quest completion
        style_update_result = update_user_style_on_quest_completion(quest_id, user_id)

        ## count rewards and update stats in user stats
        rewards = {
            "chests": [],
            "badges": []
        }
        
        # Add style update info to rewards if applicable
        if style_update_result:
            rewards["style_update"] = style_update_result

        ## get quest chest
        chest_id = quest.chest_id
        chest_dict = get_chest(chest_id=chest_id, source_id=quest_id, source_type='quest')
        if chest_dict:
            rewards['chests'].append(chest_dict)


        ## check if the program is completed
        programs = []
        ## get all the programs that the quest is associated with
        program_associations = g.db_session.query(Program).filter(Program.quests.any(Quest.id == quest_id)).all()
        for program in program_associations:
            program_id = program.id
            program_quests = program.quests
            completed_quests = g.db_session.query(UserQuest).filter_by(user_id=user_id, status='completed').filter(UserQuest.quest_id.in_([q.id for q in program_quests])).all()
            
            completed_quest_ids = [uq.quest_id for uq in completed_quests]
            program_quest_ids = [q.id for q in program_quests]

            ## remove duplicates
            completed_quest_ids = list(set(completed_quest_ids))
            program_quest_ids = list(set(program_quest_ids))

            ## update user program status and process percentage
            user_program = g.db_session.query(UserProgram).filter_by(user_id=user_id, program_id=program_id).order_by(UserProgram.date_started.desc()).first()
            if user_program:
                user_program.process_percentage = (len(completed_quest_ids) / len(program_quest_ids)) * 100
            else:
                user_program = UserProgram(user_id=user_id, program_id=program_id)
                user_program.process_percentage = (len(completed_quest_ids) / len(program_quest_ids)) * 100
                g.db_session.add(user_program)

            print(f"program_id: {program_id}, user_id: {user_id}, process_percentage: {user_program.process_percentage}")

            user_program.date_updated = datetime.datetime.utcnow()

            if all(quest_id in completed_quest_ids for quest_id in program_quest_ids):
                user_program.process_percentage = 100
                user_program.status = 'completed'
                user_program.date_completed = datetime.datetime.utcnow()

                program_badge = check_badge("program", "", source='program', source_id=program_id)
                if program_badge:
                    if isinstance(program_badge, list):
                        rewards['badges'].extend(program_badge)
                    else:
                        rewards['badges'].append(program_badge)

                first_program_badge = check_badge("", "first_program")
                if first_program_badge:
                    rewards['badges'].append(first_program_badge)

                program_chest_id = program.chest_id
                
                if program_chest_id:
                    chest_id = program_chest_id
                    chest_dict = get_chest(chest_id=chest_id, source_id=program_id, source_type='program')
                    if chest_dict:
                        rewards['chests'].append(chest_dict)

                ## if the program is finished, calculate the time spent on the program
                ## sort the completed quests by date started
                unique_completed_quests = {quest.quest_id: quest for quest in completed_quests}.values()
                unique_completed_quests = sorted(unique_completed_quests, key=lambda x: x.date_completed)
        
                user_program.time_spent = 0
                for quest in unique_completed_quests:
                    time_spent = quest.time_spent
                    user_program.time_spent = user_program.time_spent + time_spent if user_program.time_spent else time_spent


        ## check if the xperience is completed
        xperiences = []
        ## get all the xperiences that the quest is associated with
        xperience_associations = g.db_session.query(Xperience).filter(Xperience.quests.any(Quest.id == quest_id)).all()
        for xperience in xperience_associations:
            xperience_id = xperience.id
            xperience_quests = xperience.quests
            completed_quests = g.db_session.query(UserQuest).filter_by(user_id=user_id, status='completed').filter(UserQuest.quest_id.in_([q.id for q in xperience_quests])).all()

            completed_quest_ids = [uq.quest_id for uq in completed_quests]
            xperience_quest_ids = [q.id for q in xperience_quests]

            ## remove duplicates
            completed_quest_ids = list(set(completed_quest_ids))
            xperience_quest_ids = list(set(xperience_quest_ids))

            ## update user xperience status and process percentage
            user_xperience = g.db_session.query(UserXperience).filter_by(user_id=user_id, xperience_id=xperience_id).order_by(UserXperience.date_started.desc()).first()
            if user_xperience:
                user_xperience.process_percentage = (len(completed_quest_ids) / len(xperience_quest_ids)) * 100
            else:
                user_xperience = UserXperience(user_id=user_id, xperience_id=xperience_id)
                user_xperience.process_percentage = (len(completed_quest_ids) / len(xperience_quest_ids)) * 100
                g.db_session.add(user_xperience)

            user_xperience.date_updated = datetime.datetime.utcnow()

            print(f"xperience_id: {xperience_id}, user_id: {user_id}, process_percentage: {user_xperience.process_percentage}")

            if all(quest_id in completed_quest_ids for quest_id in xperience_quest_ids):
                user_xperience.process_percentage = 100
                user_xperience.status = 'completed'
                user_xperience.date_completed = datetime.datetime.utcnow()

                ## if the xperience is finished, then unlock it
                user_xperience.is_unlocked = True

                xperience_badge = check_badge("xperience", "", source='xperience', source_id=xperience_id)
                if xperience_badge:
                    if isinstance(xperience_badge, list):
                        rewards['badges'].extend(xperience_badge)
                    else:
                        rewards['badges'].append(xperience_badge)

                first_xprience_badge = check_badge("", "first_xperience")
                if first_xprience_badge:
                    rewards['badges'].append(first_xprience_badge)

                xperience_chest_id = xperience.chest_id
                if xperience_chest_id:
                    chest_id = xperience_chest_id
                    chest_dict = get_chest(chest_id=chest_id, source_id=xperience_id, source_type='xperience')
                    if chest_dict:
                        rewards['chests'].append(chest_dict)

                ## if the xperience is finished, calculate the time spent on the xperience
                ## sort the completed quests by date started
                unique_completed_quests = {quest.quest_id: quest for quest in completed_quests}.values()
                unique_completed_quests = sorted(unique_completed_quests, key=lambda x: x.date_completed)

                user_xperience.time_spent = 0
                for quest in unique_completed_quests:
                    time_spent = quest.time_spent
                    user_xperience.time_spent = user_xperience.time_spent + time_spent if user_xperience.time_spent else time_spent

        # Record all the rewards into the user quest table
        user_quest.reward = json.dumps(rewards)
        g.db_session.commit()
        
        return create_response('Quest completed', data={'quest_id': quest_id, 'user_id': user_id}, rewards=rewards)
    


@app_quest_api.doc(security='bearer')
@app_quest_api.route('/<string:quest_id>/restart')
@app_quest_api.param('quest_id', 'Quest ID')
class RestartQuest(Resource):
    def post(self, quest_id):
        user_id = g.user_id
        quest = g.db_session.query(Quest).filter_by(id=quest_id).first()
        if quest is None:
            return create_response('Quest not found', 404)
        
        # Create a new user quest
        user_quest = UserQuest(user_id=user_id, quest_id=quest_id, status='incomplete', date_started=datetime.datetime.utcnow(), process_percentage=0)
        g.db_session.add(user_quest)
        g.db_session.commit()

        ## check if user has incomplete user xperience of user program, if no then create one
        xperience_associations = g.db_session.query(Xperience).filter(Xperience.quests.any(Quest.id == quest_id)).all()
        for xperience in xperience_associations:
            xperience_id = xperience.id
            user_xperience = g.db_session.query(UserXperience).filter_by(user_id=user_id, xperience_id=xperience_id).filter(UserXperience.status != 'completed').order_by(UserXperience.date_started.desc()).first()
            if not user_xperience:
                new_user_xperience = UserXperience(user_id=user_id, xperience_id=xperience_id)
                new_user_xperience.date_started = datetime.datetime.utcnow()
                g.db_session.add(new_user_xperience)
                g.db_session.commit()

        program_associations = g.db_session.query(Program).filter(Program.quests.any(Quest.id == quest_id)).all()
        for program in program_associations:
            program_id = program.id
            user_program = g.db_session.query(UserProgram).filter_by(user_id=user_id, program_id=program_id).filter(UserProgram.status != 'completed').order_by(UserProgram.date_started.desc()).first()
            if not user_program:
                new_user_program = UserProgram(user_id=user_id, program_id=program_id)
                new_user_program.date_started = datetime.datetime.utcnow()
                g.db_session.add(new_user_program)
                g.db_session.commit()

        # remove all the previous quest node records
        g.db_session.query(UserNode).filter_by(user_id=user_id, quest_id=quest_id).delete()
        g.db_session.commit()

        return create_response('Quest Restart', data={'quest_id': quest_id, 'user_id': user_id})



user_node_model = app_quest_api.model('UserNode', {
    'quest_id': fields.String(required=True, description='The ID of the quest'),
    'node_id': fields.String(required=True, description='The ID of the node'),
    'node_type': fields.String(required=True, description='The type of the node'),
    'value': fields.String(required=False, description='The value associated with the node'),
    'points': fields.Integer(required=True, description='The points awarded for the node'),
    'duration': fields.Integer(required=True, description='How many seconds the user takes to finish this node'),
    'options': fields.List(fields.Raw, required=False, description='The options from the node and the user\'s selections'),
    'previous_node_id': fields.String(required=True, description='The ID of the previous node'),
    'next_node_id': fields.String(required=True, description='The ID of the next node'),
    'is_retry': fields.Boolean(required=False, description='Indicates if the user clicked \'submit\' but the answer was wrong, requiring a retry', default=False)
})


@app_quest_api.doc(security='bearer')
@app_quest_api.route('/<string:quest_id>/<string:node_id>/done')
@app_quest_api.param('quest_id', 'Quest ID')
@app_quest_api.param('node_id', 'Node ID')
class DoneNode(Resource):
    @app_quest_api.expect(user_node_model)
    def post(self, quest_id, node_id):
        user_id = g.user_id
        payload = request.json
        node = g.db_session.query(Node).filter_by(id=node_id, quest_id=quest_id).first()
        if node is None:
            return create_response('Node not found', 404)
        
        options = payload.get('options', [])
        raw_data = {
            "options": options
        }
        is_retry = payload.get('is_retry', False)

        user_node_history = UserNodeHistory(
            user_id=user_id,
            quest_id=quest_id,
            node_id=node_id,
            node_type=payload.get('node_type', ''),
            value=payload.get('value', ''),
            points=payload.get('points', 0),
            duration=payload.get('duration', 0),
            raw_data=json.dumps(raw_data),
            is_retry=is_retry
        )

        # Verify if previous_node_id is valid
        previous_node_id = payload.get('previous_node_id', '')
        if previous_node_id:
            previous_node = g.db_session.query(Node).filter_by(id=previous_node_id, quest_id=quest_id).first()
            if previous_node is None:
                previous_node_id = ""

        # Verify if next_node_id is valid
        next_node_id = payload.get('next_node_id', '')
        if next_node_id:
            next_node = g.db_session.query(Node).filter_by(id=next_node_id, quest_id=quest_id).first()
            if next_node is None:
                next_node_id = ""

        if previous_node_id:
            user_node_history.previous_node_id = previous_node_id

        if next_node_id:
            user_node_history.next_node_id = next_node_id

        try:
            g.db_session.add(user_node_history)
            g.db_session.commit()
        except Exception as e:
            print(payload)

        if is_retry:
            return create_response('Node recorded', data={'quest_id': quest_id, 'node_id': node_id, 'user_id': user_id})

        existing_user_node = g.db_session.query(UserNode).filter_by(user_id=user_id, quest_id=quest_id, node_id=node_id).first()
        if existing_user_node:
            user_node = existing_user_node
        else:
            user_node = UserNode(user_id=user_id, quest_id=quest_id, node_id=node_id)

        user_node.node_type = payload.get('node_type', '')
        user_node.value = payload.get('value', '')
        user_node.points = payload.get('points', 0)
        user_node.duration = payload.get('duration', 0)
        user_node.raw_data = json.dumps(raw_data)
        if previous_node_id:
            user_node.previous_node_id = previous_node_id

        if next_node_id:
            user_node.next_node_id = next_node_id

        try:
            g.db_session.add(user_node)
            g.db_session.commit()
        except Exception as e:
            print(payload)

        ## record the process percentage into user quest table
        total_nodes = g.db_session.query(PublishedNode).filter_by(quest_id=quest_id, is_latest=True).count()
        completed_nodes = g.db_session.query(UserNode).filter_by(user_id=user_id, quest_id=quest_id).count()
        process_percentage = (completed_nodes / total_nodes) * 100 if total_nodes > 0 else 0

        user_quest = g.db_session.query(UserQuest).filter_by(user_id=user_id, quest_id=quest_id, status='incomplete').order_by(UserQuest.date_started.desc()).first()
        if user_quest:
            user_quest.process_percentage = process_percentage

            ## we will calculate the time spending on the quest
            if previous_node_id:
                previous_user_node = g.db_session.query(UserNode).filter_by(user_id=user_id, quest_id=quest_id, node_id=previous_node_id).first()
                if previous_user_node:
                    previous_node_completion_time = previous_user_node.date_completed
                    time_spent = datetime.datetime.utcnow() - previous_node_completion_time
                    ## if time_spent is more than 5 mins, then just keep it as 5 mins
                    time_spent = 300 if time_spent.total_seconds() > 300 else time_spent.total_seconds()
                    user_quest.time_spent = user_quest.time_spent + time_spent if user_quest.time_spent else time_spent

            ## update last modified date
            user_quest.date_updated = datetime.datetime.utcnow()

            g.db_session.commit()

        return create_response('Node completed', data={'quest_id': quest_id, 'node_id': node_id, 'user_id': user_id})


@app_quest_api.doc(security='bearer')
@app_quest_api.route('/<string:quest_id>/<string:node_id>/reset')
@app_quest_api.param('quest_id', 'Quest ID')
@app_quest_api.param('node_id', 'Node ID')
class ResetNode(Resource):
    def post(self, quest_id, node_id):
        user_id = g.user_id
        node = g.db_session.query(Node).filter_by(id=node_id, quest_id=quest_id).first()
        if node is None:
            return create_response('Node not found', 404)

        # Remove the user node data
        g.db_session.query(UserNode).filter_by(user_id=user_id, quest_id=quest_id, node_id=node_id).delete()
        g.db_session.commit()

        return create_response('Node reset', data={'quest_id': quest_id, 'node_id': node_id, 'user_id': user_id})


