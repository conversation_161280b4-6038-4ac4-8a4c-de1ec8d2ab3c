from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from app import cache
from api.common.helper import create_response
from clientmodels import Store

cms_stores_api = Namespace('api_cms_store', description='Store related operations')

@cms_stores_api.doc(security='bearer')
@cms_stores_api.route('/list')
class ListStores(Resource):
    def get(self):
        stores = g.db_session.query(Store).all()
        data = [store.to_dict() for store in stores]
        return create_response("Stores retrieved successfully", data=data)



store_item_model = cms_stores_api.model('Store', {
    'name': fields.String(required=True, description='Store name'),
    'description': fields.String(required=True, description='Store description'),
    'image': fields.String(required=True, description='Store image'),
    'price': fields.Float(required=True, description='Store price'),
    'unit': fields.String(required=True, description='Store unit'),
    'store_type': fields.String(required=True, description='Store type', enum=["digital_assets", "add_ons", "currency"]),
    'asset_id': fields.String(required=True, description='Asset ID'),
    'button_label': fields.String(required=True, description='Button label'),
    'item_type': fields.String(required=True, description='Item type', enum=["boost", "powerup"]),
    'index': fields.Integer(required=True, description='Index', default=0),
    'config': fields.Raw(required=True, description='Config as JSON')
})

@cms_stores_api.doc(security='bearer')
@cms_stores_api.route('/<string:store_id>', methods=['GET', 'PUT', 'DELETE'])
@cms_stores_api.route('/', methods=['POST'])
class GetStore(Resource):
    def get(self, store_id):
        store = g.db_session.query(Store).filter_by(id=store_id).first()
        if not store:
            return create_response("Store not found", status=404)
        return create_response("Store retrieved successfully", data=store.to_dict())
    
    @cms_stores_api.expect(store_item_model)
    @cache.invalidate_cache(pattern='store:list')
    def put(self, store_id):
        store = g.db_session.query(Store).filter_by(id=store_id).first()
        if not store:
            return create_response("Store not found", status=404)
        
        data = json.loads(request.data)
        store.name = data.get('name', "")
        store.description = data.get('description', "")
        store.image = data.get('image', "")
        store.price = data.get('price', 0)
        store.unit = data.get('unit', "")
        store.store_type = data.get('store_type', "")
        store.asset_id = data.get('asset_id', "")
        store.button_label = data.get('button_label', "")
        store.item_type = data.get('item_type', "")
        store.index = data.get('index', 0)
        store.config = json.dumps(data.get('config', {}))

        g.db_session.commit()
        
        return create_response("Store updated successfully", data=store.to_dict())
    
    @cms_stores_api.expect(store_item_model)
    @cache.invalidate_cache(pattern='store:list')
    def post(self):
        data = json.loads(request.data)

        store = Store()
        store.name = data.get('name', "")
        store.description = data.get('description', "")
        store.image = data.get('image', "")
        store.price = data.get('price', 0)
        store.unit = data.get('unit', "")
        store.store_type = data.get('store_type', "")
        store.asset_id = data.get('asset_id', "")
        store.button_label = data.get('button_label', "")
        store.item_type = data.get('item_type', "")
        store.index = data.get('index', 0)
        store.config = json.dumps(data.get('config', {}))
        
        g.db_session.add(store)
        g.db_session.commit()

        return create_response("Store created successfully", data=store.to_dict())
    
    @cache.invalidate_cache(pattern='store:list')
    def delete(self, store_id):
        store = g.db_session.query(Store).filter_by(id=store_id).first()
        if not store:
            return create_response("Store not found", status=404)
        g.db_session.delete(store)
        g.db_session.commit()

        return create_response("Store deleted successfully")