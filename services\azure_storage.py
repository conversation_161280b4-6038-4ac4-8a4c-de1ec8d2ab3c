import logging
import mimetypes
from datetime import datetime, timedelta

from azure.core.exceptions import ResourceNotFoundError
from azure.storage.blob import BlobServiceClient, ContentSettings, generate_blob_sas, BlobSasPermissions

from services.azure_key_vault import AzureKeyVault
from api.common.storage import StorageProvider

logger = logging.getLogger(__name__)

class AzureStorage(StorageProvider):
    def __init__(self):
        try:
            self.connection_string = AzureKeyVault().get_secret("azure-storage-connection-string")
            self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        except Exception as e:
            logger.error(f"Failed to initialize Azure Storage: {str(e)}", exc_info=True)
            raise e
        
        self._container_name = AzureKeyVault().get_secret("azure-storage-container-name")
        self._container_name_override = None  # For explicit override

    @property
    def container_name(self):
        if self._container_name_override is not None:
            return self._container_name_override
        return self._container_name
    
    @container_name.setter
    def container_name(self, value):
        """
        Sets an override for the container name.
        If set to None, the session-based retrieval or default will be used.
        """
        self._container_name_override = value

    def blob_exists(self, container_name, blob_name):
        container_name = self.container_name + '/' + container_name
        try:
            blob_client = self.blob_service_client.get_blob_client(container_name, blob_name)
            return blob_client.exists()
        except Exception as e:
            logger.error(f"Error checking blob existence - container: {container_name}, blob: {blob_name}: {str(e)}")
            return False

    def upload_blob(self, container_name, blob_name, data):
        container_name = self.container_name + '/' + container_name
        try:
            blob_client = self.blob_service_client.get_blob_client(container_name, blob_name)
            content_type, _ = mimetypes.guess_type(blob_name)
            if content_type is None:  # If the type could not be guessed
                content_type = 'application/octet-stream'  # Use a default type
            blob_client.upload_blob(data, blob_type="BlockBlob", content_settings=ContentSettings(content_type=content_type), overwrite=True)
            logger.info(f"Successfully uploaded blob - container: {container_name}, blob: {blob_name}")
            return blob_name
        except ResourceNotFoundError:
            logger.error(f"Container not found - container: {container_name}, blob: {blob_name}")
            return None
        except Exception as e:
            logger.error(f"Error uploading blob - container: {container_name}, blob: {blob_name}: {str(e)}")
            return None

    def download_blob(self, container_name, blob_name):
        container_name = self.container_name + '/' + container_name
        try:
            blob_client = self.blob_service_client.get_blob_client(container_name, blob_name)
            blob_data = blob_client.download_blob()
            logger.info(f"Successfully downloaded blob - container: {container_name}, blob: {blob_name}")
            return blob_data
        except ResourceNotFoundError:
            logger.warning(f"Blob not found - container: {container_name}, blob: {blob_name}")
            return None
        except Exception as e:
            logger.error(f"Error downloading blob - container: {container_name}, blob: {blob_name}: {str(e)}")
            return None
    
    def delete_blob(self, container_name, blob_name):
        container_name = self.container_name + '/' + container_name
        try:
            blob_client = self.blob_service_client.get_blob_client(container_name, blob_name)
            blob_client.delete_blob()
            logger.info(f"Successfully deleted blob - container: {container_name}, blob: {blob_name}")
            return True
        except ResourceNotFoundError:
            logger.warning(f"Blob not found - container: {container_name}, blob: {blob_name}")
            return False
        except Exception as e:
            logger.error(f"Error deleting blob - container: {container_name}, blob: {blob_name}: {str(e)}")
            return False

    def move_blob(self, source_container_name, source_blob_name, destination_container_name, destination_blob_name):
        source_container_name = self.container_name + '/' + source_container_name
        destination_container_name = self.container_name + '/' + destination_container_name
        
        try:
            source_blob = self.blob_service_client.get_blob_client(source_container_name, source_blob_name)
            destination_blob = self.blob_service_client.get_blob_client(destination_container_name, destination_blob_name)
            destination_blob.start_copy_from_url(source_blob.url)
            source_blob.delete_blob()
            logger.info(f"Successfully moved blob from {source_container_name}/{source_blob_name} to {destination_container_name}/{destination_blob_name}")
            return destination_blob_name
        except ResourceNotFoundError:
            logger.error(f"Container or blob not found - source: {source_container_name}/{source_blob_name}, destination: {destination_container_name}/{destination_blob_name}")
            return None
        except Exception as e:
            logger.error(f"Error moving blob - source: {source_container_name}/{source_blob_name}, destination: {destination_container_name}/{destination_blob_name}: {str(e)}")
            return None
        
    def copy_blob(self, source_container_name, source_blob_name, destination_container_name, destination_blob_name):
        source_container_name = self.container_name + '/' + source_container_name
        destination_container_name = self.container_name + '/' + destination_container_name
        
        try:
            source_blob = self.blob_service_client.get_blob_client(source_container_name, source_blob_name)
            destination_blob = self.blob_service_client.get_blob_client(destination_container_name, destination_blob_name)
            destination_blob.start_copy_from_url(source_blob.url)
            logger.info(f"Successfully copied blob from {source_container_name}/{source_blob_name} to {destination_container_name}/{destination_blob_name}")
            return destination_blob_name
        except ResourceNotFoundError:
            logger.error(f"Container or blob not found - source: {source_container_name}/{source_blob_name}, destination: {destination_container_name}/{destination_blob_name}")
            return None
        except Exception as e:
            logger.error(f"Error copying blob - source: {source_container_name}/{source_blob_name}, destination: {destination_container_name}/{destination_blob_name}: {str(e)}")
            return None

    def list_blobs(self, container_name):
        """List all blobs in a container/folder"""
        try:
            container_client = self.blob_service_client.get_container_client(self.container_name)
            blobs = []
            for blob in container_client.list_blobs(name_starts_with=container_name):
                blobs.append({
                    'name': blob.name,
                    'size': blob.size,
                    'last_modified': blob.last_modified,
                    'content_type': blob.content_settings.content_type if blob.content_settings else None
                })
            logger.info(f"Successfully listed {len(blobs)} blobs in container: {container_name}")
            return blobs
        except ResourceNotFoundError:
            logger.warning(f"Container not found - container: {container_name}")
            return []
        except Exception as e:
            logger.error(f"Error listing blobs - container: {container_name}: {str(e)}")
            return []
            
    def get_signed_url(self, container_name, blob_name, expiry_minutes=60):
        """Generate a signed URL for a blob with expiry time
        
        Args:
            container_name: Container path
            blob_name: The blob name
            expiry_minutes: Expiry time in minutes (default: 60)
            
        Returns:
            str: Signed URL if successful, None otherwise
        """
        container_name = self.container_name + '/' + container_name 
        try:
            blob_client = self.blob_service_client.get_blob_client(container_name, blob_name)
            if not blob_client.exists():
                logger.warning(f"Blob not found for signed URL - container: {container_name}, blob: {blob_name}")
                return None
            account_name = self.blob_service_client.account_name
            permissions = BlobSasPermissions(read=True)
            expiry_time = datetime.utcnow() + timedelta(minutes=expiry_minutes)
            sas_token = generate_blob_sas(
                account_name=account_name,
                container_name=container_name,
                blob_name=blob_name,
                account_key=self.blob_service_client.credential.account_key,
                permission=permissions,
                expiry=expiry_time
            )
            blob_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
            logger.info(f"Successfully generated signed URL for blob: {container_name}/{blob_name}")
            return blob_url
        except ResourceNotFoundError:
            logger.warning(f"Container or blob not found - container: {container_name}, blob: {blob_name}")
            return None
        except Exception as e:
            logger.error(f"Error generating signed URL - container: {container_name}, blob: {blob_name}: {str(e)}")
            return None
