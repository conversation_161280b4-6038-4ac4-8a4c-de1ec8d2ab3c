import logging
import os
from bs4 import BeautifulSoup
from azure.communication.email import EmailClient
from services.azure_key_vault import AzureKeyVault

logger = logging.getLogger(__name__)


class EmailService:
    def __init__(self):
        self.email_service = os.environ.get('EMAIL_SERVICE', 'azure')
        self.base_url = os.environ.get('BASE_URL', 'https://go.xapa.ai')
        if self.email_service == 'azure':
            # Initialize Azure Communication Email client
            connection_string = AzureKeyVault().get_secret('azure-communication-connection-string')
            self.email_client = EmailClient.from_connection_string(connection_string)
            self.default_sender = os.environ.get('EMAIL_SENDER', '<EMAIL>')
        else:
            logger.error('Invalid email service')
            self.email_client = None

    def send_email(self, to_email, subject, content, **kwargs):
        """Send email using Azure Communication Services"""
        try:
            if not self.email_client:
                logger.error('Email client not initialized')
                return None

            to_emails = kwargs.get('to_emails', None)
            sender = kwargs.get('sender', self.default_sender)
            
            recipients = []
            
            # Handle multiple recipients
            if to_emails:
                for email in to_emails:
                    recipients.append({
                        'address': email,
                        'displayName': email
                    })

            # Handle single recipient
            if to_email:
                recipients.append({
                    'address': to_email,
                    'displayName': to_email
                })

            # Remove duplicates
            recipients = [dict(t) for t in {tuple(d.items()) for d in recipients}]

            if not recipients or not subject or not content:
                logger.error('Recipients, subject, and content are required')
                return None

            # Force [XAPA] specification in subject if not already present
            if not subject.startswith('[XAPA]'):
                subject = f'[XAPA] {subject}'

            # Prepare email content
            if self._is_html(content):
                plain_text_content = BeautifulSoup(content, 'html.parser').get_text()
                email_content = {
                    'subject': subject,
                    'plainText': plain_text_content,
                    'html': content
                }
            else:
                email_content = {
                    'subject': subject,
                    'plainText': content,
                    'html': f'<html><head></head><body>{content}</body></html>'
                }

            # Prepare email message
            email_message = {
                'content': email_content,
                'recipients': {
                    'to': recipients
                },
                'senderAddress': sender
            }

            # Send email
            response = self.email_client.begin_send(email_message)
            logger.info(f'Email sent successfully to {[r["address"] for r in recipients]}')
            return response

        except Exception as e:
            logger.error(f'Error sending email: {str(e)}')
            return None

    def send_invitation_email(self, email, first_name, last_name):
        """Send invitation email using HTML template"""
        try:
            if not email:
                logger.error('User email is required')
                return None
            
            # Load invitation email template from local file
            template_path = 'static/files/invitation_email_template.html'
            
            try:
                with open(template_path, 'r', encoding='utf-8') as template_file:
                    html_body = template_file.read()
            except FileNotFoundError:
                logger.error(f'Invitation email template not found at {template_path}')
                return None
            
            # Default subject
            email_subject = 'Welcome to XAPA - Your Journey Begins!'
            
            # Prepare user name
            user_name = f"{first_name} {last_name}".strip()
            if not user_name:
                user_name = email.split('@')[0]  # Use email prefix if no name
            
            # Replace all placeholders in template
            replacements = {
                '{{ user_name }}': user_name,
                '{{ user_email }}': email
            }
            
            # Apply replacements to HTML body
            for placeholder, value in replacements.items():
                html_body = html_body.replace(placeholder, str(value))
            
            # Send email using Azure Communication Services
            response = self.send_email(
                to_email=email,
                subject=email_subject,
                content=html_body,
                sender=self.default_sender
            )
            
            logger.info(f'Invitation email sent to {email}')
            return response

        except Exception as e:
            logger.error(f'Error sending invitation email: {str(e)}')
            return None

    def send_notification_email(self, email, first_name, subject, content, data):
        """Send notification email to user (preferences already checked)"""
        try:
            # Load notification email template from local file
            template_path = 'static/files/notification_email_template.html'
            
            try:
                with open(template_path, 'r', encoding='utf-8') as template_file:
                    html_body = template_file.read()
            except FileNotFoundError:
                logger.error(f'Notification email template not found at {template_path}')
                return None
            
            # Use the provided subject or default
            email_subject = subject or 'Notification from Xapa'
            
            # Generate notification deep link from data
            notification_data = data or {}
            
            if 'action' in notification_data:
                notification_link = f"{self.base_url}/#{notification_data['action']}"
            else:
                notification_link = self.base_url
            
            # Create unsubscribe link
            unsubscribe_link = f'{self.base_url}/#/edit-profile'
            
            # Replace all placeholders in template
            replacements = {
                '{{ firstName }}': first_name or 'User',
                '{{ notificationTitle }}': subject or '',
                '{{ notificationBody }}': content or '',
                '{{ clickHereLink }}': notification_link,
                '{{ unsubscribeLink }}': unsubscribe_link,
            }
            
            # Apply replacements to HTML body
            for placeholder, value in replacements.items():
                html_body = html_body.replace(placeholder, str(value))
            
            # Extract subject from HTML template title or use provided subject
            if '{{ notificationTitle }}' in email_subject:
                email_subject = email_subject.replace('{{ notificationTitle }}', subject or 'Notification from Xapa')
            
            # Send email using Azure Communication Services
            response = self.send_email(
                to_email=email,
                subject=email_subject,
                content=html_body,
                sender=self.default_sender
            )
            
            logger.info(f'Notification email sent to {email}')
            return response
            
        except Exception as e:
            logger.error(f'Error sending notification email: {str(e)}')
            return None

    def _is_html(self, text):
        """Check if text contains HTML"""
        soup = BeautifulSoup(text, 'html.parser')
        return bool(soup.find())
