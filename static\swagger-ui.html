{% extends "base.html" %}

{% block head %}
    {{ super() }}
    <link media="screen" rel="stylesheet" type="text/css" href="/swaggerui/droid-sans.css" />
    <link rel="stylesheet" type="text/css" href="/swaggerui/swagger-ui.css">
    <link rel="icon" type="image/png" href="/swaggerui/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="/swaggerui/favicon-16x16.png" sizes="16x16" />

    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
    </style>
{% endblock %}

{% block content %}
<div id="swagger-ui"></div>
<script src="/swaggerui/swagger-ui-bundle.js"></script>
<script src="/swaggerui/swagger-ui-standalone-preset.js"></script>
<script>
    window.onload = function() {
        {% if session.get('user_info') %}
        const token = "{{ session.get('user_info').get('access_token') }}";
        console.log(token)
        {% else %}
        const token = null;
        {% endif %}

        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        console.log("User's Timezone:", userTimezone);

        json_url = window.location.pathname.replace("/document/", "/swagger.json")
        const ui = window.ui = new SwaggerUIBundle({
            url: json_url,
            
            validatorUrl: "" || null,
            dom_id: "#swagger-ui",
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIStandalonePreset.slice(1) // No Topbar
            ],
            plugins: [
                SwaggerUIBundle.plugins.DownloadUrl
            ],
            
            displayOperationId: false,
            displayRequestDuration: false,
            docExpansion: "none",
            requestInterceptor: (req) => {
                if (!req.headers['Authorization'] && token) {
                    req.headers['Authorization'] = `Bearer ${token}`;
                    req.headers['X-Timezone'] = userTimezone;
                }
                return req;
            }
        })

        // Set the token in the authorization window
        if (token) {
            ui.authActions.authorize({
                Bearer: {
                    name: "Bearer",
                    schema: {
                        type: "apiKey",
                        in: "header",
                        name: "Authorization",
                        description: ""
                    },
                    value: `Bearer ${token}`
                }
            });
        }

        window.ui = ui;
    }
</script>
{% endblock %}

