import datetime
import logging
from typing import Dict, Any, Optional

from flask import g

from api.common.publish_utils import publish_xapa_feed
from clientmodels import Task, Feed, Xircle, get_db_session

logger = logging.getLogger(__name__)

class TaskFunction:
    def create_task(
        self,
        name: str, 
        func: str, 
        data: Dict[Any, Any], 
        scheduled_for: Optional[datetime.datetime] = None,
    ) -> Optional[Task]:
        """Create a new task and enqueue it to Azure Queue Storage.
        
        Args:
            name: Name of the task
            func: Function name to be executed
            data: Task data in dictionary format
            scheduled_for: Optional datetime for task scheduling
        
        Returns:
            Task object if successful, None otherwise
        """
        try:
            task = Task()
            task.name = name
            task.func = func
            task.data = data
            task.scheduled_for = scheduled_for
            g.db_session.add(task)
            g.db_session.flush()  # Get the task ID before commit
            
            if scheduled_for:
                # Validate scheduled time
                current_time = datetime.datetime.utcnow()
                if scheduled_for < current_time:
                    raise ValueError("Cannot schedule task in the past")

            g.db_session.commit()
            
            return task
            
        except Exception as e:
            logger.error(f"Failed to create task: {str(e)}")
            g.db_session.rollback()
            return None

    def update_task(
        self,
        task_id: str, 
        name: Optional[str] = None, 
        func: Optional[str] = None, 
        data: Optional[Dict] = None, 
        scheduled_for: Optional[datetime.datetime] = None,
    ) -> bool:
        """Update an existing task and update queue if scheduled_for is changed.
        
        Args:
            task_id: ID of the task to update
            name: Optional new name
            func: Optional new function name
            data: Optional new task data
            scheduled_for: Optional new schedule time
        
        Returns:
            bool indicating success or failure
        """
        try:
            task = g.db_session.query(Task).filter(Task.id == task_id, Task.is_deleted == False).first()
            if not task:
                raise ValueError(f"Task {task_id} not found")
            
            # Update task fields if provided
            if name:
                task.name = name
            if func:
                task.func = func
            if data:
                task.data = data
                
            # If scheduled_for is updated, update queue
            if scheduled_for and scheduled_for != task.scheduled_for:
                current_time = datetime.datetime.utcnow()
                if scheduled_for < current_time:
                    raise ValueError("Cannot schedule task in the past")

            task.scheduled_for = scheduled_for
            
            g.db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to update task: {str(e)}")
            g.db_session.rollback()
            return False

    def delete_task(self, task_id: str) -> bool:
        """Soft delete a task and clear its queue_id.
        
        Args:
            task_id: ID of the task to delete
        
        Returns:
            bool indicating success or failure
        """
        try:
            task = g.db_session.query(Task).filter(Task.id == task_id, Task.is_deleted == False).first()
            if not task:
                raise ValueError(f"Task {task_id} not found")
            
            # Soft delete the task
            task.is_deleted = True
            g.db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete task: {str(e)}")
            g.db_session.rollback()
            return False

    def execute_task(self, task_id: str) -> bool:
        """Execute a task by its ID.
        
        Args:
            task_id: ID of the task to execute
        
        Returns:
            bool indicating success or failure
        """
        try:
            task = g.db_session.query(Task).filter(Task.id == task_id, Task.is_deleted == False).first()
            if not task:
                raise ValueError(f"Task {task_id} not found")
            
            # Get the function to execute
            func = getattr(self, task.func, None)
            if not func:
                raise ValueError(f"Function {task.func} not found in task object")
            
            if not callable(func):
                raise ValueError(f"Function {task.func} is not callable")
                
            # Execute the function with task data
            func(task.data)
            return True
            
        except Exception as e:
            logger.error(f"Failed to execute task: {str(e)}")
            g.db_session.rollback()
            return False

    def post_feed(
        self, 
        data: Dict[str, Any]
    ) -> bool:
        try:
            tenant_id = data.get('tenant_id')
            feed_id = data.get('feed_id')
            type = 'common_feed'
            type = 'common_feed'

            target_db_session = get_db_session(tenant_id)
            feed = target_db_session.query(Feed).filter(Feed.id == feed_id, Feed.is_deleted == False).first()
            if not feed:
                raise ValueError(f"Feed {feed_id} not found")
            
            xircle = target_db_session.query(Xircle).filter(Xircle.id == feed.xircle_id, Xircle.name.ilike('xapa'), Xircle.is_deleted == False).first()
            if xircle:
                type = 'xapa_feed'
            
            feed.status = 'active'
            feed.date_created = datetime.datetime.utcnow()
            target_db_session.commit()
            # Check if feed is xapa feed and tenant is global
             
            if type == 'xapa_feed' and tenant_id == 'global':
                publish_xapa_feed(feed.id)
            return True
        except Exception as e:
            logger.error(f"Failed to post feed: {str(e)}")
            target_db_session.rollback() 
            return False

    def delete_feed(
        self,  
        data: Dict[str, Any]
    ) -> bool:
        try:
            tenant_id = data.get('tenant_id')
            feed_id = data.get('feed_id')
            type = 'common_feed'

            target_db_session = get_db_session(tenant_id)
            feed = target_db_session.query(Feed).filter(Feed.id == feed_id, Feed.is_deleted == False).first()
            if not feed:
                raise ValueError(f"Feed {feed_id} not found") 
            
            xircle = target_db_session.query(Xircle).filter(Xircle.id == feed.xircle_id, Xircle.name.ilike('xapa'), Xircle.is_deleted == False).first()
            if xircle:
                type = 'xapa_feed' 
            
            feed.is_deleted = True
            target_db_session.commit()
            if type == 'xapa_feed' and tenant_id == 'global':
                publish_xapa_feed(feed.id)
            return True
        except Exception as e:
            logger.error(f"Failed to delete feed: {str(e)}")
            target_db_session.rollback() 
            return False
