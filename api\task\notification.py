import logging

from flask import g, request
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from api.common.notification import NotificationFunction
from clientmodels import get_db_session, Notification
from services.azure_queue import AzureQueue

logger = logging.getLogger(__name__)

task_notification_api = Namespace('task_notification_api', description='Task notification operations')

@task_notification_api.route('/execute', methods=['PUT'])
class NotificationObject(Resource):
    def put(self):
        """
        Process a notification task from the queue.
        Returns 200 for successful processing or when notification should be removed from queue.
        Returns 400 for invalid requests and 500 for processing errors.
        """
        try:
            # 1. Validate request
            data = request.json
            queue_id = request.headers.get('QueueId')

            if not queue_id:
                logger.error("No QueueId header found in request headers")
                return create_response("QueueId header is required", status=400)

            logger.error(f"api/task/notification received header: {queue_id}")

            if not data:
                logger.error("No data found in request body")
                return create_response("No data found in request body", status=400)
            
            logger.error(f"api/task/notification received data: {data}")

            # 2. Setup database session
            notification_id = data.get('notification_id')
            db_session = get_db_session('global')
            g.db_session = db_session

            # 3. Get notification
            notification = g.db_session.query(Notification).filter(
                Notification.id == notification_id
            ).first()

            # 4. Handle notification states
            # Case 1: Notification doesn't exist
            if not notification:
                logger.error(f"Notification not found - notification does not exist: {notification_id}")
                return create_response("Notification not found - notification does not exist", status=200)
            
            # Case 2: Notification already sent or failed
            if notification.status == 'sent':
                logger.error(f"Notification already sent - notification already sent: {notification_id}")
                return create_response("Notification already sent", status=200)
            
            if notification.status == 'failed':
                logger.error(f"Notification previously failed - notification previously failed: {notification_id}")
                return create_response("Notification previously failed", status=200)
            
            # Case 3: Notification is deleted but still in queue
            if notification.is_deleted and notification.queue_id is not None:
                queue = AzureQueue()
                queue.delete_message_by_id("notification-queue", notification.queue_id)
                logger.error(f"Notification not found - notification was deleted: {notification_id}")
                return create_response("Notification not found - notification was deleted", status=200)

            # Case 4: Queue message ID mismatch
            if notification.queue_id != queue_id:
                queue = AzureQueue()
                queue.delete_message_by_id("notification-queue", queue_id)
                logger.error(f"Notification not found - queue ID mismatch: {notification_id}")
                return create_response("Notification not found - queue ID mismatch", status=200)

            # 5. Process valid notification
            nf = NotificationFunction()
            result = nf.send_notification(notification_id)

            if not result:
                notification.status = 'failed'
                g.db_session.commit()
                logger.error(f"Failed to send notification: {notification_id}")
                return create_response("Failed to send notification", status=200)

            return create_response("Notification sent successfully", status=200)

        except Exception as e:
            logger.error(f"Error processing notification task: {str(e)}")
            return create_response(f"Send notification failed: {str(e)}", 500)
