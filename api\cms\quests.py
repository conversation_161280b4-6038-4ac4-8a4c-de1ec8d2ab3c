import datetime
from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from api.common.file import FileService
from api.common.helper import create_response
from clientmodels import Chest, ChestAssetAssociation, Asset
from clientmodels import Quest, QuestCategoryAssociation, QuestTagAssociation, QuestFacetAssociation, Category, Tag, \
    Facet, User
from clientmodels import QuestBadgeAssociation, Badge


cms_quests_api = Namespace('api_cms_quests', description='Quest related operations')

cms_quest_model = cms_quests_api.model('Quest', {
    'name': fields.String(required=True, description='The quest name'),
    'description': fields.String(required=False, description='The quest description'),
    'learning_objective': fields.String(required=False, description='The learning objective identifiers'),
    'level': fields.Integer(required=False, description='The quest level', default=1),
    'category_ids': fields.List(fields.String, required=False, description='The category identifiers'),
    'tag_ids': fields.List(fields.String, required=False, description='The tag identifiers'),
    'facet_ids': fields.List(fields.String, required=False, description='The facet identifiers'),
    'badge_ids': fields.List(fields.String, required=False, description='The badge identifiers'),
    'image': fields.String(required=False, description='The quest image'),
    'configs': fields.Nested(cms_quests_api.model('QuestConfig', {
        'components': fields.List(fields.Raw, required=False, description='The components of the quest')
    })),
    'chest': fields.Nested(cms_quests_api.model('QuestChest', {
        'xp': fields.Integer(required=False, description='The quest xp', default=0),
        'coins': fields.Integer(required=False, description='The quest coins', default=0),
        'gems': fields.Integer(required=False, description='The quest gems', default=0),
        'keys': fields.Integer(required=False, description='The quest keys', default=0),
        'asset_ids': fields.List(fields.String, required=False, description='The asset identifiers'),
        'button_label': fields.String(required=False, description='The button label'),
        'name': fields.String(required=False, description='The chest name'),
        'facet_xp': fields.List(fields.Nested(cms_quests_api.model('Facet', {
            'id': fields.String(required=True, description='The facet id'),
            'xp': fields.Integer(required=False, description='The facet xp', default=0)
        })))
    })),
    'status': fields.String(required=False, description='The quest status', default='Draft')
})

delete_parser = cms_quests_api.parser()
delete_parser.add_argument('force', type=bool, location='args', default=False, help='Force delete the quest')

@cms_quests_api.doc(security='bearer')
@cms_quests_api.route('/', methods=['POST'])
@cms_quests_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class QuestItem(Resource):
    @cms_quests_api.expect(cms_quest_model)
    def post(self):
        '''Create a new quest'''
        data = request.json
        name = data.get('name', '')
        if not name:
            return create_response('Name is required', 400)
        
        ## check if the quest already exists
        quest = g.client_db_session.query(Quest).filter(Quest.name == name).first()
        if quest:
            return create_response('Quest already exists', 400)

        description = data.get('description', '')
        learning_objective = data.get('learning_objective', '')
        level = data.get('level', 1)
        category_ids = data.get('category_ids', [])
        tag_ids = data.get('tag_ids', [])
        facet_ids = data.get('facet_ids', [])
        badge_ids = data.get('badge_ids', [])
        image = data.get('image', '')

        quest = Quest(
            name=name,
            description=description,
            learning_objective=learning_objective,
            level=level
        )

        g.client_db_session.add(quest)
        g.client_db_session.commit()

        ## save image if exists
        FileService.process_entity_image(quest, image, 'quest', quest.id)

        ## save categories
        for category_id in category_ids:
            category = g.client_db_session.query(Category).get(category_id)
            if category is not None:
                association = QuestCategoryAssociation(quest_id=quest.id, category_id=category_id)
                g.client_db_session.add(association)

        ## save tags
        for tag_id in tag_ids:
            tag = g.client_db_session.query(Tag).get(tag_id)
            if tag is not None:
                association = QuestTagAssociation(quest_id=quest.id, tag_id=tag_id)
                g.client_db_session.add(association)

        ## save facets
        for facet_id in facet_ids:
            facet = g.client_db_session.query(Facet).get(facet_id)
            if facet is not None:
                association = QuestFacetAssociation(quest_id=quest.id, facet_id=facet_id)
                g.client_db_session.add(association)

        ## save badges
        for badge_id in badge_ids:
            badge = g.client_db_session.query(Badge).get(badge_id)
            if badge is not None:
                association = QuestBadgeAssociation(quest_id=quest.id, badge_id=badge_id)
                g.client_db_session.add(association)

        ## if components exist, save them in quest config
        configs = data.get('configs', {})
        quest.configs = json.dumps(configs)

        ## add a chest item and save the chest id in quest
        chest = data.get('chest', {})
        if chest:
            xp = chest.get('xp', 0)
            coins = chest.get('coins', 0)
            gems = chest.get('gems', 0)
            keys = chest.get('keys', 0)
            asset_ids = chest.get('asset_ids', [])
            chest_name = chest.get('name', "YOUR REWARDS")
            button_label = chest.get('button_label', 'Claim Rewards')
            facets = chest.get('facet_xp', [])

            ## create chest
            chest = Chest(
                xp=xp,
                coins=coins,
                gems=gems,
                keys=keys,
                name=chest_name,
                button_label=button_label,
                facet_xp = json.dumps(facets)
            )
            g.client_db_session.add(chest)
            g.client_db_session.commit()

            ## save chest assets
            for asset_id in asset_ids:
                asset = g.client_db_session.query(Asset).get(asset_id)
                if asset is not None:
                    association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                    g.client_db_session.add(association)

            ## save chest id in quest
            quest.chest_id = chest.id

        g.client_db_session.add(quest)
        g.client_db_session.commit()

        data = {
            'id': quest.id
        }

        return create_response('Quest created successfully', data=data)
    
    def get(self, id):
        '''Get a quest'''
        quest = g.client_db_session.query(Quest).get(id)
        if quest is None:
            return create_response(404, 'Quest not found')

        data = quest.to_dict()
        ## get categories
        categories = []
        for category in quest.categories:
            categories.append(category.id)
        data['categories'] = categories

        ## get tags
        tags = []
        for tag in quest.tags:
            tags.append(tag.id)
        data['tags'] = tags

        ## get facets
        facets = []
        for facet in quest.facets:
            facets.append(facet.id)
        data['facets'] = facets

        ## get badges
        badges = []
        for badge in quest.badges:
            badges.append(badge.id)
        data['badges'] = badges

        ## get chest
        chest = {}
        if quest.chest_id:
            chest = g.client_db_session.query(Chest).get(quest.chest_id)
            if chest:
                chest_data = chest.to_dict()
                assets = []
                for asset in chest.assets:
                    assets.append(asset.id)
                chest_data['asset_ids'] = assets
                data['chest'] = chest_data
        else:
            data['chest'] = {
                'xp': 0,
                'coins': 0,
                'gems': 0,
                'keys': 0,
                'asset_ids': [],
                'button_label': 'Claim Rewards',
                'name': 'YOUR REWARDS',
                'facet_xp': []
            }

        return create_response('Quest retrieved successfully', data=data)
    
    @cms_quests_api.expect(cms_quest_model)
    def put(self, id):
        '''Update a quest'''
        quest = g.client_db_session.query(Quest).get(id)
        if quest is None:
            return create_response('Quest not found', 404)
        
        data = request.json
        name = data.get('name', '')
        if not name:
            return create_response('Name is required', 404)

        ## check if the quest already exists
        if name != quest.name:
            existing_quest = g.client_db_session.query(Quest).filter(Quest.name.ilike(name)).first()
            if existing_quest:
                return create_response('Quest already exists', 400)

        description = data.get('description', '')
        learning_objective = data.get('learning_objective', '')
        level = data.get('level', 1)
        category_ids = data.get('category_ids', [])
        tag_ids = data.get('tag_ids', [])
        facet_ids = data.get('facet_ids', [])
        badge_ids = data.get('badge_ids', [])
        image = data.get('image', '')

        quest.name = name
        quest.description = description
        quest.learning_objective = learning_objective
        quest.level = level
        quest.status = data.get('status', 'Draft')

        ## save image if exists
        FileService.process_entity_image(quest, image, 'quest', quest.id)

        ## save categories, add new categories and remove deleted categories
        existing_categories = [category.id for category in quest.categories]
        for category_id in category_ids:
            if category_id not in existing_categories:
                category = g.client_db_session.query(Category).get(category_id)
                if category is not None:
                    association = QuestCategoryAssociation(quest_id=quest.id, category_id=category_id)
                    g.client_db_session.add(association)
        for category_id in existing_categories:
            if category_id not in category_ids:
                association = g.client_db_session.query(QuestCategoryAssociation).filter(QuestCategoryAssociation.quest_id == quest.id, QuestCategoryAssociation.category_id == category_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## save tags, add new tags and remove deleted tags
        existing_tags = [tag.id for tag in quest.tags]
        for tag_id in tag_ids:
            if tag_id not in existing_tags:
                tag = g.client_db_session.query(Tag).get(tag_id)
                if tag is not None:
                    association = QuestTagAssociation(quest_id=quest.id, tag_id=tag_id)
                    g.client_db_session.add(association)
        for tag_id in existing_tags:
            if tag_id not in tag_ids:
                association = g.client_db_session.query(QuestTagAssociation).filter(QuestTagAssociation.quest_id == quest.id, QuestTagAssociation.tag_id == tag_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## save facets, add new facets and remove deleted facets
        existing_facets = [facet.id for facet in quest.facets]
        for facet_id in facet_ids:
            if facet_id not in existing_facets:
                facet = g.client_db_session.query(Facet).get(facet_id)
                if facet is not None:
                    association = QuestFacetAssociation(quest_id=quest.id, facet_id=facet_id)
                    g.client_db_session.add(association)
        for facet_id in existing_facets:
            if facet_id not in facet_ids:
                association = g.client_db_session.query(QuestFacetAssociation).filter(QuestFacetAssociation.quest_id == quest.id, QuestFacetAssociation.facet_id == facet_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## save badges, add new badges and remove deleted badges
        existing_badges = [badge.id for badge in quest.badges]
        for badge_id in badge_ids:
            if badge_id not in existing_badges:
                badge = g.client_db_session.query(Badge).get(badge_id)
                if badge is not None:
                    association = QuestBadgeAssociation(quest_id=quest.id, badge_id=badge_id)
                    g.client_db_session.add(association)
        for badge_id in existing_badges:
            if badge_id not in badge_ids:
                association = g.client_db_session.query(QuestBadgeAssociation).filter(QuestBadgeAssociation.quest_id == quest.id, QuestBadgeAssociation.badge_id == badge_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## if components exist, save them in quest config
        configs = data.get('configs', {})
        quest.configs = json.dumps(configs)

        ## update chest if exists, add new assets and remove deleted assets. If chest does not exist, create a new chest
        chest = data.get('chest', {})
        if chest:
            xp = chest.get('xp', 0)
            coins = chest.get('coins', 0)
            gems = chest.get('gems', 0)
            keys = chest.get('keys', 0)
            asset_ids = chest.get('asset_ids', [])
            chest_name = chest.get('name', "YOUR REWARDS")
            button_label = chest.get('button_label', 'Claim Rewards')
            facets = chest.get('facet_xp', [])

            ## check if chest exists
            if quest.chest_id:
                chest = g.client_db_session.query(Chest).get(quest.chest_id)
                if chest:
                    chest.xp = xp
                    chest.coins = coins
                    chest.gems = gems
                    chest.keys = keys
                    chest.name = chest_name
                    chest.button_label = button_label
                    chest.facet_xp = json.dumps(facets)

                ## check if chest assets changed, add new assets and remove deleted assets
                existing_assets = [asset.id for asset in chest.assets]
                for asset_id in asset_ids:
                    if asset_id not in existing_assets:
                        asset = g.client_db_session.query(Asset).get(asset_id)
                        if asset is not None:
                            association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                            g.client_db_session.add(association)
                for asset_id in existing_assets:
                    if asset_id not in asset_ids:
                        association = g.client_db_session.query(ChestAssetAssociation).filter(ChestAssetAssociation.chest_id == chest.id, ChestAssetAssociation.asset_id == asset_id).first()
                        if association is not None:
                            g.client_db_session.delete(association)

            else:
                ## create chest
                chest = Chest(
                    xp=xp,
                    coins=coins,
                    gems=gems,
                    keys=keys,
                    name=chest_name,
                    button_label=button_label,
                    facet_xp=json.dumps(facets)
                )
                g.client_db_session.add(chest)
                g.client_db_session.commit()

                ## save chest assets
                for asset_id in asset_ids:
                    asset = g.client_db_session.query(Asset).get(asset_id)
                    if asset is not None:
                        association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                        g.client_db_session.add(association)

                ## save chest id in quest
                quest.chest_id = chest.id

        quest.update_user = g.user_id
        quest.date_updated = datetime.datetime.utcnow()
        g.client_db_session.add(quest)
        g.client_db_session.commit()

        data = quest.to_dict()

        return create_response('Quest updated successfully', 200, data=data)


    @cms_quests_api.expect(delete_parser)
    def delete(self, id):
        '''Delete a quest'''
        quest = g.client_db_session.query(Quest).get(id)
        if quest is None:
            return create_response('Quest not found', 404)

        ## get force delete tag
        force_delete = request.args.get('force', 'false')
        if force_delete and force_delete == 'true':
            from api.common.publish_utils import delete_quest
            delete_quest(quest)

            return create_response('Quest deleted successfully', 200)
        else:
            ## update quest status to delete
            quest.status = "Archive"
            g.client_db_session.commit()
            return create_response('Quest is archived', 200)
        
        return create_response('Quest is archived', 200)
    

status_model = cms_quests_api.model('QuestStatus', {
    'status': fields.String(required=True, description='The quest status')
})

@cms_quests_api.doc(security='bearer')
@cms_quests_api.route('/<string:id>/status', methods=['PUT'])
class QuestStatus(Resource):
    @cms_quests_api.expect(status_model)
    def put(self, id):
        '''Update quest status'''
        quest = g.client_db_session.query(Quest).get(id)
        if quest is None:
            return create_response('Quest not found', 404)

        data = request.json
        status = data.get('status', '')
        if not status:
            return create_response('Status is required', 400)

        quest.status = status
        g.client_db_session.commit()

        return create_response('Quest status updated successfully', 200, data={'id': quest.id, 'status': quest.status})


quest_parser = cms_quests_api.parser()
quest_parser.add_argument('page', type=int, location='args', default=1)
quest_parser.add_argument('limit', type=int, location='args', default=10)
quest_parser.add_argument('search', type=str, location='args', default='')
quest_parser.add_argument('status', type=str, location='args', default='')
quest_parser.add_argument('sort', type=str, location='args', default=None)

@cms_quests_api.doc(security='bearer')
@cms_quests_api.route('/list')
class QuestList(Resource):
    @cms_quests_api.expect(quest_parser)
    def get(self):
        '''Get quests'''
        args = quest_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        search = args.get('search', '')
        status = args.get('status', '')
        sort = args.get('sort', None)

        offset = (page - 1) * limit

        from sqlalchemy.orm import aliased
        from sqlalchemy import or_
        create_user = aliased(User)
        update_user = aliased(User)

        query = g.client_db_session.query(
            Quest,
            create_user,
            update_user
        ).outerjoin(
            create_user,
            create_user.id == Quest.create_user
        ).outerjoin(
            update_user,
            update_user.id == Quest.update_user
        )
        
        if search:
            query = query.filter(
                or_(
                    Quest.name.ilike(f'%{search}%'),
                    Quest.description.ilike(f'%{search}%')
                )
            )

        if status:
            query = query.filter(Quest.status == status)

        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(Quest, sort[1:]).desc())
            else:
                query = query.order_by(getattr(Quest, sort))
        else:
            query = query.order_by(Quest.date_created.desc())

        # Calculate total items and total pages
        total = query.count()

        # Apply offset and limit to the query
        quests = query.offset(offset).limit(limit).all()

        data = []
        for quest, create_user, update_user in quests:
            quest_dict = quest.to_dict()
            quest_dict['create_user'] = create_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if create_user else None
            quest_dict['update_user'] = update_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if update_user else None
            data.append(quest_dict)

        return create_response('Quests retrieved successfully', data=data, total=total, page=page, limit=limit)





## create copy quest api
copy_quest_body = cms_quests_api.model('CopyQuest', {
    'name': fields.String(required=True, description='The new quest name')
})

@cms_quests_api.doc(security='bearer')
@cms_quests_api.route('/<string:id>/copy', methods=['POST'])
class CopyQuest(Resource):
    @cms_quests_api.expect(copy_quest_body)
    def post(self, id):
        from clientmodels import Node, NodeBranch, NodeOption, NodeOptionMatching, NodeTranscript
        from uuid_extensions import uuid7str

        '''Copy a quest'''
        quest = g.client_db_session.query(Quest).get(id)
        if quest is None:
            return create_response('Quest not found', 404)
        
        name = request.json.get('name', '')
        if not name:
            name = f"{quest.name} (Copy)"

        ## check if the quest already exists
        existing_quest = g.client_db_session.query(Quest).filter(Quest.name.ilike(name)).first()
        if existing_quest:
            return create_response('Quest with the same name already exists', 400)

        # Create a copy of the quest
        new_quest = Quest(
            name=name,
            description=quest.description,
            learning_objective=quest.learning_objective,
            level=quest.level,
            status='Draft',
            create_user=g.user_id,
            date_created=datetime.datetime.utcnow()
        )
        g.client_db_session.add(new_quest)
        g.client_db_session.commit()

        # Copy categories
        for category in quest.categories:
            association = QuestCategoryAssociation(quest_id=new_quest.id, category_id=category.id)
            g.client_db_session.add(association)

        # Copy tags
        for tag in quest.tags:
            association = QuestTagAssociation(quest_id=new_quest.id, tag_id=tag.id)
            g.client_db_session.add(association)

        # Copy facets
        for facet in quest.facets:
            association = QuestFacetAssociation(quest_id=new_quest.id, facet_id=facet.id)
            g.client_db_session.add(association)

        # Copy badges
        # for badge in quest.badges:
        #     association = QuestBadgeAssociation(quest_id=new_quest.id, badge_id=badge.id)
        #     g.client_db_session.add(association)

        # Copy chest if exists
        if quest.chest_id:
            chest = g.client_db_session.query(Chest).get(quest.chest_id)
            if chest:
                new_chest = Chest(
                    xp=chest.xp,
                    coins=chest.coins,
                    gems=chest.gems,
                    keys=chest.keys,
                    name=chest.name,
                    button_label=chest.button_label,
                    facet_xp=chest.facet_xp
                )
                g.client_db_session.add(new_chest)
                g.client_db_session.commit()

                # Copy chest assets
                for asset in chest.assets:
                    association = ChestAssetAssociation(chest_id=new_chest.id, asset_id=asset.id)
                    g.client_db_session.add(association)

                # Link new chest to the copied quest
                new_quest.chest_id = new_chest.id

        g.client_db_session.commit()

        FileService.process_entity_image(new_quest, quest.image, 'quest', new_quest.id)

        ## copy nodes
        new_nodes = []
        new_options = []
        new_option_matchings = []
        new_transcripts = []
        new_branches = []
        node_id_mapping = {}
        node_options_mapping = {}

        nodes = g.client_db_session.query(Node).filter(Node.quest_id == quest.id).all()
        for node in nodes:
            new_node = Node()
            # Copy node data
            new_node.id = uuid7str()
            new_node.quest_id = new_quest.id
            new_node.name = node.name
            new_node.screen_type = node.screen_type
            new_node.title = node.title
            new_node.sub_title = node.sub_title
            new_node.points = node.points
            new_node.quest_character_id = node.quest_character_id
            new_node.quest_character_animation = node.quest_character_animation
            new_node.quest_character_audio = node.quest_character_audio
            new_node.quest_text = node.quest_text
            new_node.answer_placeholder = node.answer_placeholder
            new_node.button_text = node.button_text
            new_node.next_node_id = node.next_node_id

            ## update next_node_id_mapping
            node_id_mapping[node.id] = new_node.id
            
            new_nodes.append(new_node)

        ## update next node id from mapping
        for node in new_nodes:
            if node.next_node_id:
                node.next_node_id = node_id_mapping.get(node.next_node_id, None)
        
        ## copy options, option matchings, transcripts and branches
        for node in nodes:
            ## copy options
            options = g.client_db_session.query(NodeOption).filter(NodeOption.node_id == node.id).all()
            for option in options:
                new_option = NodeOption()
                new_option.id = uuid7str()
                
                new_option.node_id = node_id_mapping.get(node.id, None)
                if not new_option.node_id:
                    continue

                new_option.label = option.label if option.label else ""
                new_option.value = option.value if option.value else ""
                new_option.position = option.position if option.position else ""
                new_option.label_color = option.label_color if option.label_color else ""
                new_option.is_correct = option.is_correct
                new_option.index = option.index if option.index else 0
                new_option.points = option.points if option.points else 0
                new_option.next_node_id = node_id_mapping.get(option.next_node_id, None)
                new_option.feedback = option.feedback if option.feedback else ""
                new_options.append(new_option)
                node_options_mapping[option.id] = new_option.id

            ## copy option matchings
            option_matchings = g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_id == node.id).all()
            for option_matching in option_matchings:
                new_option_matching = NodeOptionMatching()
                new_option_matching.node_id = node_id_mapping.get(node.id, None)
                if not new_option_matching.node_id:
                    continue

                new_option_matching.node_option_id = node_options_mapping.get(option_matching.node_option_id, None)
                new_option_matching.matching_node_option_id = node_options_mapping.get(option_matching.matching_node_option_id, None)
                new_option_matchings.append(new_option_matching)
        
            ## copy transcripts
            transcripts = g.client_db_session.query(NodeTranscript).filter(NodeTranscript.node_id == node.id).all()
            for transcript in transcripts:
                new_transcript = NodeTranscript()
                new_transcript.node_id = node_id_mapping.get(node.id, None)
                if not new_transcript.node_id:
                    continue

                new_transcript.index = transcript.index
                new_transcript.text = transcript.text if transcript.text else ""
                new_transcript.character_id = transcript.character_id if transcript.character_id else ""
                new_transcript.animation = transcript.animation if transcript.animation else ""
                new_transcript.show_icon = transcript.show_icon
                new_transcript.audio_en = transcript.audio_en if transcript.audio_en else ""
                new_transcript.audios = transcript.audios if transcript.audios else None
                new_transcript.duration = transcript.duration
                new_transcript.date_created = transcript.date_created
                new_transcripts.append(new_transcript)

            ## copy branches
            branches = g.client_db_session.query(NodeBranch).filter(NodeBranch.node_id == node.id).all()
            for branch in branches:
                new_branch = NodeBranch()
                new_branch.node_id = node_id_mapping.get(node.id, None)
                if not new_branch.node_id:
                    continue

                new_branch.condition = branch.condition if branch.condition else ""
                new_branch.condition_value = branch.condition_value if branch.condition_value else ""
                new_branch.next_node_id = node_id_mapping.get(branch.next_node_id, None)
                new_branches.append(new_branch)

        # Add all nodes at once
        g.client_db_session.add_all(new_nodes)
        g.client_db_session.commit()

        # Add all options at once
        g.client_db_session.add_all(new_options)
        g.client_db_session.commit()

        # Add all option matchings at once
        g.client_db_session.add_all(new_option_matchings)

        ## Add all transcripts at once
        g.client_db_session.add_all(new_transcripts)

        # Add all branches at once
        g.client_db_session.add_all(new_branches)

        # Commit the changes
        g.client_db_session.commit()

        return create_response('Quest copied successfully', data={'id': new_quest.id})