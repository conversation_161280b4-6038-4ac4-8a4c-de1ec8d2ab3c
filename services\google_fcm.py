import json
from typing import Dict, Any, Optional
import time
import firebase_admin
from firebase_admin import credentials, messaging
import requests

from services.azure_key_vault import AzureKeyVault

class GoogleFCMService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GoogleFCMService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            cred_json = AzureKeyVault().get_secret('google-firebase-credentials')
            cred = json.loads(cred_json)
            if not firebase_admin._apps:
                cred = credentials.Certificate(cred)
                firebase_admin.initialize_app(cred)
            self._initialized = True


    def push_notification(
        self,
        token: str,
        title: str,
        body: str,
        data: Optional[Dict[str, Any]] = None,
        badge: Optional[int] = None,
        device_type: Optional[str] = None
    ) -> bool:
        """Send a notification to a specific device token.
        
        Args:
            token: FCM device token
            title: Notification title
            body: Notification body
            data: Optional data payload
            badge: Optional badge count
            device_type: Optional device type (ios, android, web)
        """
        try:
            # Prepare data payload with title and body included
            notification_data = data or {}
            notification_data['title'] = title
            notification_data['body'] = body
            # Prepare APNS payload for iOS
            apns = messaging.APNSConfig(
                payload=messaging.APNSPayload(
                    aps=messaging.Aps(
                        badge=badge,
                        sound='default' if title else None,
                    )
                )
            ) if badge is not None else None

            # Prepare Android payload
            android = messaging.AndroidConfig(
                notification=messaging.AndroidNotification(
                    notification_count=badge,
                    sound = 'default' if title else '',
                    default_sound = True if title else False,
                    default_vibrate_timings = True if title else False
                )
            ) if badge is not None else None

            # Create message based on device type
            if device_type == 'web':
                # For web devices, send only data message without notification
                message = messaging.Message(
                    data=notification_data,
                    token=token
                )
            else:
                # For mobile devices, send both notification and data
                message = messaging.Message(
                    notification=messaging.Notification(
                        title=title,
                        body=body
                    ),
                    data=notification_data,
                    token=token,
                    apns=apns,
                    android=android
                )

            # Send message
            messaging.send(message)
            return True

        except Exception as e:
            print(f'Error sending FCM notification: {str(e)}')
            return False


    def push_live_activity_notification(
        self,
        token: str,  # FCM device token
        live_activity_token: str,  # Live activity token
        is_update: bool,  # True for update, False for end
    ) -> bool:
        """
        Send a live activity notification using FCM REST API.

        Args:
            token: FCM device token.
            is_update: True for update, False for end.

        Returns:
            bool: True if the notification was sent successfully, False otherwise.
        """
        try:
            # Get the access token for FCM
            access_token = credentials.Certificate(self.cred).get_access_token()
            
            # Prepare the FCM REST API endpoint
            url = 'https://fcm.googleapis.com/v1/projects/xapa-world/messages:send'
            
            # Prepare headers
            headers = {
                'Authorization': f'Bearer {access_token.access_token}',
                'Content-Type': 'application/json'
            }

            # Prepare the message payload for testing (regular notification)
            payload = {
                'message': {
                    'token': token,
                    'apns':{
                        'live_activity_token': live_activity_token,
                        'headers':{
                            'apns-priority': '10'
                        },
                        'payload':{
                            'aps': {
                                'timestamp': int(time.time()),
                                'dismissal-date': int(time.time()),
                                'event': 'end',
                                'content-state': {}
                            }
                        }
                    }
                }
            }

            # Send the request
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                print(f'Notification sent successfully: {response.json()}')
                return True
            else:
                print(f'Error sending notification: {response.status_code} - {response.text}')
                return False

        except Exception as e:
            print(f'Error sending FCM notification: {str(e)}')
            return False


if __name__ == '__main__':
    # Test with APNs token and Live Activity token
    token = ''
    activity_token = '' 
    
    fcm = GoogleFCMService()
    
    # Test update notification
    print('\nTesting update notification:')
    fcm.push_live_activity_notification(
        token=token,
        live_activity_token=activity_token,
        is_update=True
    )
    
    # Test end notification
    print('\nTesting end notification:')
    fcm.push_live_activity_notification(
        token=token,
        live_activity_token=activity_token,
        is_update=False
    )
