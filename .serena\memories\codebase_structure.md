# XAPA Backend Codebase Structure

## Root Files
- `app.py`: Main Flask application entry point
- `models.py`: Core database models and base classes
- `clientmodels.py`: Client-specific database models
- `requirements.txt`: Python dependencies
- `gunicorn_config.py`: Gunicorn server configuration
- `alembic.ini`: Database migration configuration
- `sso.py`: SSO authentication logic
- `version.py`: Application version information

## Key Directories

### `/api/` - API Endpoints
- `account/`: Authentication and admin management
- `app/`: Mobile app APIs (quests, user interactions, social features)
- `cms/`: Content Management System APIs (clients, users, programs, etc.)
- `common/`: Shared utilities and helpers
- `file/`: File handling operations
- `task/`: Background task APIs
- `websocket/`: Real-time communication

### `/config/` - Configuration Files
- `development.py`: Development environment settings
- `production.py`: Production environment settings
- `init_*.py`: Various service initialization modules (cache, database, OAuth, etc.)

### `/services/` - External Service Integrations
- `azure_*.py`: Azure service clients (Key Vault, Storage, Queue)
- `google_*.py`: Google service integrations (FCM, APIs)
- `apple_apns.py`: Apple Push Notifications
- `email_service.py`: Email functionality
- `local_storage.py`: Local file storage

### `/tests/` - Test Suite
- `test_*.py`: API endpoint tests
- `init_request.py`: Test request initialization

### `/migrations/` - Database Migrations
- Alembic migration files

### `/static/` - Static Files
- Static assets and configuration files

### `/docs/` - Documentation
- Project documentation files