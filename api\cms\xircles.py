from flask import g, request
from flask_restx import Namespace, Resource, fields, reqparse
from sqlalchemy import alias, and_, text

from api.common.decorator import check_user_permission
from api.common.file import FileService
from api.common.helper import create_response
from api.common.publish_utils import publish_xapa_xircle
from clientmodels import Feed, FeedComment, FeedFlag, FeedLike, Xircle, XircleMember, User

cms_xircles_api = Namespace('api_cms_xircle', description='Xircle management related operations')


## xircle model
xircle_model = cms_xircles_api.model('XircleItem', {
    'name': fields.String(required=True, description='Xircle Name'),
    'description': fields.String(required=True, description='Xircle Description'),
    'image': fields.String(required=True, description='Xircle Image'),
    'is_public': fields.Boolean(required=True, description='Is Xircle Public', default=True),
    'include_leaderboard': fields.Boolean(required=True, description='Include Leaderboard', default=True)
})

## create, edit, delete and get xircle
@cms_xircles_api.doc(security='bearer')
@cms_xircles_api.route('/', methods=['POST'])
@cms_xircles_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class XircleObject(Resource):
    def get(self, id):
        ## get xircle by id
        xircle = g.client_db_session.query(Xircle).filter_by(id=id).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        # Get member count
        member_count = g.client_db_session.query(XircleMember).filter_by(xircle_id=id).count()
        
        # Get member preview with user details
        member_preview = g.client_db_session.query(User).join(XircleMember).filter(
            XircleMember.xircle_id == id
        ).limit(5).all()
        
        # Convert member preview to list of dictionaries
        member_preview_list = [m.to_dict(["id", "first_name", "last_name", "image", "title"]) for m in member_preview]
        
        # Get feed count
        feed_count = g.client_db_session.query(Feed).filter(
            Feed.xircle_id == id,
            Feed.is_deleted == False
        ).count()
        
        data = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard'])
        
        # Get creator info
        creator = g.client_db_session.query(User).filter_by(id=xircle.creator_id).first()
        data['creator'] = creator.to_dict(['id', 'first_name', 'last_name', 'image', 'title']) if creator else None
        
        # Determine permissions based on xircle type and user role
        data['member_count'] = member_count
        data['member_preview'] = member_preview_list
        data['feed_count'] = feed_count
        
        return create_response("Get xircle", data=data)

    @cms_xircles_api.expect(xircle_model)
    # @check_user_permission('admin')
    def post(self):
        ## create a xircle
        data = request.json

        name = data.get('name').strip()
        description = data.get('description')
        image = data.get('image', '')
        is_public = data.get('is_public', True)
        include_leaderboard = data.get('include_leaderboard', True)

        ## check if name is already taken, case insensitive
        xircle = g.client_db_session.query(Xircle).filter(Xircle.name.ilike(name), Xircle.is_deleted == False).first()
        if xircle is not None:
            return create_response("Xircle name already taken", status=400)
        
        xircle = Xircle()
        xircle.name = name
        xircle.description = description
        xircle.is_public = is_public
        xircle.include_leaderboard = include_leaderboard
        xircle.creator_id = g.user_id

        g.client_db_session.add(xircle)
        g.client_db_session.flush()

        ## save image
        FileService.process_entity_image(xircle, image, 'xircle', xircle.id)

        ## add creator to member list
        xircle_member = XircleMember()
        xircle_member.xircle_id = xircle.id
        xircle_member.user_id = g.user_id
        
        g.client_db_session.add(xircle_member)
        g.client_db_session.commit()

        return create_response("Xircle created", data=xircle.to_dict())

    # @check_user_permission('admin')
    def delete(self, id):
        ## delete a xircle
        xircle = g.client_db_session.query(Xircle).filter_by(id=id).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        # Delete all related feeds first
        feeds = g.client_db_session.query(Feed).filter_by(xircle_id=id).all()
        for feed in feeds:
            ## delete all feed likes
            g.client_db_session.query(FeedLike).filter_by(feed_id=feed.id).delete()
            ## delete all feed comments
            g.client_db_session.query(FeedComment).filter_by(feed_id=feed.id).delete()
            ## delete all feed flags
            g.client_db_session.query(FeedFlag).filter_by(feed_id=feed.id).delete()
            
            ## delete feed
            g.client_db_session.delete(feed)
        
        # Delete all xircle members
        g.client_db_session.query(XircleMember).filter_by(xircle_id=id).delete()
        
        # Delete the xircle itself
        g.client_db_session.delete(xircle)
        
        # Commit all changes
        g.client_db_session.commit()

        return create_response("Xircle and all related data deleted successfully")

    @cms_xircles_api.expect(xircle_model)
    # @check_user_permission('admin')
    def put(self, id):
        ## edit a xircle
        xircle = g.client_db_session.query(Xircle).filter_by(id=id).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        data = request.json

        name = data.get('name').strip()
        description = data.get('description')
        image = data.get('image', '')
        is_public = data.get('is_public', True)
        include_leaderboard = data.get('include_leaderboard', True)

        ## check if name is already taken, case insensitive
        xircle_check = g.client_db_session.query(Xircle).filter(Xircle.name.ilike(name), Xircle.is_deleted == False).first()
        if xircle_check is not None and xircle_check.id != xircle.id:
            return create_response("Xircle name already taken", status=400)

        FileService.process_entity_image(xircle, image, 'xircle', xircle.id)
        
        xircle.name = name
        xircle.description = description
        xircle.is_public = is_public
        xircle.include_leaderboard = include_leaderboard
        
        g.client_db_session.commit()

        return create_response("Xircle updated successfully")


## xircle member model
xircle_member_model = cms_xircles_api.model('XircleMember', {
    'user_ids': fields.List(fields.String, required=False, description='User IDs')
})

@cms_xircles_api.doc(security='bearer')
@cms_xircles_api.route('/<string:id>/members', methods=['GET', 'POST', 'DELETE'])
class XircleMemberObject(Resource):
    # @check_user_permission('admin')
    def get(self, id):
        """Get xircle members"""
        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        members = g.client_db_session.query(User).join(XircleMember).filter(
            XircleMember.xircle_id == id
        ).all()
        
        data = [m.to_dict(["id", "first_name", "last_name", "image", "title", "email"]) for m in members]
        return create_response("Get xircle members", data=data)

    @cms_xircles_api.expect(xircle_member_model)
    # @check_user_permission('admin')
    def post(self, id):
        """Add members to xircle"""
        data = request.json
        user_ids = data.get('user_ids', [])
        
        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)

        # Get existing members
        existing_members = g.client_db_session.query(XircleMember.user_id).filter(
            XircleMember.xircle_id == id
        ).all()
        existing_member_ids = [m[0] for m in existing_members]

        added_members = []
        skipped_members = []
        
        for user_id in user_ids:
            if user_id in existing_member_ids:
                user = g.client_db_session.query(User).filter(User.id==user_id).first()
                if user:
                    skipped_members.append(user.to_dict(['id', 'first_name', 'last_name', 'email']))
                continue

            user = g.client_db_session.query(User).filter(User.id==user_id).first()
            if not user:
                continue

            xircle_member = XircleMember()
            xircle_member.xircle_id = id
            xircle_member.user_id = user_id
            g.client_db_session.add(xircle_member)
            added_members.append(user.to_dict(['id', 'first_name', 'last_name', 'email']))

        g.client_db_session.commit()

        return create_response(
            "Members added to xircle",
            data={
                'added_members': added_members,
                'skipped_members': skipped_members,
                'total_added': len(added_members)
            }
        )

    @cms_xircles_api.expect(xircle_member_model)
    # @check_user_permission('admin')
    def delete(self, id):
        """Remove members from xircle"""
        data = request.json
        user_ids = data.get('user_ids', [])

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)

        removed_members = []
        skipped_members = []

        for user_id in user_ids:
            member = g.client_db_session.query(XircleMember).filter(
                XircleMember.xircle_id==id,
                XircleMember.user_id==user_id
            ).first()

            if not member:
                user = g.client_db_session.query(User).filter(User.id==user_id).first()
                if user:
                    skipped_members.append(user.to_dict(['id', 'first_name', 'last_name', 'email']))
                continue

            user = g.client_db_session.query(User).filter(User.id==user_id).first()
            if user:
                removed_members.append(user.to_dict(['id', 'first_name', 'last_name', 'email']))

            # If creator is being removed, transfer ownership to xapa admin
            if user_id == xircle.creator_id:
                xircle.creator_id = '00000000-0000-0000-0000-000000000000'

            g.client_db_session.delete(member)

        g.client_db_session.commit()

        return create_response(
            "Members removed from xircle",
            data={
                'removed_members': removed_members,
                'skipped_members': skipped_members,
                'total_removed': len(removed_members)
            }
        )

## get xircle list
parser = reqparse.RequestParser()
parser.add_argument('page', type=int, help='Page number', default=1)
parser.add_argument('limit', type=int, help='Items per page', default=10)
parser.add_argument('is_public', type=bool, help='Filter by public status', default=None)
parser.add_argument('member_id', type=str, default=None, help='Filter by member id to find xircles with this member')
parser.add_argument('search', type=str, help='Search term', default=None)

@cms_xircles_api.doc(security='bearer')
@cms_xircles_api.route('/list', methods=['GET'])
class XircleList(Resource):
    @cms_xircles_api.expect(parser)
    def get(self):
        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        offset = (page - 1) * limit
        is_public = args.get('is_public', None)
        member_id = args.get('member_id', None)
        search = args.get('search', None)

        # Base query with non-deleted xircles
        query = g.client_db_session.query(
            Xircle
        ).filter(
            Xircle.name.notilike('xapa'), 
            Xircle.id != '00000000-0000-0000-0000-000000000000',
            Xircle.is_deleted == False
        )

        # 1. Filter by public status if specified
        if is_public is not None:
            query = query.filter(Xircle.is_public == is_public)

        # 2. Filter by member if specified
        if member_id:
            member_alias = alias(XircleMember)
            query = query.join(
                member_alias,
                and_(
                    member_alias.c.xircle_id == Xircle.id,
                    member_alias.c.user_id == member_id
                )
            )

        # 3. Apply search filter if specified
        if search:
            query = query.filter(Xircle.name.ilike(f"%{search}%"))

        # Get total count before pagination
        total = query.count()

        # Apply pagination and ordering
        xircles = query.order_by(Xircle.date_updated.desc()).limit(limit).offset(offset).all()
        
        # Get xircle IDs for member preview
        xircle_ids = [x.id for x in xircles]

        # Get all creators for these xircles in a single query
        creators = {
            user.id: user.to_dict(['id', 'first_name', 'last_name', 'image', 'title'])
            for user in g.client_db_session.query(User).filter(
                User.id.in_([x.creator_id for x in xircles])
            ).all()
        }

        # Get member counts and previews using CTE
        member_preview_query = text("""
            WITH RankedMembers AS (
                SELECT 
                    xm.xircle_id,
                    COUNT(*) OVER (PARTITION BY xm.xircle_id) as member_count,
                    ROW_NUMBER() OVER (PARTITION BY xm.xircle_id ORDER BY xm.date_created) as rn,
                    u.id as user_id,
                    u.first_name,
                    u.last_name,
                    u.title,
                    u.image
                FROM xircle_member xm
                JOIN "user" u ON u.id = xm.user_id
                WHERE xm.xircle_id = ANY(:xircle_ids)
            )
            SELECT * FROM RankedMembers WHERE rn <= 5
        """)
        
        member_results = g.client_db_session.execute(
            member_preview_query, 
            {'xircle_ids': xircle_ids}
        ).fetchall()

        # Get feed counts for each xircle
        feed_count_query = text("""
            SELECT 
                xircle_id,
                COUNT(*) as feed_count
            FROM feed
            WHERE xircle_id = ANY(:xircle_ids) AND is_deleted = false
            GROUP BY xircle_id
        """)
        
        feed_counts = {
            row.xircle_id: row.feed_count
            for row in g.client_db_session.execute(
                feed_count_query,
                {'xircle_ids': xircle_ids}
            ).fetchall()
        }

        # Process member preview results
        member_previews = {}
        member_counts = {}
        
        for row in member_results:
            xircle_id = row.xircle_id
            
            # Initialize member preview list for this xircle
            if xircle_id not in member_previews:
                member_previews[xircle_id] = []
                member_counts[xircle_id] = row.member_count
            
            # Add member to preview
            member_previews[xircle_id].append({
                'id': row.user_id,
                'first_name': row.first_name,
                'last_name': row.last_name,
                'title': row.title,
                'image': row.image
            })
        
        # Process results
        data = []
        for xircle in xircles:
            item = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard', 'date_updated'])
            
            # Add member count and preview
            item['member_count'] = member_counts.get(xircle.id, 0)
            item['member_preview'] = member_previews.get(xircle.id, [])
            item['feed_count'] = feed_counts.get(xircle.id, 0)  # Add feed count to response

            # Add creator info
            item['creator'] = creators.get(xircle.creator_id)

            data.append(item)

        return create_response("Get Xircle list", data=data, total=total, page=page, limit=limit)


@cms_xircles_api.doc(security='bearer')
@cms_xircles_api.route('/xapa', methods=['GET', 'PUT'])
class XapaXircleObject(Resource):
    def get(self):
        ## get xapa xircle
        user = g.db_session.query(User).filter(User.id=='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.db_session.add(user)
            g.db_session.commit()

        xircle = g.db_session.query(Xircle).filter(Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if not xircle:
            xircle = Xircle()
            xircle.name = 'Xapa'
            xircle.description = 'Global Xapa Community'
            xircle.is_public = True
            xircle.include_leaderboard = True
            xircle.creator_id = user.id
            g.db_session.add(xircle)
            g.db_session.commit()

        data = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard'])
        
        data['creator'] = None
        data['is_creator'] = False
        data['is_member'] = False
        data['can_invite'] = False
        data['can_delete'] = False
        data['can_edit'] = False
        data['can_leave'] = False
        data['can_post'] = False
        data['can_join'] = False
        data['member_count'] = 0
        data['member_preview'] = []

        return create_response("Get Xapa xircle", data=data)

    @cms_xircles_api.expect(xircle_model)
    # @check_user_permission('admin')
    def put(self):
        ## edit xapa xircle
        data = request.json

        xircle = g.db_session.query(Xircle).filter(Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        description = data.get('description', None)
        image = data.get('image', None)
        is_public = data.get('is_public', None)
        include_leaderboard = data.get('include_leaderboard', None)

        if description:
            xircle.description = description
        
        if is_public:
            xircle.is_public = is_public
        
        if include_leaderboard:
            xircle.include_leaderboard = include_leaderboard

        FileService.process_entity_image(xircle, image, 'xircle', xircle.id)

        g.db_session.add(xircle)
        g.db_session.commit()

        publish_xapa_xircle()

        return create_response("Edit xircle")


## xircle model
xircle_model = cms_xircles_api.model('XircleItem', {
    'name': fields.String(required=True, description='Xircle Name'),
    'description': fields.String(required=True, description='Xircle Description'),
    'image': fields.String(required=True, description='Xircle Image'),
    'is_public': fields.Boolean(required=True, description='Is Xircle Public', default=True),
    'include_leaderboard': fields.Boolean(required=True, description='Include Leaderboard', default=True)
})

@cms_xircles_api.doc(security='bearer')
@cms_xircles_api.route('/company', methods=['GET', 'PUT'])
class CompanyXircleObject(Resource):
    def get(self):
        # get company xircle
        user = g.client_db_session.query(User).filter(User.id=='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.client_db_session.add(user)
            g.client_db_session.commit()

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id=='00000000-0000-0000-0000-000000000000', Xircle.is_deleted==False).first()
        if not xircle:
            xircle = Xircle()
            xircle.id = '00000000-0000-0000-0000-000000000000'
            xircle.name = 'Company Xircle'
            xircle.description = 'Global Company Community'
            xircle.is_public = True
            xircle.include_leaderboard = True
            xircle.creator_id = user.id
            g.client_db_session.add(xircle)
            g.client_db_session.commit()

        data = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard'])
        
        data['creator'] = None
        data['is_creator'] = False
        data['is_member'] = False
        data['can_invite'] = False
        data['can_delete'] = False
        data['can_edit'] = False
        data['can_leave'] = False
        data['can_post'] = False
        data['can_join'] = False
        data['member_count'] = 0
        data['member_preview'] = []

        return create_response("Get Xapa xircle", data=data)

    @cms_xircles_api.expect(xircle_model)
    # @check_user_permission('admin')
    def put(self):
        ## edit company xircle
        data = request.json

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id=='00000000-0000-0000-0000-000000000000', Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        name = data.get('name', None)
        description = data.get('description', None)
        image = data.get('image', None)
        is_public = data.get('is_public', None)
        include_leaderboard = data.get('include_leaderboard', None)
        
        if name:
             ## check if name is already taken, case insensitive
            xircle_check = g.client_db_session.query(Xircle).filter(Xircle.name.ilike(name), Xircle.is_deleted == False).first()
            if xircle_check is not None and xircle_check.id != xircle.id:
                return create_response("Xircle name already taken", status=400)
            xircle.name = name

        if description:
            xircle.description = description
        
        if is_public:
            xircle.is_public = is_public
        
        if include_leaderboard:
            xircle.include_leaderboard = include_leaderboard

        FileService.process_entity_image(xircle, image, 'xircle', xircle.id)

        g.client_db_session.add(xircle)
        g.client_db_session.commit()

        return create_response("Edit xircle")
