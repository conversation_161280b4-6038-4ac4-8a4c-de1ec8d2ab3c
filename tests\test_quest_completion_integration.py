import unittest
import json
import datetime
from unittest.mock import patch, MagicMock

from api.app.quest import CompleteQuest
from flask import Flask


class TestQuestCompletionWithXipCode(unittest.TestCase):
    """Integration tests for quest completion with xip code updates"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.app = Flask(__name__)
        self.quest_id = "test-quest-123"
        self.user_id = "test-user-456"
        
    @patch('api.app.quest.g')
    @patch('api.app.quest.update_user_style_on_quest_completion')
    @patch('api.app.quest.request')
    def test_quest_completion_with_style_update(self, mock_request, mock_style_update, mock_g):
        """Test quest completion when style update is successful"""
        
        # Mock request payload
        mock_request.json = {
            'program_id': 'test-program',
            'xperience_id': 'test-xperience'
        }
        
        # Mock database session and objects
        mock_db_session = MagicMock()
        mock_g.db_session = mock_db_session
        mock_g.user_id = self.user_id
        
        # Mock quest object
        mock_quest = MagicMock()
        mock_quest.id = self.quest_id
        mock_quest.chest_id = None
        
        # Mock user quest object
        mock_user_quest = MagicMock()
        mock_user_quest.status = 'incomplete'
        
        # Set up query mocks
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_quest
        mock_db_session.query.return_value.filter_by.return_value.order_by.return_value.first.return_value = mock_user_quest
        
        # Mock empty program and xperience associations
        mock_db_session.query.return_value.filter.return_value.all.return_value = []
        
        # Mock style update result
        mock_style_update.return_value = {
            "action": "created",
            "style_id": "style-123",
            "style_name": "Test Style",
            "style_value": "value1",
            "latest_node_id": "node-1",
            "quest_id": self.quest_id
        }
        
        with self.app.test_request_context():
            with patch('api.app.quest.create_response') as mock_create_response:
                mock_create_response.return_value = {"status": "success"}
                
                # Create CompleteQuest resource and call post method
                complete_quest = CompleteQuest()
                result = complete_quest.post(self.quest_id)
                
                # Verify quest was marked as completed
                self.assertEqual(mock_user_quest.status, 'completed')
                self.assertEqual(mock_user_quest.process_percentage, 100)
                self.assertIsNotNone(mock_user_quest.date_completed)
                
                # Verify style update was called
                mock_style_update.assert_called_once_with(self.quest_id, self.user_id)
                
                # Verify the response includes style update information
                args, kwargs = mock_create_response.call_args
                self.assertIn('rewards', kwargs)
                rewards = kwargs['rewards']
                self.assertIn('style_update', rewards)
                self.assertEqual(rewards['style_update']['action'], 'created')
                
    @patch('api.app.quest.g')
    @patch('api.app.quest.update_user_style_on_quest_completion')
    @patch('api.app.quest.request')
    def test_quest_completion_without_style_update(self, mock_request, mock_style_update, mock_g):
        """Test quest completion when no style update is needed"""
        
        # Mock request payload
        mock_request.json = {
            'program_id': 'test-program',
            'xperience_id': 'test-xperience'
        }
        
        # Mock database session and objects
        mock_db_session = MagicMock()
        mock_g.db_session = mock_db_session
        mock_g.user_id = self.user_id
        
        # Mock quest object
        mock_quest = MagicMock()
        mock_quest.id = self.quest_id
        mock_quest.chest_id = None
        
        # Mock user quest object
        mock_user_quest = MagicMock()
        mock_user_quest.status = 'incomplete'
        
        # Set up query mocks
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_quest
        mock_db_session.query.return_value.filter_by.return_value.order_by.return_value.first.return_value = mock_user_quest
        
        # Mock empty program and xperience associations
        mock_db_session.query.return_value.filter.return_value.all.return_value = []
        
        # Mock no style update (returns None)
        mock_style_update.return_value = None
        
        with self.app.test_request_context():
            with patch('api.app.quest.create_response') as mock_create_response:
                mock_create_response.return_value = {"status": "success"}
                
                # Create CompleteQuest resource and call post method
                complete_quest = CompleteQuest()
                result = complete_quest.post(self.quest_id)
                
                # Verify quest was marked as completed
                self.assertEqual(mock_user_quest.status, 'completed')
                self.assertEqual(mock_user_quest.process_percentage, 100)
                
                # Verify style update was called but returned None
                mock_style_update.assert_called_once_with(self.quest_id, self.user_id)
                
                # Verify the response does not include style update information
                args, kwargs = mock_create_response.call_args
                self.assertIn('rewards', kwargs)
                rewards = kwargs['rewards']
                self.assertNotIn('style_update', rewards)


if __name__ == '__main__':
    unittest.main()