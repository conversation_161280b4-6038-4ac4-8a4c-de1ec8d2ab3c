# Program-Xperience Association API Documentation

This document describes the new Program-Xperience association functionality added to the CMS.

## Overview

The Program-Xperience association allows linking Xperiences to Programs and managing their order within each Program. This enables the CMS to organize Xperiences under Programs and control their sequence.

## Database Changes

### New Table: `program_xperience_association`

| Column | Type | Description |
|--------|------|-------------|
| id | String(36) | Primary key (UUID) |
| program_id | String(36) | Foreign key to program.id |
| xperience_id | String(36) | Foreign key to xperience.id |
| order | Integer | Order of xperience within the program (default: 0) |

### Model Relationships

- `Program.xperiences` - Returns related Xperiences ordered by association order
- `Xperience.programs` - Returns related Programs

## API Endpoints

### 1. Program Management (Updated)

#### GET `/api/cms/programs/{program_id}`
**Returns program details including associated xperiences**

Response includes:
```json
{
  "data": {
    "id": "program-id",
    "name": "Program Name",
    "xperience_ids": ["xperience-1", "xperience-2"],
    "xperiences": [
      {
        "id": "xperience-1", 
        "order": 0
      },
      {
        "id": "xperience-2", 
        "order": 1
      }
    ]
  }
}
```

#### POST `/api/cms/programs/`
**Create program with xperiences**

Request body includes:
```json
{
  "name": "New Program",
  "description": "Program description",
  "xperience_ids": ["xperience-1", "xperience-2"]
}
```

#### PUT `/api/cms/programs/{program_id}`
**Update program and manage xperience associations**

Request body can include:
```json
{
  "name": "Updated Program",
  "xperience_ids": ["xperience-1", "xperience-3", "xperience-2"]
}
```

### 2. Xperience Order Management

#### PUT `/api/cms/programs/{program_id}/xperiences/order`
**Update the order of xperiences within a program**

Request body:
```json
{
  "xperience_ids": ["xperience-3", "xperience-1", "xperience-2"]
}
```

**Validation:**
- All provided xperience_ids must be currently associated with the program
- The list must contain exactly the same xperiences as currently associated

### 3. Individual Xperience Association Management

#### POST `/api/cms/programs/{program_id}/xperiences`
**Add an xperience to a program**

Request body:
```json
{
  "xperience_id": "xperience-id",
  "order": 2
}
```

#### DELETE `/api/cms/programs/{program_id}/xperiences`
**Remove an xperience from a program**

Request body:
```json
{
  "xperience_id": "xperience-id"
}
```

## Migration

A database migration file has been created at:
`migrations/versions/add_program_xperience_association.py`

To apply the migration:
```bash
alembic upgrade head
```

## Usage Examples

### Creating a Program with Xperiences
```python
import requests

data = {
    "name": "Leadership Training Program",
    "description": "Comprehensive leadership development",
    "xperience_ids": ["leadership-basics", "team-management", "strategic-thinking"]
}

response = requests.post("/api/cms/programs/", json=data)
```

### Reordering Xperiences
```python
import requests

# Reorder to put strategic-thinking first
data = {
    "xperience_ids": ["strategic-thinking", "leadership-basics", "team-management"]
}

response = requests.put("/api/cms/programs/program-id/xperiences/order", json=data)
```

### Adding a New Xperience
```python
import requests

data = {
    "xperience_id": "communication-skills",
    "order": 1  # Insert at position 1
}

response = requests.post("/api/cms/programs/program-id/xperiences", json=data)
```

## Implementation Notes

1. **Order Management**: Orders are 0-indexed and should be sequential for best results
2. **Referential Integrity**: Foreign key constraints ensure data consistency
3. **Cascade Operations**: When programs are force-deleted, all xperience associations are removed
4. **Validation**: API endpoints validate that xperiences exist before creating associations

## Testing

Tests are located in `tests/test_program_xperience_association.py` and cover:
- Model instantiation
- Relationship existence
- Ordering functionality
- API data structures

To run tests in a properly configured environment:
```bash
python -m pytest tests/test_program_xperience_association.py
```