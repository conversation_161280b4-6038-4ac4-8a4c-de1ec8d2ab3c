import os

from flask import g
from flask_restx import Namespace, Resource
from sqlalchemy import func

from api.common.helper import create_response
from api.common.users_utils import get_user_entitlement_groups
from api.app.xperience import get_xperience_quests
from clientmodels import MASTER_TENANT, Character, Node, Program, Category, ProgramQuestAssociation, Quest, ProgramCategoryAssociation, \
    User, UserProgram, UserSpending, UserStats
from clientmodels import ProgramDivider, Chest, ProgramXperienceAssociation, Xperience, PublishedXperience
from clientmodels import EntitlementGroupProgramAssignment
from clientmodels import PublishedProgram, Client, ClientPackageAssociation, PackageProgramAssociation
from clientmodels import PublishedQuest

app_program_api = Namespace('api_app_program', description='Program related API')

programs_parser = app_program_api.parser()
programs_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
programs_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
programs_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')

@app_program_api.doc(security='bearer')
@app_program_api.route('/list')
class ProgramsList(Resource):
    @app_program_api.expect(programs_parser)
    def get(self):
        args = programs_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        offset = (page - 1) * limit

        query = get_program_query(search=search, user_id=g.user_id)
    
        total, programs = paginate_query(query, page, limit)

        if not programs:
            return create_response("No programs found", status=404)
        
        data = [transform_program_data(program, published_program) for program, published_program in programs]

        return create_response("Programs List", data=data, page=page, limit=limit, total=total)
        


@app_program_api.doc(security='bearer')
@app_program_api.route('/list/category')
class ProgramsListByCategory(Resource):
    @app_program_api.expect(programs_parser)
    def get(self):
        args = programs_parser.parse_args()
        search = args.get('search', '')

        data = []
        categories = g.db_session.query(Category).all()
        ## sory by order
        categories.sort(key=lambda c: c.order)

        for category in categories:
            category_dict = category.to_dict()
            category_dict["program_list"] = []

            category_id = category.id
            ## get programs by category
            programs = get_program_query(category_id=category_id, search=search, user_id=g.user_id)

            ## get programs by category
            category_dict["program_list"] = [
                {**transform_program_data(program, published_program, g.user_id), "order": order}
                for program, published_program, order in programs
            ]
            

            # ## if no programs in category, skip this category
            # if not category_dict["program_list"]:
            #     continue

            ## sort programs by order
            category_dict["program_list"].sort(key=lambda p: (p['order'] is None, p['order']))

            data.append(category_dict)

        from clientmodels import ClientProgramCategoryAssociation
        if g.tenant_id != MASTER_TENANT:
            # Step 1: Build client-level mappings
            client_prog_cat_assocs = g.client_db_session.query(ClientProgramCategoryAssociation).all()
            prog_to_cat_order = {}  # program_id -> (category_id, order)

            for assoc in client_prog_cat_assocs:
                prog_to_cat_order[assoc.program_id] = (assoc.category_id, assoc.order)

            # Step 2: Remove programs from categories if moved, and update order if needed
            progs_to_move = []  # List of (program_dict, new_cat_id, new_order)
            for category in data:
                category_id = category.get('id')
                progs_to_remove = []
                for program in category.get('program_list', []):
                    prog_id = program.get('id')
                    if prog_id in prog_to_cat_order:
                        new_cat_id, new_order = prog_to_cat_order[prog_id]
                        if new_cat_id != category_id:
                            progs_to_remove.append(program)
                            progs_to_move.append((program, new_cat_id, new_order))
                        else:
                            program['order'] = new_order
                for prog in progs_to_remove:
                    category['program_list'].remove(prog)

            # Step 3: Add/move programs to the correct category
            cat_id_to_category = {cat['id']: cat for cat in data}
            for prog_obj, cat_id, order in progs_to_move:
                target_cat = cat_id_to_category.get(cat_id)
                if target_cat:
                    prog_obj['order'] = order
                    # also update the program category list
                    prog_obj['category_list'] = [{
                        'id': target_cat['id'],
                        'name': target_cat.get('name'),
                        'description': target_cat.get('description'),
                        'order': target_cat.get('order', 0)
                    }]
                    # Avoid duplicates
                    if prog_obj not in target_cat['program_list']:
                        target_cat['program_list'].append(prog_obj)

            # Step 4: Sort programs in each category and categories themselves
            for category in data:
                if 'program_list' in category:
                    category['program_list'].sort(key=lambda x: x.get('order', 0))
            
            # Step 5: Get Client Categories for re-ordering for client level
            client_categories = g.client_db_session.query(Category).all()
            client_category_map = {cat.id: cat for cat in client_categories}
            for category in data:
                # If the category exists in client categories, update its order
                if category['id'] in client_category_map:
                    client_cat = client_category_map[category['id']]
                    category['order'] = client_cat.order
                else:
                    # If not found, set to default order (0)
                    category['order'] = 0

        # Step 6: Remove empty categories
        data = [cat for cat in data if cat.get('program_list')]

        # Step 7: Sort categories by order
        data.sort(key=lambda c: c.get('order', 0))

        return create_response("Programs List by Category", data=data)
    

@app_program_api.doc(security='bearer')
@app_program_api.route('/<string:program_id>')
class ProgramDetail(Resource):
    def get(self, program_id):
        program = g.db_session.query(Program).filter_by(id=program_id, status="Published").first()
        if not program:
            return create_response("Program not found", status=404)
        
        ## check if user has access to xperience
        user_id = g.user_id
        if user_id:
            user = g.db_session.query(User).filter(User.id == user_id).first()
            if user:
                entitlement_groups = get_user_entitlement_groups(user)
                if entitlement_groups:
                    assigned_entitlement_group_ids = entitlement_groups
                    excluded_program_ids = g.client_db_session.query(EntitlementGroupProgramAssignment.program_id).filter(
                        ~EntitlementGroupProgramAssignment.entitlement_group_id.in_(assigned_entitlement_group_ids)
                    ).distinct().all()
                    excluded_program_ids = [program_id[0] for program_id in excluded_program_ids]
                    if program.id in excluded_program_ids:
                        return create_response("Program not available", status=403)

        program_dict = program.to_dict()
        program_dict['category_list'] = [category.to_dict() for category in program.categories]
        program_dict['tag_list'] = [tag.to_dict() for tag in program.tags]
        program_dict['facet_list'] = [facet.to_dict() for facet in program.facets]

        program_dict['xperiences'] = get_program_xperiences(program, user_id=g.user_id)

        ## sort xperiences by order
        program_dict['xperiences'].sort(key=lambda x: (x['order'] is None, x['order']))

        chest_id = program.chest_id
        if chest_id:
            chest = g.db_session.query(Chest).filter(Chest.id == chest_id).first()
        else:
            chest = None

        if chest:
            program_dict['xp'] = chest.xp
        else:
            program_dict['xp'] = 0

        ## go through program xperiences and add total xp from quests inside the xperiences
        total_xp = program_dict['xp']
        for xperience in program_dict['xperiences']:
            for quest in xperience['quests']:
                total_xp += quest['xp']
        program_dict['xp'] = total_xp

        ## get cast
        cast = get_program_cast(program_dict)
        program_dict['cast'] = cast

        return create_response("Program Detail", data=program_dict)
    


@app_program_api.doc(security='bearer')
@app_program_api.route('/<string:program_id>/cast')
class XperienceCast(Resource):
    def get(self, program_id):
        program = g.db_session.query(Program).filter_by(id=program_id, status="Published").first()
        if not program:
            return create_response("Program not found", status=404)

        program_dict = program.to_dict()
        program_dict['xperiences'] = get_program_xperiences(program, user_id=g.user_id)

        cast = get_program_cast(program_dict)
        return create_response("Xperience cast retrieved successfully", data=cast)


def get_program_xperiences(program, user_id=None):
    """Get xperiences associated with a program and their quests"""
    xperiences = []
    
    # Get all ProgramXperienceAssociations for this program, ordered
    associations = g.db_session.query(ProgramXperienceAssociation).filter_by(
        program_id=program.id
    ).order_by(ProgramXperienceAssociation.order).all()
    
    xperience_ids = [assoc.xperience_id for assoc in associations]
    if not xperience_ids:
        return xperiences
    
    # Fetch all xperiences
    program_xperiences = g.db_session.query(Xperience).filter(
        Xperience.id.in_(xperience_ids)
    ).all()
    xperience_map = {x.id: x for x in program_xperiences}
    
    # Get published xperiences
    if g.tenant_id == MASTER_TENANT:
        # Get the latest revision for each xperience_id
        latest_revision_subq = (
            g.db_session.query(
                PublishedXperience.xperience_id,
                func.max(PublishedXperience.revision).label("max_revision")
            )
            .filter(PublishedXperience.xperience_id.in_(xperience_ids))
            .group_by(PublishedXperience.xperience_id)
            .subquery()
        )
        published_xperiences = (
            g.db_session.query(PublishedXperience)
            .join(latest_revision_subq, 
                  (PublishedXperience.xperience_id == latest_revision_subq.c.xperience_id) &
                  (PublishedXperience.revision == latest_revision_subq.c.max_revision))
            .all()
        )
    else:
        published_xperiences = (
            g.db_session.query(PublishedXperience)
            .filter(
                PublishedXperience.xperience_id.in_(xperience_ids),
                PublishedXperience.is_latest == True
            )
            .all()
        )
    
    published_xperience_map = {px.xperience_id: px for px in published_xperiences}
    
    # Build xperience list with their quests
    for assoc in associations:
        xperience = xperience_map.get(assoc.xperience_id)
        published_xperience = published_xperience_map.get(assoc.xperience_id)
        
        if not xperience or not published_xperience:
            continue
            
        # Transform xperience data
        xperience_dict = published_xperience.to_dict(["name", "description", "image", "level"])
        xperience_dict['id'] = xperience.id
        xperience_dict['order'] = assoc.order
        
        # Get quests for this xperience using the existing function
        quests = get_xperience_quests(xperience, published_xperience, user_id)
        xperience_dict['quests'] = quests
        
        xperiences.append(xperience_dict)
    
    return xperiences


def get_program_divider_quests(divider, user_id):
    # Get all ProgramQuestAssociations for this divider, ordered
    associations = g.db_session.query(ProgramQuestAssociation).filter_by(
        program_divider_id=divider.id
    ).order_by(ProgramQuestAssociation.order).all()
    quest_ids = [assoc.quest_id for assoc in associations]

    # Fetch all PublishedQuests in one query
    published_quests = g.db_session.query(PublishedQuest).filter(
        PublishedQuest.quest_id.in_(quest_ids)
    ).all()
    published_quest_map = {q.quest_id: q for q in published_quests}

    # Fetch all chests in one query
    chest_ids = [q.chest_id for q in published_quests if q.chest_id]
    chests = g.db_session.query(Chest).filter(Chest.id.in_(chest_ids)).all()
    chest_map = {c.id: c for c in chests}

    quests = []
    for order, quest_id in enumerate(quest_ids, start=1):
        quest = published_quest_map.get(quest_id)
        if not quest:
            continue
        quest_dict = quest.to_dict(["name", "description", "image", "level"])
        quest_dict['id'] = quest_id
        quest_dict['order'] = quest_ids.index(quest_id) + 1
        
        chest_id = quest.chest_id
        if chest_id:
            chest = g.db_session.query(Chest).filter(Chest.id == chest_id).first()
        else:
            chest = None

        if chest:
            quest_dict['xp'] = chest.xp
        else:
            quest_dict['xp'] = 0

        quests.append(quest_dict)

    return quests



def get_program_cast(program_data):
    quest_ids = []
    for xperience in program_data['xperiences']:
        for quest in xperience['quests']:
            quest_ids.append(quest['id'])

    # Get all nodes and transcripts in one query using joins
    nodes_with_transcripts = (
        g.db_session.query(Node)
        .filter(Node.quest_id.in_(quest_ids))
        .all()
    )

    # Extract unique character IDs
    character_ids = set()
    for node in nodes_with_transcripts:
        if node.transcripts:  # Check if transcripts exist
            for transcript in node.transcripts:
                if transcript and transcript.character_id:  # Add null checks
                    character_ids.add(transcript.character_id)

    # Get all characters in one query
    characters = (
        g.db_session.query(Character, User)
        .join(User, Character.user_id == User.id)
        .filter(Character.key.in_(character_ids))
        .all()
    )

    # Build cast list
    cast = []
    for character, user in characters:
        if user:
            user_dict = user.to_dict(["id", "first_name", "last_name", "preferred_name", "image", "phone_number", "email", "company", "title", "bio"])
            user_dict['character'] = character.to_dict()
            cast.append(user_dict)

    return cast



## unlock program
@app_program_api.doc(security='bearer')
@app_program_api.route('/unlock/<string:program_id>')
class UnlockProgram(Resource):
    def post(self, program_id):
        program = g.db_session.query(Program).filter_by(id=program_id).first()
        if not program:
            return create_response("Program not found", status=404)

        user_id = g.user_id
        program_id = program.id

        program_unlocked = g.db_session.query(Program).filter_by(id=program_id, status="Published").first()
        if not program_unlocked:
            return create_response("Program not found", status=404)

        user = g.db_session.query(User).filter_by(id=user_id).first()
        if not user:
            return create_response("User not found", status=404)

        keys_required = program.keys_required

        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)

        user_stats.keys_count = user_stats.keys_count if user_stats.keys_count else 0
        if user_stats.keys_count < keys_required:
            return create_response("Not enough keys to unlock this program", status=403)
        
        user_stats.keys_count -= keys_required
        g.db_session.commit()

        user_programs = g.db_session.query(UserProgram).filter_by(user_id=user_id, program_id=program_id).first()
        if not user_programs:
            user_programs = UserProgram(user_id=user_id, program_id=program_id)
            g.db_session.add(user_programs)
            g.db_session.commit()
        else:
            if user_programs.is_unlocked:
                return create_response("Program already unlocked", status=403)

        user_programs.is_unlocked = True
        g.db_session.commit()

        ## save keys used in user spending table
        user_spending = UserSpending(user_id=user_id, keys=keys_required, source="program", source_id=program_id)
        g.db_session.add(user_spending)
        g.db_session.commit()
        
        return create_response("Program unlocked successfully", data=program_unlocked.to_dict())



def get_program_query(category_id=None, search=None, user_id=None):
    """
    Returns a query for (Program, PublishedProgram[, order]) tuples,
    filtered by client, search, user entitlement, and optionally category.
    If category_id is provided, includes ProgramCategoryAssociation.order in the result.
    If g.tenant_id == MASTER_TENANT, returns all published programs without client filtering.
    """
    if g.tenant_id == MASTER_TENANT:
        # No client filtering, return all published programs
        if category_id:
            query = (
                g.db_session.query(Program, PublishedProgram, ProgramCategoryAssociation.order)
                .join(ProgramCategoryAssociation, Program.id == ProgramCategoryAssociation.program_id)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(
                    ProgramCategoryAssociation.category_id == category_id,
                    PublishedProgram.is_latest == True
                )
            )
        else:
            query = (
                g.db_session.query(Program, PublishedProgram)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(PublishedProgram.is_latest == True)
            )
    else:
        client = g.db_session.query(Client).filter(Client.id_key == g.tenant_id).first()
        if not client:
            return []

        # Get all package ids associated with the client
        client_package_ids = g.db_session.query(ClientPackageAssociation.package_id).filter(
            ClientPackageAssociation.client_id == client.id
        ).subquery()

        # Get all program ids associated with those packages
        package_program_ids = g.db_session.query(PackageProgramAssociation.program_id).filter(
            PackageProgramAssociation.package_id.in_(client_package_ids.select())
        ).subquery()

        if category_id:
            query = (
                g.db_session.query(Program, PublishedProgram, ProgramCategoryAssociation.order)
                .join(ProgramCategoryAssociation, Program.id == ProgramCategoryAssociation.program_id)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(
                    ProgramCategoryAssociation.category_id == category_id,
                    PublishedProgram.is_latest == True,
                    Program.id.in_(package_program_ids.select())
                )
            )
        else:
            query = (
                g.db_session.query(Program, PublishedProgram)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(
                    PublishedProgram.is_latest == True,
                    PublishedProgram.program_id.in_(package_program_ids.select()),
                )
            )

    # Entitlement group filtering
    if user_id:
        user = g.db_session.query(User).filter(User.id == user_id).first()
        if user:
            entitlement_group_ids = get_user_entitlement_groups(user)
            if entitlement_group_ids:
                restricted_program_subquery = (
                    g.client_db_session.query(EntitlementGroupProgramAssignment.program_id)
                    .filter(~EntitlementGroupProgramAssignment.entitlement_group_id.in_(entitlement_group_ids))
                    .group_by(EntitlementGroupProgramAssignment.program_id)
                    .having(func.count(EntitlementGroupProgramAssignment.entitlement_group_id) > 0)
                    .subquery()
                )
                query = query.filter(~Program.id.in_(restricted_program_subquery.select()))

    # Search filtering
    if search:
        query = query.filter(Program.name.ilike(f'%{search}%'))

    # Ordering
    if category_id:
        query = query.order_by(ProgramCategoryAssociation.order)

    return query



def paginate_query(query, page, limit):
    ## add exception if query is empty
    if not query:
        return 0, []
    ## add exception if page or limit is not valid
    if page < 1 or limit < 1:
        return 0, []
    ## add exception if page or limit is not valid
    if page > 1000 or limit > 1000:
        return 0, []
    
    offset = (page - 1) * limit
    total = query.count()
    items = query.offset(offset).limit(limit).all()
    return total, items



def transform_program_data(program, published_program, user_id=None):
    if not program or not published_program:
        return {}

    program_dict = published_program.to_dict()
    program_dict['id'] = program.id
    program_dict['category_list'] = [category.to_dict() for category in program.categories]
    program_dict['tag_list'] = [tag.to_dict() for tag in program.tags]
    program_dict['facet_list'] = [facet.to_dict() for facet in program.facets]

    # Get xperiences instead of dividers
    program_dict['xperiences'] = get_program_xperiences(program, user_id=user_id)

    # Fetch chest once
    chest = g.db_session.query(Chest).filter(Chest.id == program.chest_id).first() if program.chest_id else None
    program_dict['xp'] = chest.xp if chest else 0

    # Add up all quest XP
    total_xp = program_dict['xp']
    for xperience in program_dict['xperiences']:
        for quest in xperience['quests']:
            total_xp += quest['xp']
    program_dict['xp'] = total_xp

    return program_dict