"""add enable_email_notification to notification

Revision ID: 66aee7ced1b0
Revises: 
Create Date: 2025-06-11 23:23:24.820865

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '66aee7ced1b0'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('notification', schema=None) as batch_op:
        batch_op.add_column(sa.Column('enable_email_notification', sa.<PERSON>(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('notification', schema=None) as batch_op:
        batch_op.drop_column('enable_email_notification')

    # ### end Alembic commands ###
