VERSION_FILE = 'static/files/version'

def get_version():
    try:
        with open(VERSION_FILE, 'r') as file:
            version = file.read().strip()
            return version
    except FileNotFoundError:
        return '0.0.0'  # Default version if file not found

def set_version(new_version):
    with open(VERSION_FILE, 'w') as file:
        file.write(new_version)

# Initialize the version file if it doesn't exist
if __name__ == "__main__":
    if get_version() == '0.0.0':
        set_version('1.0.0')