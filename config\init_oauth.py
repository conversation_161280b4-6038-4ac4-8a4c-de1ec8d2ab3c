import json
import logging

from flask import Flask
from authlib.integrations.flask_client import OAuth
from authlib.integrations.flask_client import LocalProxy

from services.azure_key_vault import AzureKeyVault

logger = logging.getLogger(__name__)

def init_oauth(app: Flask) -> LocalProxy:
    """
    Initializes and registers the Auth0 OAuth provider with the Flask app.
    Returns the OAuth object with Auth0 registered.
    """
    # Get environment from app config
    is_production = app.config.get('IS_PRODUCTION', False)
    if not is_production:
        return None

    environment = app.config.get('ENVIRONMENT', 'dev')

    oauth = OAuth(app)
    get_secret = AzureKeyVault().get_secret
    try:
        sso_setting_key = 'sso-global-auth0-settings'

        sso_setting = get_secret(sso_setting_key)
        sso_setting = json.loads(sso_setting)
        client_id = sso_setting.get("client_id")
        client_secret = sso_setting.get("client_secret")
        api_base_url = sso_setting.get("api_base_url")
        access_token_url = 'https://' + api_base_url + '/oauth/token'
        authorize_url = 'https://' + api_base_url + '/authorize'
        jwt_url = 'https://' + api_base_url + '/.well-known/jwks.json'
        user_info_url = 'https://' + api_base_url + '/userinfo'
        scopes = sso_setting.get("scopes")

        auth0 = oauth.register(
            name='auth0',
            client_id=client_id,
            client_secret=client_secret,
            api_base_url=api_base_url,
            access_token_url=access_token_url,
            authorize_url=authorize_url,
            client_kwargs={
                'scope': 'openid profile email',
            },
            authorize_params={
                'prompt': 'login',
            },
            jwks_uri=jwt_url,
            userinfo_endpoint=user_info_url,
            userinfo_compliance_fix=lambda userinfo: userinfo
        )

        logger.info(f"Auth0 initialized successfully for {environment} environment")
        return auth0
    except Exception as e:
        error_msg = f"Error initializing Auth0 for {environment} environment: {str(e)}"
        logger.error(error_msg)
        return None
