# account.py
import datetime
import secrets

import jwt
from flask import g
from flask import json, request, session, url_for
from flask_restx import Namespace, Resource, fields, reqparse
from sqlalchemy import func

from api.common.helper import create_response
from app import app
from clientmodels import USER_TENANT
from clientmodels import get_db_session, Client, User, UserToken, UserLoginLog, UserAssignment, MasterUser, \
    UserGraphToken, Admin
from services.graph_api import GraphAPI

import logging

account_auth_api = Namespace('api_account_auth', description='Account related operations')

## SSO Redirect 
authenticate_model = account_auth_api.model('Authenticate', {
    'user_name': fields.String(required=True, description='User Email'),
    'login_type': fields.String(required=False, description='Login Type, CMS or App User. Default is app user', default='app'),
    'sso_key': fields.String(required=False, description='SSO Key, supported types: global-google, global-microsoft')
})
@account_auth_api.route('authenticate', endpoint='authenticate')
class Authenticate(Resource):
    @account_auth_api.expect(authenticate_model)
    def post(self):
        username = request.json.get('user_name', "")
        login_type = request.json.get('login_type', 'app')
        sso_key = request.json.get('sso_key', "")

        if username:
            email_suffix = username.split('@')[-1]
            db_session = get_db_session()
            client = db_session.query(Client).filter(
                func.lower(Client.email_suffix).contains(func.lower(email_suffix))
            ).first()

            try:
                client_sso_config = json.loads(client.email_suffix)
                for sso in client_sso_config:
                    if email_suffix in sso['domain']:
                        sso_key = sso['sso_id']
                        break
            except:
                client_sso_config = []

            if not client:
                return create_response("Invalid corporate email, Please try to login with Google or Microsoft", status=422)
                
        params = {'email': username, 'state': login_type, 'sso_key': sso_key}
        login_url = url_for('sso.ssologin', **params)

        data = {
            "login_url": login_url
        }
        
        ## add special account handling
        if username == "<EMAIL>":
            data = {
                "login_url": ""
            }
        return create_response("Ready to redirect", data=data)



## User Login
login_model = account_auth_api.model('Login', {
    'user_name': fields.String(required=False, description='User Email, Optional if SSO Token is provided'),
    'auth_key': fields.String(required=True, description='Authentication Password or SSO One Time Token'),
    'auth_type': fields.String(required=False, description='Authentication Type, Password: password or SSO Token: sso_token', default='sso_token'),
    'device_id': fields.String(required=False, description='Unique User Device ID'),
    'issuer': fields.String(required=False, description='Tenant ID'),
    'login_type': fields.String(required=False, description='Login Type, CMS or App User. Default is app user', default='app')
})
@account_auth_api.route('login', endpoint='login')
class Login(Resource):
    @account_auth_api.expect(login_model)
    def post(self):
        email = request.json['user_name']
        auth_key = request.json['auth_key']
        auth_type = request.json.get("auth_type", "password")
        device_id = request.json.get("device_id", "")
        tenant_id_key = request.json.get("issuer", "")
        login_type = request.json.get("login_type", "app")
        verify = False
        
        if auth_type == "password":
            # password = auth_key
            # user = g.db_session.query(User).filter_by(email=email).first()
            # if check_password_hash(user.password, password):
            #     verify = True

            if email.startswith("tester") and (email.endswith("@xapa.ai") or email.endswith("@xapa.com")):
                verify = True
                password = auth_key
                if password == "XaPa1289!!":
                    verify = True
                    tenant_id_key = "xl66rbqq8lwxz795"
                    db_session = get_db_session()
                    tenant = db_session.query(Client).filter_by(id_key=tenant_id_key).first()
                    g.db_session = get_db_session(USER_TENANT)
                    session['tenant_id_key'] = tenant.id_key
                    user = g.db_session.query(User).filter_by(email=email).first()
                    user.is_active = True
                    user.status = 'active'
                    g.db_session.add(user)
                    g.db_session.commit()
                else:
                    return create_response("Invalid username or password", status=422)
            else:
                return create_response("Auth method not supported", status=422)
            
        elif auth_type == "sso_token":
            ## get tenant id
            db_session = get_db_session()
            tenant = db_session.query(Client).filter_by(id_key=tenant_id_key).first()
            if not tenant:
                return create_response("Invalid client", status=422)
            else:
                ## after user login, all user data will stay in global tenant
                g.db_session = get_db_session(USER_TENANT)
                session['tenant_id_key'] = tenant.id_key

            user = g.db_session.query(User).filter_by(sso_redirect_token=auth_key).first()
            if user:
                ## if user is deleted, return error
                if user.is_deleted:
                    return create_response("User account is not active", status=422)

                ## check if the user is authenticated to the tenant
                try:
                    client_email_domains = tenant.email_suffix.lower() if tenant.email_suffix else ""
                    email_suffix = user.email.split('@')[-1].lower()
                    if email_suffix not in client_email_domains:
                        ## check if user has any user assignment record
                        user_assignment = g.db_session.query(UserAssignment).filter_by(user_id=user.id).first()
                        if not user_assignment:
                            user.is_active = False
                            user.status = 'unassigned'
                            g.db_session.add(user)
                            g.db_session.commit()
                        else:
                            ## update tenant id to the first assignment tenant id
                            assigned_client = db_session.query(Client).filter_by(id=user_assignment.client_id).first()
                            tenant_id_key = assigned_client.id_key
                            session['tenant_id_key'] = tenant_id_key

                        ## allow user to continue using the app api
                        if login_type not in ['app', 'web']:
                            ## for cms login, we need to verify if the user is an admin in the super admin database
                            admin = g.db_session.query(Admin).filter_by(user_id=user.id).first()
                            if admin:
                                ## update tenant id to USER tenant
                                tenant_id_key = USER_TENANT
                                session['tenant_id_key'] = tenant_id_key
                            else:
                                return create_response("Invalid corporate email", status=422)
                    
                    else:
                        user.is_active = True
                        user.status = 'active'
                        
                except Exception as e:
                    logging.error(e)
                    return create_response("Invalid corporate email", status=422)


                verify = True
                user.sso_redirect_token = None
                if user.is_active:
                    user.last_login = datetime.datetime.utcnow()

                g.db_session.add(user)
                g.db_session.commit()

        if user and verify:
            ## create master account in global database
            master_user_associate = g.db_session.query(UserAssignment).filter_by(user_id=user.id).first()
            if not master_user_associate:
                ## generate master user, with 8 digit random username
                master_user = MasterUser(
                    username=secrets.token_hex(8),
                    first_name=user.first_name,
                    last_name=user.last_name
                )
                g.db_session.add(master_user)
                g.db_session.commit()
            else:
                master_user = g.db_session.query(MasterUser).filter_by(id=master_user_associate.master_user_id).first()
                if not master_user:
                    ## generate master user, with 8 digit random username
                    master_user = MasterUser(
                        username=secrets.token_hex(8),
                        first_name=user.first_name,
                        last_name=user.last_name
                    )
                    g.db_session.add(master_user)
                    g.db_session.commit

            if tenant.id_key != USER_TENANT:
                user_assignment = g.db_session.query(UserAssignment).filter_by(
                    user_id=user.id, 
                    master_user_id=master_user.id, 
                    client_id=tenant.id
                ).first()

                if not user_assignment:    
                    ## associate user with master user
                    user_assignment = UserAssignment(
                        user_id=user.id,
                        master_user_id=master_user.id,
                        client_id=tenant.id,
                        is_verified=True
                    )
                    g.db_session.add(user_assignment)
                    g.db_session.commit()

            ## if login type is admin, check if the user is an admin in super admin db, if so, redirect them to super admin console
            if login_type == 'cms':
                admin = g.db_session.query(Admin).filter_by(user_id=user.id).first()
                if admin:
                    ## update tenant id to USER tenant
                    tenant_id_key = USER_TENANT
                    session['tenant_id_key'] = tenant_id_key


            ## create a session
            session['user_id'] = user.id

            ## create user token
            token = generate_tokens(user.id, tenant_id_key)
            print(token)

            data = {
                "access_token": token[0],
                "refresh_token": token[1],
                "expires_time": token[2].strftime("%Y-%m-%d %H:%M:%S"),
                "user_id": user.id,
                "is_active": user.is_active
            }
            print(data)

            session['user_info'] = {
                "id": user.id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "email": user.email,
                "access_token": token[0]
            }

            ## for app login, remove all other tokens for app type
            if login_type == 'app':
                g.db_session.query(UserToken).filter_by(user_id=user.id, login_type='app').delete()
                g.db_session.commit()

            ## save user token
            user_token = UserToken(
                user_id=user.id,
                access_token=token[0],
                refresh_token=token[1],
                date_expired=token[2],
                device_id=device_id,
                login_type=login_type
            )

            ## get device type from header "X-OS" value
            try:
                device_type = request.headers['X-OS']
                if device_type:
                    user_token.device_type = device_type
            except:
                pass

            g.db_session.add(user_token)

            ## save login history
            login_log = UserLoginLog(
                user_id=user.id,
                device_id=device_id,
                date_created=datetime.datetime.utcnow(),
                login_type=login_type,
                is_success=True
            )
            g.db_session.add(login_log)

            ## get user ip address
            try:
                user_ip = request.remote_addr
                if user_ip == "":
                    user_ip = request.headers['X-Real-IP']

                if user_ip:
                    login_log.ip_address = user_ip
            except:
                pass

            ## get user agent
            try:
                user_agent = request.user_agent.string
                if user_agent:
                    login_log.user_agent = user_agent
            except:
                pass

            g.db_session.commit()

            ## copy user data to client tenant
            from api.common.users_utils import sync_user
            sync_user(user, tenant.id_key)

            ## get admin information if login type is cms
            if login_type == 'cms':
                client_tenant_session = get_db_session(tenant_id_key)
                admin = client_tenant_session.query(Admin).filter_by(user_id=user.id).first()
                if admin:
                    data['role'] = admin.role if admin.role else ''
                    admin.last_login = datetime.datetime.utcnow()
                    client_tenant_session.add(admin)
                    client_tenant_session.commit()
                else:
                    data['role'] = ''

            cookie = {
                "access_token": token[0],
                "refresh_token": token[1],
                f"refresh_token_{login_type}": token[1],
                "expires_time": token[2].strftime("%Y-%m-%d %H:%M:%S"),
                "user_id": user.id
            }

            ## try to get user profile from graph api
            graph_api = GraphAPI(user.id, tenant_id_key)
            graph_token = g.db_session.query(UserGraphToken).filter_by(user_id=user.id).first()
            if graph_token:
                print("Graph Token Exists, Get User Profile")
                graph_api.init_user_profile(user)

            return create_response("Logged in successfully", data=data, cookie=cookie)
        else:
            return create_response("Invalid username or password", status=422)



## user logout
parser = reqparse.RequestParser()
parser.add_argument('type', type=str, help='Type of logout, app or cms', default='app')

@account_auth_api.doc(security='bearer')
@account_auth_api.route('logout', endpoint='logout')
class Logout(Resource):
    @account_auth_api.expect(parser)
    def get(self):
        login_type = parser.parse_args().get('type', 'app')

        ## remove the user token
        user_id = g.user_id
        user = g.db_session.query(User).filter_by(id=user_id).first()

        ## remove all user token for the login type
        g.db_session.query(UserToken).filter_by(user_id=user_id, login_type=login_type).delete()
        g.db_session.commit()

        ## remove session
        session.clear()

        return create_response("Logged out successfully")


## refresh token
refresh_token_model = account_auth_api.model('RefreshToken', {
    'refresh_token': fields.String(required=True, description='Refresh Token'),
    'login_type': fields.String(required=False, description='Login Type, CMS or App User. Default is app user', default='app')
})

@account_auth_api.doc(security='bearer')
@account_auth_api.route('refresh_token', endpoint='refresh_token')
class RefreshToken(Resource):
    @account_auth_api.expect(refresh_token_model)
    def post(self):
        from services.azure_key_vault import AzureKeyVault
        get_secret = AzureKeyVault().get_secret

        ## check if refresh token is in request body
        refresh_token = request.json.get('refresh_token', "")

        ## check login type
        login_type = request.json.get('login_type', 'app')

        if not refresh_token:
            ## check if refresh token is in cookie
            refresh_token = request.cookies.get('refresh_token', "")

            ## check if refresh token is in cookie with login type
            refresh_token = request.cookies.get(f'refresh_token_{login_type}', "")
            
            if not refresh_token:
                logging.info("No refresh token found")
                return create_response("Invalid token", status=401)

        try:
            # Verify the refresh token
            secret_key = app.config.get('SECRET_KEY')
            decoded = jwt.decode(refresh_token, secret_key, algorithms=['HS256'], audience='api')
            user_id = decoded['sub']
            tenant_id = decoded['iss']

            # Check if token is expired
            exp_timestamp = decoded['exp']
            if datetime.datetime.fromtimestamp(exp_timestamp) < datetime.datetime.utcnow():
                logging.info("Token expired")
                return create_response("Token expired", status=401)


            g.db_session = get_db_session(USER_TENANT)
            # Verify token exists in database
            user_token = g.db_session.query(UserToken).filter_by(
                user_id=user_id,
                refresh_token=refresh_token
            ).first()

            if not user_token:
                logging.info("Cannot find token in database")
                return create_response("Invalid token", status=401)

            ## verify the refresh token, if the login_type is CMS, verify the create time of access token 
            ## if the access token is created more than 180 mins ago, then remove the token and return 401 status
            if user_token.login_type == 'cms':
                cms_timeout = get_secret('CMS_TOKEN_TIMEOUT')
                if not cms_timeout:
                    cms_timeout = 180
                else:
                    cms_timeout = int(cms_timeout)

                ## get the access token create time by decode it
                access_token = user_token.access_token
                try:
                    decoded_access_token = jwt.decode(access_token, secret_key, algorithms=['HS256'], audience='api')
                    access_token_create_time = datetime.datetime.fromtimestamp(decoded_access_token['iat'], tz=datetime.timezone.utc)
                    if datetime.datetime.now(datetime.timezone.utc) - access_token_create_time > datetime.timedelta(minutes=cms_timeout):
                        logging.info("Access token expired")
                        g.db_session.delete(user_token)
                        g.db_session.commit()
                        ## remove session
                        session.clear()
                        return create_response("Access token expired", status=401)
                except jwt.ExpiredSignatureError as e:
                    logging.info(f"Access token expired: {str(e)}")
                    g.db_session.delete(user_token)
                    g.db_session.commit()
                    ## remove session
                    session.clear()
                    return create_response(f"Access token expired: {str(e)}", status=401)
                except jwt.InvalidTokenError as e:
                    logging.info(f"Invalid access token: {str(e)}") 
                    g.db_session.delete(user_token)
                    g.db_session.commit()
                    ## remove session
                    session.clear()
                    return create_response(f"Invalid access token: {str(e)}", status=401)
            
            
            # Generate new access token only (keep the same refresh token)
            token = generate_tokens(user_id, tenant_id)
            
            data = {
                "access_token": token[0],
                "refresh_token": refresh_token,  # Return the same refresh token
                "expires_time": token[2].strftime("%Y-%m-%d %H:%M:%S"),
                "user_id": user_id
            }

            # Update only the access token in database
            user_token.access_token = token[0]
            user_token.date_expired = token[2]
            g.db_session.add(user_token)
            g.db_session.commit()

            cookie = {
                "access_token": token[0],
                "refresh_token": refresh_token,
                f"refresh_token_{login_type}": refresh_token,
                "expires_time": token[2].strftime("%Y-%m-%d %H:%M:%S"),
                "user_id": user_id
            }

            user = g.db_session.query(User).filter_by(id=user_id).first()
            if user:
                session['user_info'] = {
                    "id": user.id,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "access_token": token[0]
                }

            return create_response("Token refreshed successfully", data=data, cookie=cookie)

        except jwt.ExpiredSignatureError as e:
            logging.info(f"Token expired: {str(e)}")
            return create_response(f"Token expired: {str(e)}", status=401)
        except jwt.InvalidTokenError as e:
            logging.info(f"Invalid token: {str(e)}")
            return create_response(f"Invalid token: {str(e)}", status=401)
        except Exception as e:
            logging.info(f"Error refreshing token: {str(e)}")
            return create_response(f"Error refreshing token: {str(e)}", status=401)


## Update Device Token
device_token_model = account_auth_api.model('Device Token', {
    'device_id': fields.String(required=False, description='Device ID'),
    'device_token': fields.String(required=True, description='Device Token'),
})

@account_auth_api.doc(security='bearer')
@account_auth_api.route('device_token', endpoint='device_token')
class DeviceToken(Resource):
    @account_auth_api.expect(device_token_model)
    def post(self):
        user_id = g.user_id
        user = g.db_session.query(User).filter_by(id=user_id).first()
        if user:
            data = request.json
            device_id = data.get("device_id", "")
            device_token = data.get("device_token", "")
            if device_id and device_token:
                user_token = g.db_session.query(UserToken).filter_by(user_id=user_id, device_id=device_id).first()
                if not user_token:
                    ## get device by user token
                    authorization_header = request.headers.get('Authorization')
                    if not authorization_header:
                        return create_response("Invalid token", status=401)
                    
                    parts = authorization_header.split()
                    if parts[0].lower() != 'bearer' or len(parts) == 1 or len(parts) > 2:
                        return create_response('Invalid token', status=401)

                    token = parts[1]
                    user_token = g.db_session.query(UserToken).filter_by(access_token=token).first()

                if user_token:
                    user_token.device_token = device_token

                    ## get device type from header "X-OS" value
                    try:
                        device_type = request.headers['X-OS']
                        if device_type:
                            user_token.device_type = device_type
                    except:
                        pass

                    g.db_session.add(user_token)
                    g.db_session.commit()
                    return create_response("Update Device Token")
                else:
                    return create_response("Invalid token", status=401)
                
            return create_response("Update Device Token")
        else:
            return create_response("Invalid user", status=401)     


def generate_tokens(user_id, tenant_id):
    payload = {
        # 'exp': datetime.datetime.utcnow() + datetime.timedelta(days=0, minutes=60),
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=14),
        'iat': datetime.datetime.utcnow(),
        'sub': user_id,
        'aud': 'api',
        'iss': tenant_id
    }
    print(payload)

    secret_key = app.config.get('SECRET_KEY')
    access_token = jwt.encode(
        payload,
        secret_key,  # get secret key from environment variable
        algorithm='HS256'
    )

    refresh_payload = {
        # 'exp': datetime.datetime.utcnow() + datetime.timedelta(days=0, minutes=360),
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=60),
        'iat': datetime.datetime.utcnow(),
        'sub': user_id,
        'aud': 'api',
        'iss': tenant_id
    }

    refresh_token = jwt.encode(
        refresh_payload,
        secret_key,  # get secret key from environment variable
        algorithm='HS256'
    )

    return access_token, refresh_token, payload['exp']
