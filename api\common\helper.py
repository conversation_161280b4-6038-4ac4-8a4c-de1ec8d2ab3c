import datetime
import re

import pytz
from flask import g, jsonify, session

from api.common.messages import Messages
from app import app


def format_datetime_with_timezone(dt, timezone='UTC'):
    if dt is None:
        return ""
    
    if not isinstance(dt, datetime.datetime):
        return ""
    
    if dt.tzinfo is None:
        dt = pytz.UTC.localize(dt)
    else:
        dt = dt.astimezone(pytz.UTC)

    target_tz = pytz.timezone(timezone)
    localized_time = dt.astimezone(target_tz)
    return localized_time.strftime('%Y-%m-%d %H:%M:%SZ')


def get_local_today():
    try:
        timezone = g.timezone
        local_tz = pytz.timezone(timezone)  
        today = datetime.datetime.now(local_tz).date()
        print("Current Timezone:", timezone)
    except Exception as e:
        print(str(e))
        today = datetime.datetime.today().date()

    return today


def parse_bool_or_none(value):
    """
    Custom type for parsing boolean or None values.
    
    Accepts:
    - None
    - 'true', 'True', 'TRUE' -> True
    - 'false', 'False', 'FALSE' -> False
    - Raises ValueError for other inputs
    """
    if value is None:
        return None
    
    if isinstance(value, bool):
        return value
    
    if isinstance(value, str):
        lower_value = value.lower()
        if lower_value == 'true':
            return True
        elif lower_value == 'false':
            return False
    
    raise ValueError(f'Invalid boolean value: {value}')


def create_response(message, status=200, **kwargs):
    ## check if message has translation
    message = Messages().get_message(message, session.get("language", "en"))

    response_message = {
        "status": status, 
        "message": message,
        "version": app.config.get('API_VERSION', '1.0.0'),
        "environment": app.config.get("ENVIRONMENT", "dev")
    }

    ## if "data" not in kwargs, add it
    if "data" not in kwargs:
        kwargs["data"] = {}
        
    response_message.update(kwargs)

    if "cookie" in kwargs:
        ## remove cookie from response message
        response_message.pop("cookie", None)

    response = jsonify(response_message)
    response.status_code = status

    if "cookie" in kwargs:
        for key, value in kwargs["cookie"].items():
            response.set_cookie(
                key, 
                value, 
                httponly=True, 
                secure=True,
                samesite='None',  # Allow cross-site cookie
                domain='.xapa.ai',  # Make cookie accessible across subdomains
                path='/'  # Make cookie available on all paths
            )
    
    return response


def parse_mentions(message):
    """
    Parse @mentions from comment message in the format [@username](/people/:user_id)
    Returns a list of user IDs that were mentioned
    """
    # Regex pattern to match [@username](/people/:user_id)
    mention_pattern = r'\[@[^\]]+\]\(/people/([^)]+)\)'
    
    # Find all matches and extract user IDs
    matches = re.findall(mention_pattern, message)
    
    # Return unique user IDs
    return list(set(matches))
