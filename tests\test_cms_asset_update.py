import json
import unittest
from unittest.mock import patch, MagicMock, Mock


class TestCMSAssetUpdate(unittest.TestCase):
    """
    Test to verify that the CMS asset PUT method processes file uploads correctly
    when replacing existing files, even if the file path appears the same.
    """

    @patch('api.cms.asset.g')
    @patch('api.cms.asset.FileService')
    @patch('api.cms.asset.request')
    def test_asset_update_should_process_file_when_same_path_provided(self, mock_request, mock_file_service, mock_g):
        """Test that uploading a new file with same path structure replaces the old file"""
        # Import the module after mocking to avoid import errors
        from api.cms.asset import AssetItem
        
        # Setup mock asset
        mock_asset = MagicMock()
        mock_asset.id = 'test_asset_id'
        mock_asset.name = 'Test Asset'
        mock_asset.description = 'Test Description'
        mock_asset.file_type = 'image'
        mock_asset.file_path = 'temp/existing_file.jpg'
        mock_asset.to_dict.return_value = {
            'id': 'test_asset_id',
            'name': 'Updated Asset',
            'description': 'Updated Description',
            'file_type': 'image',
            'file_path': 'asset/test_asset_id/new_file.jpg'
        }

        # Setup mock database session
        mock_db_session = MagicMock()
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_asset
        mock_g.client_db_session = mock_db_session

        # Setup mock FileService
        mock_file_service.process_entity_image.return_value = True

        # Setup mock request
        mock_request.json = {
            'name': 'Updated Asset',
            'description': 'Updated Description', 
            'file_type': 'image',
            'file_path': 'temp/existing_file.jpg'  # Same path as existing file
        }

        # Create instance and call PUT method
        asset_item = AssetItem()
        response = asset_item.put('test_asset_id')

        # Verify that FileService.process_entity_image was called
        # This is the key test - it should be called even when file_path is the same
        # But currently it won't be called due to the bug we're fixing
        print("FileService.process_entity_image called:", mock_file_service.process_entity_image.called)
        print("Number of calls:", mock_file_service.process_entity_image.call_count)
        
        # With the current bug, this will fail because the file won't be processed
        # when file_path is the same as the existing one
        if mock_asset.file_path == 'temp/existing_file.jpg':
            # The current logic will NOT call process_entity_image because the paths match
            mock_file_service.process_entity_image.assert_not_called()
            print("BUG CONFIRMED: FileService.process_entity_image was not called when paths match")
        else:
            # After our fix, it should always be called when a file_path is provided
            mock_file_service.process_entity_image.assert_called_once_with(
                mock_asset, 'temp/existing_file.jpg', 'asset', 'test_asset_id'
            )

    @patch('api.cms.asset.g')  
    @patch('api.cms.asset.FileService')
    @patch('api.cms.asset.request')
    def test_asset_update_should_not_process_file_when_no_file_path(self, mock_request, mock_file_service, mock_g):
        """Test that no file processing occurs when no file_path is provided"""
        # Import the module after mocking
        from api.cms.asset import AssetItem
        
        # Setup mock asset
        mock_asset = MagicMock()
        mock_asset.id = 'test_asset_id'
        mock_asset.name = 'Test Asset'
        mock_asset.description = 'Test Description'
        mock_asset.file_type = 'image'
        mock_asset.file_path = 'asset/test_asset_id/existing_file.jpg'
        mock_asset.to_dict.return_value = {
            'id': 'test_asset_id',
            'name': 'Updated Asset',
            'description': 'Updated Description',
            'file_type': 'image',
            'file_path': 'asset/test_asset_id/existing_file.jpg'
        }

        # Setup mock database session
        mock_db_session = MagicMock()
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_asset
        mock_g.client_db_session = mock_db_session

        # Setup mock request - no file_path provided
        mock_request.json = {
            'name': 'Updated Asset',
            'description': 'Updated Description',
            'file_type': 'image'
        }

        # Create instance and call PUT method
        asset_item = AssetItem()
        response = asset_item.put('test_asset_id')

        # Verify that FileService.process_entity_image was NOT called
        mock_file_service.process_entity_image.assert_not_called()
        print("CORRECT: FileService.process_entity_image was not called when no file_path provided")


if __name__ == '__main__':
    unittest.main()

    @patch('api.cms.asset.g')
    @patch('api.cms.asset.FileService')
    def test_asset_update_should_process_file_when_same_path_provided(self, mock_file_service, mock_g):
        """Test that uploading a new file with same path structure replaces the old file"""
        # Setup mock asset
        mock_asset = MagicMock()
        mock_asset.id = 'test_asset_id'
        mock_asset.name = 'Test Asset'
        mock_asset.description = 'Test Description'
        mock_asset.file_type = 'image'
        mock_asset.file_path = 'temp/existing_file.jpg'
        mock_asset.to_dict.return_value = {
            'id': 'test_asset_id',
            'name': 'Updated Asset',
            'description': 'Updated Description',
            'file_type': 'image',
            'file_path': 'asset/test_asset_id/new_file.jpg'
        }

        # Setup mock database session
        mock_db_session = MagicMock()
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_asset
        mock_g.client_db_session = mock_db_session

        # Setup mock FileService
        mock_file_service.process_entity_image.return_value = True

        # Test data - simulating a new file upload with same temp path
        test_data = {
            'name': 'Updated Asset',
            'description': 'Updated Description', 
            'file_type': 'image',
            'file_path': 'temp/existing_file.jpg'  # Same path as existing file
        }

        # Make the PUT request
        response = self.client.put(
            '/cms/asset/test_asset_id',
            data=json.dumps(test_data),
            headers=self.headers
        )

        # Verify the response
        self.assertEqual(response.status_code, 200)
        
        # Verify that FileService.process_entity_image was called
        # This is the key test - it should be called even when file_path is the same
        mock_file_service.process_entity_image.assert_called_once_with(
            mock_asset, 'temp/existing_file.jpg', 'asset', 'test_asset_id'
        )

    @patch('api.cms.asset.g')  
    @patch('api.cms.asset.FileService')
    def test_asset_update_should_not_process_file_when_no_file_path(self, mock_file_service, mock_g):
        """Test that no file processing occurs when no file_path is provided"""
        # Setup mock asset
        mock_asset = MagicMock()
        mock_asset.id = 'test_asset_id'
        mock_asset.name = 'Test Asset'
        mock_asset.description = 'Test Description'
        mock_asset.file_type = 'image'
        mock_asset.file_path = 'asset/test_asset_id/existing_file.jpg'
        mock_asset.to_dict.return_value = {
            'id': 'test_asset_id',
            'name': 'Updated Asset',
            'description': 'Updated Description',
            'file_type': 'image',
            'file_path': 'asset/test_asset_id/existing_file.jpg'
        }

        # Setup mock database session
        mock_db_session = MagicMock()
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_asset
        mock_g.client_db_session = mock_db_session

        # Test data - no file_path provided
        test_data = {
            'name': 'Updated Asset',
            'description': 'Updated Description',
            'file_type': 'image'
        }

        # Make the PUT request
        response = self.client.put(
            '/cms/asset/test_asset_id',
            data=json.dumps(test_data),
            headers=self.headers
        )

        # Verify the response
        self.assertEqual(response.status_code, 200)
        
        # Verify that FileService.process_entity_image was NOT called
        mock_file_service.process_entity_image.assert_not_called()


if __name__ == '__main__':
    unittest.main()