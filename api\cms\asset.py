from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.file import FileService
from api.common.helper import create_response
from clientmodels import Asset, ChestAssetAssociation, UserAsset

cms_asset_api = Namespace('api_cms_asset', description='Asset management operations')


# Asset model for API documentation and validation
asset_model = cms_asset_api.model('AssetItem', {
    'name': fields.String(required=True, description='Asset Name'),
    'description': fields.String(required=False, description='Asset Description'),
    'file_type': fields.String(required=True, description='Asset Type (image, video, document, etc.)'),
    'file_path': fields.String(required=True, description='Asset File Path'),
})


@cms_asset_api.doc(security='bearer')
@cms_asset_api.route('/', methods=['POST'])
@cms_asset_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class AssetItem(Resource):
    def get(self, id):
        """Get a specific asset"""
        asset = g.client_db_session.query(Asset).filter_by(id=id).first()
        if asset is None or asset.is_deleted:
            return create_response("Asset not found", status=404)

        return create_response("Asset retrieved successfully", 
                             data=asset.to_dict())

    @cms_asset_api.expect(asset_model)
    def post(self):
        """Create a new asset"""
        # Parse request data
        data = request.json

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        file_type = data.get('file_type', '').strip()
        asset = Asset()

        # Create asset
        asset.name=name
        asset.description=description
        asset.file_type=file_type
        # asset.file_path=file_path
        
        g.client_db_session.add(asset)
        g.client_db_session.flush()

        # Handle file upload or link
        file_path = data.get('file_path', '').strip()
        if file_path:
            if file_type == 'link':
                asset.file_path = file_path
            else:
                FileService.process_entity_image(asset, file_path, 'asset', asset.id, 'file_path')

        g.client_db_session.add(asset)
        g.client_db_session.commit()

        return create_response("Asset created successfully", data=asset.to_dict())

    @cms_asset_api.expect(asset_model)
    def put(self, id):
        """Update asset metadata"""
        data = request.json

        asset = g.client_db_session.query(Asset).filter_by(id=id).first()
        if asset is None:
            return create_response("Asset not found", status=404)

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        file_type = data.get('file_type', '').strip()

        # Update asset fields
        asset.name = name
        asset.description = description
        asset.file_type = file_type

        # Handle file upload
        file_path = data.get('file_path', '').strip()
        if file_path:
            if file_type == 'link':
                asset.file_path = file_path
            else:
                FileService.process_entity_image(asset, file_path, 'asset', asset.id, 'file_path')

        g.client_db_session.add(asset)
        g.client_db_session.commit()

        return create_response("Asset updated successfully", data=asset.to_dict())

    def delete(self, id):
        """Delete an asset"""
        asset = g.client_db_session.query(Asset).filter_by(id=id).first()
        if asset is None:
            return create_response("Asset not found", status=404)
        
        ## Soft delete
        asset.is_deleted = True
        g.client_db_session.add(asset)
        g.client_db_session.commit()

        ## remove user relationship
        user_assets = g.db_session.query(UserAsset).filter_by(asset_id=id).all()
        for user_asset in user_assets:
            g.db_session.delete(user_asset)
        g.db_session.commit()

        ## remove chest relationship
        chest_assets = g.db_session.query(ChestAssetAssociation).filter_by(asset_id=id).all()
        for chest_asset in chest_assets:
            g.db_session.delete(chest_asset)
        g.db_session.commit()

        # g.client_db_session.delete(asset)
        # g.client_db_session.commit()

        return create_response("Asset deleted successfully")


# List parser
asset_list_parser = cms_asset_api.parser()
asset_list_parser.add_argument('page', type=int, help='Page number', default=1)
asset_list_parser.add_argument('limit', type=int, help='Items per page', default=20)
asset_list_parser.add_argument('search', type=str, help='Search query', default='')
asset_list_parser.add_argument('type', type=str, help='Filter by asset type', default='')
asset_list_parser.add_argument('sort', type=str, help='Sort by asset type', default='')


@cms_asset_api.doc(security='bearer')
@cms_asset_api.route('/list')
class AssetList(Resource):
    @cms_asset_api.expect(asset_list_parser)
    def get(self):
        """Get list of assets"""
        args = asset_list_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        asset_type = args.get('type', '')
        sort = args.get('sort', None)
        offset = (page - 1) * limit

        # Build query
        query = g.client_db_session.query(Asset).filter(Asset.is_deleted == False)
        
        # Apply filters
        if search:
            query = query.filter(Asset.name.ilike(f'%{search}%'))

        if asset_type:
            query = query.filter(Asset.type == asset_type)

        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(Asset, sort[1:]).desc())
            else:
                query = query.order_by(getattr(Asset, sort))
        else:
            query = query.order_by(Asset.name)

        # Get total count
        total = query.count()
        
        # Get paginated results
        assets = query.offset(offset).limit(limit).all()
        
        # Format response
        data = [asset.to_dict() for asset in assets]
        
        return create_response("Asset list retrieved successfully", data=data, total=total, page=page, limit=limit)
