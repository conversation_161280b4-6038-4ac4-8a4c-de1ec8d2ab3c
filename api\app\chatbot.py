from flask import g, json, request, session
from flask_restx import Namespace, Resource, fields
import requests
from app import app

from api.common.helper import create_response

app_chatbot_api = Namespace('api_app_chatbot', description='Chatbot related operations')


chatbot_model = app_chatbot_api.model('ChatbotConfig', {
    'chatInput': fields.String(required=True, description='Input for the chatbot'),
})

CHATBOT_WEBHOOK_URL = app.config.get("CHATBOT_WEBHOOK_URL", "")

@app_chatbot_api.doc(security='bearer')
@app_chatbot_api.route('/')
class Chatbot(Resource):
    @app_chatbot_api.expect(chatbot_model)
    def post(self):
        """
        Update the chatbot configuration.
        """
        data = request.get_json()
        if not data:
            return create_response({'error': 'No data provided'}, status=400)

        chat_input = data.get('chatInput')
        if not chat_input:
            return create_response({'error': 'chatInput is required'}, status=400)
        
        # Send data to n8n webhook
        n8n_webhook_url = CHATBOT_WEBHOOK_URL  
        ## create a unique session ID for the session
        chat_session_id = request.cookies.get('chat_session_id', None)
        if not chat_session_id:
            ## create a new session ID of random characters
            # import random
            # import string
            # session_id = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
            # chat_session_id = session_id
            chat_session_id = g.user_id

        try:
            headers = {
                "Content-Type": "application/json"
            }
            n8n_response = requests.post(
                n8n_webhook_url,
                json={
                    "chatInput": chat_input,
                    "sessionId": chat_session_id
                },
                timeout=60,
                headers=headers
            )
            ## get the response from n8n
            n8n_data = n8n_response.json()
            print(f'n8n response: {n8n_data}')
            output = n8n_data.get('output', "")

            ## after we get the output, 
            # check the links inside the output 
            # if it matches "https://gostage.xapa.ai/#/xperiences/xperience/{xperience_id}" 
            # and g.user_login_type is app, the replace the link with "xapa://page/xperiences/xperience/{xperience_id}"
            if output:
                if g.user_login_type == 'app':
                    import re
                    pattern = r'https://gostage\.xapa\.ai/#/xperiences/xperience/([a-zA-Z0-9\-]+)'
                    output = re.sub(pattern, r'/xperiences/xperience/\1', output)

        except requests.RequestException as e:
            print(f'Error calling n8n webhook: {str(e)}')
            data = {
                'output': "Error processing request, please try again later."
            }
            return create_response('Error processing chatbot request', status=500)
        
        data = {
            'output': output
        }

        cookie = {
            "chat_session_id": chat_session_id
        }
        return create_response("Chatbot response processed successfully", data=data, cookie=cookie)
    



parser = app_chatbot_api.parser()
parser.add_argument('xperienceId', type=str, required=False, help='Xperience ID to fetch embedding data for', location='args')

@app_chatbot_api.doc(security='bearer')
@app_chatbot_api.route('/nodes')
class ChatbotNodes(Resource):
    @app_chatbot_api.expect(parser)
    def get(self):
        """
        Test getting the embedding data from the database.
        """

        from clientmodels import Xperience, Quest, Node, NodeOption, NodeTranscript, NodeOptionMatching
        from clientmodels import Client, Package

        xperiences_mapping = {}
        quest_mapping = {}
        ## get all nodes from the database

        xperience_id = request.args.get('xperienceId')

        if not xperience_id:
            xperiences = g.db_session.query(Xperience).all()
        else:
            xperiences = g.db_session.query(Xperience).filter(Xperience.id == xperience_id).all()

        clients = g.db_session.query(Client).all()
        client_mapping = {}
        for client in clients:
            client_mapping[client.id] = {
                'name': client.name,
                'id': client.id,
                'package_ids': [package.id for package in client.packages] if client.packages else []
            }

        # print(f'client_mapping: {client_mapping}')

        for xperience in xperiences:
            # Get package IDs for this xperience
            xperience_package_ids = [package.id for package in xperience.packages] if xperience.packages else []
            # print(f'xperience_package_ids: {xperience_package_ids}')
            
            # Find clients that have matching package IDs with this xperience
            client_ids = []
            for client_id, client_data in client_mapping.items():
                # Check if any package IDs match between client and xperience
                if any(pkg_id in client_data['package_ids'] for pkg_id in xperience_package_ids):
                    client_ids.append(client_id)

            xperiences_mapping[xperience.id] = {
                'name': xperience.name,
                'description': xperience.description,
                'id': xperience.id,
                'quest_ids': [quest.id for quest in xperience.quests] if xperience.quests else [],
                'package_ids': xperience_package_ids,
                'client_ids': client_ids
            }

        # print(f'xperiences_mapping: {xperiences_mapping}')
        
        quests = g.db_session.query(Quest).all()
        for quest in quests:
            quest_mapping[quest.id] = {
                'name': quest.name,
                'description': quest.description,
                'id': quest.id
            }

        nodes = g.db_session.query(Node).all()
        options = g.db_session.query(NodeOption).all()
        transcripts = g.db_session.query(NodeTranscript).all()

        # Group options and transcripts by node_id
        from collections import defaultdict
        options_by_node = defaultdict(list)
        for option in options:
            options_by_node[option.node_id].append({
                'text': option.label,
                'is_correct': option.is_correct
            })

        transcripts_by_node = defaultdict(list)
        for transcript in transcripts:
            transcripts_by_node[transcript.node_id].append({
                'text': transcript.text
            })

        result = []
        for node in nodes:
            ## get content from quest_text, options_by_node and transcripts_by_node and merge it into a single string
            quest_text = node.quest_text if node.quest_text else ""
            options = options_by_node.get(node.id, [])
            transcripts = transcripts_by_node.get(node.id, [])
            # If there is any correct option, only get the correct options, otherwise get all options
            correct_options = [opt for opt in options if opt['is_correct']]
            if correct_options:
                options_to_use = correct_options
            else:
                options_to_use = options
            options_text = "\n".join([f"{opt['text']}" for opt in options_to_use])
            transcripts_text = "\n".join([transcript['text'] for transcript in transcripts])

            content_text = f"{quest_text}\n\n{options_text}\n\n{transcripts_text}"

            ## get quest data from node quest_id
            quest_data = quest_mapping.get(node.quest_id, {})
            ## get xperience data from node quest id and match it with xperiences_mapping quest_ids
            xperience_data = {}
            for xperience in xperiences_mapping.values():
                if node.quest_id in xperience['quest_ids']:
                    xperience_data = xperience
                    break

            node_data = {
                'id': node.id,
                'quest_id': node.quest_id,
                'quest_name': quest_data.get('name', ''),
                'quest_description': quest_data.get('description', ''),
                'xperience_id': xperience_data.get('id', ''),
                'xperience_name': xperience_data.get('name', ''),
                'xperience_description': xperience_data.get('description', ''),
                'xperience_packages': xperience_data.get('package_ids', []),
                'xperience_clients': xperience_data.get('client_ids', []),
                'content_text': content_text
            }

            result.append(node_data)

        return create_response("Chatbot test endpoint is working", data=result)
    


@app_chatbot_api.doc(security='bearer')
@app_chatbot_api.route('/people')
class ChatbotPeople(Resource):
    ## There're four fixed type of communication style:
    COMMUNICATION_STYLE_MAPPING = {
        "06795cdd-6dd0-7556-8000-1d7bd8edb233": "Stabilizer",
        "06795cdc-fa48-77a6-8000-01a20f68ca8f": "Driver",
        "06795cdd-4893-7272-8000-856889fd0e62": "Influencer",
        "06795cdd-aa70-7a4f-8000-5d9c88a3bc13": "Deliberator"
    }

    def get(self):
        from clientmodels import UserNodeHistory
        from clientmodels import User

        ## Get Communication Style Id List
        communication_style_node_ids = list(self.COMMUNICATION_STYLE_MAPPING.keys())

        # Search in the user node history table where node_id in communication_style_node_ids
        records = (
            g.db_session.query(UserNodeHistory, User)
            .join(User, UserNodeHistory.user_id == User.id)
            .filter(UserNodeHistory.node_id.in_(communication_style_node_ids))
            .order_by(UserNodeHistory.date_completed.desc())
            .all()
        )

        user_styles = {}
        for record, user in records:
            user_id = record.user_id
            if user_id not in user_styles:
                style_name = self.COMMUNICATION_STYLE_MAPPING.get(record.node_id, "Unknown")
                user_styles[user_id] = {
                    "user_id": user_id,
                    "communication_style_id": record.node_id,
                    "communication_style": style_name,
                    "date_completed": record.date_completed,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "title": getattr(user, "title", ""),
                    "company": getattr(user, "company", ""),
                    "bio": getattr(user, "bio", "")
                }

        result = list(user_styles.values())
        return create_response("User communication styles fetched successfully", data=result)



    



