import datetime

import pytz
from flask import g
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from clientmodels import User, UserPowerUp

app_addon_api = Namespace('api_app_user_addon', description='Boost and Powerup related operations')


@app_addon_api.doc(security='bearer')
@app_addon_api.route('/powerup/start')
class PowerUpStart(Resource):
    def post(self):
        user_id = g.user_id

        ## get current time for user with timezone
        user = g.db_session.query(User).filter_by(id=user_id).first()
        if not user:
            return create_response("User not found", status=404)
        
        timezone_name = user.timezone if user.timezone else "UTC"
        try:
            timezone = pytz.timezone(timezone_name)
        except pytz.UnknownTimeZoneError:
            return create_response("Invalid timezone", status=400)

        current_time = datetime.datetime.now(pytz.utc).astimezone(timezone)
        start_of_day = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = current_time.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Convert start and end of day to UTC
        start_of_day_utc = start_of_day.astimezone(datetime.timezone.utc)
        end_of_day_utc = end_of_day.astimezone(datetime.timezone.utc)

        # Check if a power up within the time already exists
        user_powerup = g.db_session.query(UserPowerUp).filter(
            UserPowerUp.user_id == user_id,
            UserPowerUp.start >= start_of_day_utc,
            UserPowerUp.end <= end_of_day_utc,
            UserPowerUp.source == "daily"
        ).first()

        if user_powerup:
            return create_response("Power Up already active within the time range")

        user_powerup = UserPowerUp(user_id=user_id)
        user_powerup.start = datetime.datetime.utcnow()
        user_powerup.end = datetime.datetime.utcnow() + datetime.timedelta(minutes=15)
        user_powerup.source = "daily"
        g.db_session.add(user_powerup)
        g.db_session.commit()
        data = user_powerup.to_dict()
        return create_response("Power Up started", data=data)



