# For more information, please refer to https://aka.ms/vscode-docker-python
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Install system dependencies required for xmlsec
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    # libxml2-dev \
    # libxmlsec1-dev \
    # libxmlsec1-openssl \
    # zlib1g-dev \
    pkg-config && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install any needed packages specified in requirements.txt
RUN python -m pip install --upgrade pip && \
    # python -m pip install --no-binary lxml lxml==4.9.3 && \
    # python -m pip install --no-binary xmlsec xmlsec==1.3.13 && \
    # python -m pip install python3-saml && \
    python -m pip install -r requirements.txt

# Creates a non-root user with an explicit UID and adds permission to access the /app folder
RUN adduser -u 5678 --disabled-password --gecos "" appuser && chown -R appuser /app
USER appuser

# Install Gunicorn and psutil for auto-configuration
RUN pip install gunicorn psutil

# Make port 5000 available to the world outside this container
EXPOSE 5000

# Run the application
# CMD ["gunicorn", "--bind", "0.0.0.0:5000", "app:app"]

# Run Gunicorn with the configuration file
CMD ["gunicorn", "-c", "gunicorn_config.py", "app:app"]
