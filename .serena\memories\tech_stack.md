# XAPA Backend Tech Stack

## Core Framework
- **Flask**: Main web framework
- **Flask-RestX**: API documentation and validation (Swagger UI)
- **Flask-SQLAlchemy**: Database ORM
- **Flask-Migrate**: Database migrations
- **Gunicorn**: WSGI HTTP Server

## Database & Caching
- **PostgreSQL**: Primary database
- **SQLAlchemy**: ORM with multi-tenant support
- **Redis**: Caching layer
- **Flask-Caching**: Cache management

## Authentication & Security
- **PyJWT**: JWT token handling
- **Authlib**: OAuth/SSO integration
- **Flask-HTTPAuth**: HTTP authentication
- **Flask-CORS**: Cross-origin resource sharing

## Azure Services Integration
- **azure-storage-blob**: File storage
- **azure-storage-queue**: Message queuing  
- **azure-keyvault-secrets**: Secret management
- **azure-identity**: Authentication
- **azure-communication-email**: Email service

## Monitoring & Performance
- **opencensus-ext-azure**: Application Insights integration
- **elastic-apm[flask]**: Application Performance Monitoring
- **Flask-Compress**: Response compression

## External Services
- **firebase-admin**: Firebase integration
- **aioapns**: Apple Push Notifications
- **google-api-python-client**: Google APIs
- **requests**: HTTP client library

## Development Tools
- **uuid7**: UUID generation
- **croniter**: Cron expression parsing
- **Pillow**: Image processing
- **bs4**: HTML parsing