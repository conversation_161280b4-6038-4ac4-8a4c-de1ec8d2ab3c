import datetime
from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from clientmodels import MASTER_TENANT, Category, ClientProgramCategoryAssociation, ClientXperienceCategoryAssociation, Level, ProgramCategoryAssociation, QuestCategoryAssociation, Tag, Facet, TargetCommunicationStyle, XperienceCategoryAssociation
from clientmodels import Character

cms_attributes_api = Namespace('api_cms_attributes', description='Attributes API')

category_model = cms_attributes_api.model('Category', {
    'name': fields.String(required=True, description='Category name'),
    'description': fields.String(required=True, description='Category description'),
})

tag_model = cms_attributes_api.model('Tag', {
    'name': fields.String(required=True, description='Tag name'),
    'description': fields.String(required=True, description='Tag description'),
})

facet_model = cms_attributes_api.model('Facet', {
    'name': fields.String(required=True, description='Facet name'),
    'description': fields.String(required=True, description='Facet description'),
})

target_communication_style_model = cms_attributes_api.model('TargetCommunicationStyle', {
    'name': fields.String(required=True, description='Target Communication style name'),
    'description': fields.String(required=True, description='Target Communication style description'),
})


## save, get, update, delete category
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/category/', methods=['POST'])
@cms_attributes_api.route('/category/<string:category_id>', methods=['GET', 'PUT', 'DELETE'])
class CategoryItem(Resource):
    @cms_attributes_api.expect(category_model)
    def post(self):
        '''Create a new category'''
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')

        ## check if the name already exists
        category = g.client_db_session.query(Category).filter(Category.name == name).first()
        if category:
            return create_response('Category already exists', 400)
        
        category = Category(name=name, description=description)

        ## check if category with the same name exists in global level
        if g.tenant_id != MASTER_TENANT:
            global_category = g.db_session.query(Category).filter(Category.name == name).first()
            if global_category:
                category.id = global_category.id
        
        g.client_db_session.add(category)
        g.client_db_session.commit()

        data = category.to_dict()
        return create_response("Category created successfully", data=data)
        
    def get(self, category_id):
        from clientmodels import Xperience, XperienceCategoryAssociation
        from clientmodels import Program, ProgramCategoryAssociation
        '''Get one category'''
        category = g.client_db_session.query(Category).filter(Category.id == category_id).first()
        if not category:
            return create_response('Category not found', 404)
        
        data = category.to_dict()

        ## get xperiences associate with this category
        ## if is client level, then get xperiences from client db session
        ## if is global level, then get xperiences from global db session

        if g.tenant_id != MASTER_TENANT:
            ## get xperiences from global db session
            xperiences = g.db_session.query(Xperience, XperienceCategoryAssociation.order).join(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.category_id == category_id).order_by(XperienceCategoryAssociation.order).all()
            xperience_data = []

            ## check if client has assign different xperience in their db
            client_xperiences = g.client_db_session.query(ClientXperienceCategoryAssociation).filter(ClientXperienceCategoryAssociation.category_id == category_id).all()
            if client_xperiences:
                client_xperience_ids = [xperience.xperience_id for xperience in client_xperiences]
                client_xperiences = g.db_session.query(Xperience).filter(Xperience.id.in_(client_xperience_ids)).all()

                for xperience in client_xperiences:
                    item = xperience.to_dict(["id", "name"])
                    # check if the xperience has order in client db session
                    association = g.client_db_session.query(ClientXperienceCategoryAssociation).filter(ClientXperienceCategoryAssociation.category_id == category_id, ClientXperienceCategoryAssociation.xperience_id == xperience.id).first()
                    item['order'] = association.order if association else 0
                    xperience_data.append(item)
                
                ## check if any xperience in global db session is not in client db session
                for xperience, order in xperiences:
                    if xperience.id not in client_xperience_ids:
                        ## check if this xperience has another category associated, if no then add it to this category
                        association = g.client_db_session.query(ClientXperienceCategoryAssociation).filter(ClientXperienceCategoryAssociation.xperience_id == xperience.id).first()
                        if not association:
                            item = xperience.to_dict(["id", "name"])
                            item['order'] = order if order else 0
                            xperience_data.append(item)

            else:
                for xperience, order in xperiences:
                    ## check if this xperience has another category associated, if no then add it to this category
                    association = g.client_db_session.query(ClientXperienceCategoryAssociation).filter(ClientXperienceCategoryAssociation.xperience_id == xperience.id).first()
                    if association:
                        continue
                    
                    item = xperience.to_dict(["id", "name"])
                    item['order'] = order if order else 0
                    xperience_data.append(item)

        else:
            ## get xperiences from client db session    
            xperiences = g.client_db_session.query(Xperience, XperienceCategoryAssociation.order).join(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.category_id == category_id).order_by(XperienceCategoryAssociation.order).all()

            xperience_data = []
            for xperience, order in xperiences:
                item = xperience.to_dict(["id", "name"])
                item['order'] = order if order else 0
                xperience_data.append(item)

        ## sort xperiences by order
        xperience_data = sorted(xperience_data, key=lambda x: x['order'])

        data['xperiences'] = xperience_data

        ## get program associate with this category
        if g.tenant_id != MASTER_TENANT:
            # Get programs from global db session
            programs = g.db_session.query(Program, ProgramCategoryAssociation.order) \
                .join(ProgramCategoryAssociation) \
                .filter(ProgramCategoryAssociation.category_id == category_id) \
                .order_by(ProgramCategoryAssociation.order).all()
            program_data = []

            # Check if client has assigned different programs in their db
            client_programs = g.client_db_session.query(ClientProgramCategoryAssociation) \
                .filter(ClientProgramCategoryAssociation.category_id == category_id).all()
            if client_programs:
                client_program_ids = [prog.program_id for prog in client_programs]
                client_programs_objs = g.db_session.query(Program).filter(Program.id.in_(client_program_ids)).all()

                for program in client_programs_objs:
                    item = program.to_dict(["id", "name"])
                    # check if the program has order in client db session
                    association = g.client_db_session.query(ClientProgramCategoryAssociation) \
                        .filter(ClientProgramCategoryAssociation.category_id == category_id,
                                ClientProgramCategoryAssociation.program_id == program.id).first()
                    item['order'] = association.order if association else 0
                    program_data.append(item)

                # Add global programs not in client db session
                for program, order in programs:
                    if program.id not in client_program_ids:
                        # Check if this program has another category associated, if no then add it to this category
                        association = g.client_db_session.query(ClientProgramCategoryAssociation) \
                            .filter(ClientProgramCategoryAssociation.program_id == program.id).first()
                        if not association:
                            item = program.to_dict(["id", "name"])
                            item['order'] = order if order else 0
                            program_data.append(item)
            else:
                for program, order in programs:
                    # Check if this program has another category associated, if no then add it to this category
                    association = g.client_db_session.query(ClientProgramCategoryAssociation) \
                        .filter(ClientProgramCategoryAssociation.program_id == program.id).first()
                    if association:
                        continue

                    item = program.to_dict(["id", "name"])
                    item['order'] = order if order else 0
                    program_data.append(item)
        else:
            # Get programs from client db session
            programs = g.client_db_session.query(Program, ProgramCategoryAssociation.order) \
                .join(ProgramCategoryAssociation) \
                .filter(ProgramCategoryAssociation.category_id == category_id) \
                .order_by(ProgramCategoryAssociation.order).all()
            program_data = []
            for program, order in programs:
                item = program.to_dict(["id", "name"])
                item['order'] = order if order else 0
                program_data.append(item)

        ## sort programs by order
        program_data = sorted(program_data, key=lambda x: x['order'])

        data['programs'] = program_data
        
        return create_response("Category retrieved successfully", data=data)


    @cms_attributes_api.expect(category_model)
    def put(self, category_id):
        '''Update a category'''
        data = request.json
        category = g.client_db_session.query(Category).filter(Category.id == category_id).first()
        if not category:
            return create_response('Category not found', 404)
        
        ## check if the name already exists
        name = data.get('name', '')
        description = data.get('description', '')

        if name and name != category.name:
            exist_category = g.client_db_session.query(Category).filter(Category.name == name).first()
            if exist_category:
                return create_response('Category already exists', 400)
        
        category.name = name
        category.description = description
        g.client_db_session.commit()

        data = category.to_dict()
        return create_response("Category updated successfully", data=data)
    

    def delete(self, category_id):
        '''Delete a category'''
        category = g.client_db_session.query(Category).filter(Category.id == category_id).first()
        if not category:
            return create_response('Category not found', 404)
        
        g.client_db_session.delete(category)
        g.client_db_session.commit()

        return create_response("Category deleted successfully")
    


category_sort_model = cms_attributes_api.model('CategorySort', {
    'id': fields.String(required=True, description='Category ID'),
    'name': fields.String(required=True, description='Category name'),
    'description': fields.String(required=True, description='Category description'),
})

## sort categories
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/category/sort', methods=['POST'])
class CategorySort(Resource):
    @cms_attributes_api.expect([category_sort_model])
    def post(self):
        '''Sort categories'''
        data = request.json
        for index, item in enumerate(data):
            category_id = item.get('id', '')
            category = g.client_db_session.query(Category).filter(Category.id == category_id).first()
            if category:
                category.order = index

        g.client_db_session.commit()

        return create_response("Categories sorted successfully")


xperience_cateogory_sort_model = cms_attributes_api.model('XperienceCategorySort', {
    'id': fields.String(required=True, description='Xperience ID'),
    'name': fields.String(required=True, description='Xperience name')
})

## sort xperience inside category
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/category/<string:category_id>/xperience/sort', methods=['POST'])
class CategoryXperienceSort(Resource):
    @cms_attributes_api.expect([xperience_cateogory_sort_model])
    def post(self, category_id):
        from clientmodels import XperienceCategoryAssociation
        '''Sort xperiences inside category'''
        data = request.json
        for index, item in enumerate(data):
            xperience_id = item.get('id', '')

            if g.tenant_id == MASTER_TENANT:
                association = g.client_db_session.query(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.category_id == category_id, XperienceCategoryAssociation.xperience_id == xperience_id).first()
                if association:
                    association.order = index
            else:
                association = g.client_db_session.query(ClientXperienceCategoryAssociation).filter(ClientXperienceCategoryAssociation.category_id == category_id, ClientXperienceCategoryAssociation.xperience_id == xperience_id).first()
                if association:
                    association.order = index

        g.client_db_session.commit()    

        ## update last modified date of category
        category = g.client_db_session.query(Category).filter(Category.id == category_id).first()
        if category:
            category.date_updated = datetime.datetime.utcnow()
            g.client_db_session.commit()

        return create_response("Xperiences sorted successfully")
    

program_cateogory_sort_model = cms_attributes_api.model('ProgramCategorySort', {
    'id': fields.String(required=True, description='Program ID'),
    'name': fields.String(required=True, description='Program name')
})

## sort program inside category
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/category/<string:category_id>/program/sort', methods=['POST'])
class CategoryProgramSort(Resource):
    @cms_attributes_api.expect([program_cateogory_sort_model])
    def post(self, category_id):
        from clientmodels import ProgramCategoryAssociation
        '''Sort programs inside category'''
        data = request.json
        for index, item in enumerate(data):
            program_id = item.get('id', '')

            if g.tenant_id == MASTER_TENANT:
                association = g.client_db_session.query(ProgramCategoryAssociation).filter(ProgramCategoryAssociation.category_id == category_id, ProgramCategoryAssociation.program_id == program_id).first()
                if association:
                    association.order = index
            else:
                association = g.client_db_session.query(ClientProgramCategoryAssociation).filter(ClientProgramCategoryAssociation.category_id == category_id, ClientProgramCategoryAssociation.program_id == program_id).first()
                if association:
                    association.order = index

        g.client_db_session.commit()    

        ## update last modified date of category
        category = g.client_db_session.query(Category).filter(Category.id == category_id).first()
        if category:
            category.date_updated = datetime.datetime.utcnow()
            g.client_db_session.commit()

        return create_response("Programs sorted successfully")
    




## get categories
categories_parser = cms_attributes_api.parser()
categories_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
categories_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
categories_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')

@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/category/list')
class CategoriesList(Resource):
    @cms_attributes_api.expect(categories_parser)
    def get(self):
        '''Get all categories'''
        args = categories_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')

        ## get global level cateogory list and copy it to the client db session
        if g.tenant_id != MASTER_TENANT:
            global_categories = g.db_session.query(Category).all()
            for category in global_categories:
                exist_category = g.client_db_session.query(Category).filter(Category.id == category.id).first()
                if not exist_category:
                    ## check if category with the same name exists
                    exist_category_by_name = g.client_db_session.query(Category).filter(Category.name == category.name).first()
                    if not exist_category_by_name:
                        new_category = Category(
                            id=category.id,
                            name=category.name,
                            description=category.description,
                            order=category.order,
                            date_created=category.date_created,
                            date_updated=category.date_updated
                        )
                        g.client_db_session.add(new_category)
                    else:
                        exist_category_id = exist_category_by_name.id
                        ## remove category program associations and xperience associations
                        g.client_db_session.query(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.id == exist_category_id).delete()
                        g.client_db_session.query(ProgramCategoryAssociation).filter(ProgramCategoryAssociation.id == exist_category_id).delete()
                        g.client_db_session.query(QuestCategoryAssociation).filter(QuestCategoryAssociation.id == exist_category_id).delete()

                        ## update the existing category with the id
                        exist_category_by_name.id = category.id

                        g.client_db_session.add(exist_category_by_name)

            ## commit the changes to the client db session  
            g.client_db_session.commit()

        query = g.client_db_session.query(Category)
        if search:
            query = query.filter(Category.name.ilike(f"%{search}%") | Category.description.ilike(f"%{search}%"))
        
        total = query.count()
        categories = query.order_by(Category.name).offset((page - 1) * limit).limit(limit).all()

        ## sort categories by order
        categories = sorted(categories, key=lambda x: x.order)
        
        data = []
        for category in categories:
            item = category.to_dict()
            data.append(item)
        
        return create_response("Categories List", data=data, total=total, page=page, limit=limit)
    


## save, get, update, delete tag
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/tag/', methods=['POST'])
@cms_attributes_api.route('/tag/<string:tag_id>', methods=['GET', 'PUT', 'DELETE'])
class TagItem(Resource):
    @cms_attributes_api.expect(tag_model)
    def post(self):
        '''Create a new tag'''
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')

        ## check if the name already exists
        tag = g.client_db_session.query(Tag).filter(Tag.name == name).first()
        if tag:
            return create_response('Tag already exists', 400)
        
        tag = Tag(name=name, description=description)
        g.client_db_session.add(tag)
        g.client_db_session.commit()

        data = tag.to_dict()
        return create_response("Tag created successfully", data=data)
        
    def get(self, tag_id):
        '''Get one tag'''
        tag = g.client_db_session.query(Tag).filter(Tag.id == tag_id).first()
        if not tag:
            return create_response('Tag not found', 404)
        
        data = tag.to_dict()
        
        return create_response("Tag retrieved successfully", data=data)

    @cms_attributes_api.expect(tag_model)
    def put(self, tag_id):
        '''Update a tag'''
        data = request.json
        tag = g.client_db_session.query(Tag).filter(Tag.id == tag_id).first()
        if not tag:
            return create_response('Tag not found', 404)

        name = data.get('name', '')            
        description = data.get('description', '')
        
        if name and name != tag.name:
            exist_tag = g.client_db_session.query(Tag).filter(Tag.name == name).first()
            if exist_tag:
                return create_response('Tag already exists', 400)
        
        tag.name = name
        tag.description = description
        g.client_db_session.commit()

        data = tag.to_dict()
        return create_response("Tag updated successfully", data=data)
    

    def delete(self, tag_id):
        '''Delete a tag'''
        tag = g.client_db_session.query(Tag).filter(Tag.id == tag_id).first()
        if not tag:
            return create_response('Tag not found', 404)
        
        g.client_db_session.delete(tag)
        g.client_db_session.commit()

        return create_response("Tag deleted successfully")
    

## get tags
tags_parser = cms_attributes_api.parser()
tags_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
tags_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
tags_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')

@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/tag/list')
class TagsList(Resource):
    @cms_attributes_api.expect(tags_parser)
    def get(self):
        '''Get all tags'''
        args = tags_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')

        query = g.client_db_session.query(Tag)
        if search:
            query = query.filter(Tag.name.ilike(f"%{search}%") | Tag.description.ilike(f"%{search}%"))
        
        total = query.count()
        tags = query.order_by(Tag.name).offset((page - 1) * limit).limit(limit).all()
        
        data = []
        for tag in tags:
            item = tag.to_dict()
            data.append(item)
        
        return create_response("Tags List", data=data, total=total, page=page, limit=limit)
    


## save, get, update, delete facet
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/facet/', methods=['POST'])
@cms_attributes_api.route('/facet/<string:facet_id>', methods=['GET', 'PUT', 'DELETE'])
class FacetItem(Resource):
    @cms_attributes_api.expect(facet_model)
    def post(self):
        '''Create a new facet'''
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')

        ## check if the name already exists
        facet = g.client_db_session.query(Facet).filter(Facet.name == name).first()
        if facet:
            return create_response('Facet already exists', 400)
        
        facet = Facet(name=name, description=description)
        g.client_db_session.add(facet)
        g.client_db_session.commit()

        data = facet.to_dict()
        return create_response("Facet created successfully", data=data)
        
    def get(self, facet_id):
        '''Get one facet'''
        facet = g.client_db_session.query(Facet).filter(Facet.id == facet_id).first()
        if not facet:
            return create_response('Facet not found', 404)
        
        data = facet.to_dict()
        
        return create_response("Facet retrieved successfully", data=data)


    @cms_attributes_api.expect(facet_model)
    def put(self, facet_id):
        '''Update a facet'''
        data = request.json
        facet = g.client_db_session.query(Facet).filter(Facet.id == facet_id).first()
        if not facet:
            return create_response('Facet not found', 404)
        
        ## check if the name already exists
        name = data.get('name', '')
        description = data.get('description', '')
        
        if name and name != facet.name:
            exist_facet = g.client_db_session.query(Facet).filter(Facet.name == name).first()
            if exist_facet:
                return create_response('Facet already exists', 400)
        
        facet.name = name
        facet.description = description
        g.client_db_session.commit()

        data = facet.to_dict()
        return create_response("Facet updated successfully", data=data)
    

    def delete(self, facet_id):
        '''Delete a facet'''
        facet = g.client_db_session.query(Facet).filter(Facet.id == facet_id).first()
        if not facet:
            return create_response('Facet not found', 404)
        
        g.client_db_session.delete(facet)
        g.client_db_session.commit()

        return create_response("Facet deleted successfully")


## get facets
facets_parser = cms_attributes_api.parser()
facets_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
facets_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
facets_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/facet/list')
class FacetsList(Resource):
    @cms_attributes_api.expect(facets_parser)
    def get(self):
        '''Get all facets'''
        args = facets_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')

        query = g.client_db_session.query(Facet)
        if search:
            query = query.filter(Facet.name.ilike(f"%{search}%") | Facet.description.ilike(f"%{search}%"))
        
        total = query.count()
        facets = query.order_by(Facet.name).offset((page - 1) * limit).limit(limit).all()
        
        data = []
        for facet in facets:
            item = facet.to_dict()
            data.append(item)
        
        return create_response("Facets List", data=data, total=total, page=page, limit=limit)
    


## save, get, update, delete target communication
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/target_communication_style/', methods=['POST'])
@cms_attributes_api.route('/target_communication_style/<string:target_communication_style_id>', methods=['GET', 'PUT', 'DELETE'])

class TargetCommunicationStyleItem(Resource):
    @cms_attributes_api.expect(target_communication_style_model)
    def post(self):
        '''Create a new target communication style'''
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')

        ## check if the name already exists
        target_communication_style = g.client_db_session.query(TargetCommunicationStyle).filter(TargetCommunicationStyle.name == name).first()
        if target_communication_style:
            return create_response('Target Communication already exists', 400)
        
        target_communication_style = TargetCommunicationStyle(name=name, description=description)
        g.client_db_session.add(target_communication_style)
        g.client_db_session.commit()

        data = target_communication_style.to_dict()
        return create_response("Target Communication created successfully", data=data)
        
    def get(self, target_communication_style_id):
        '''Get one target communication'''
        target_communication_style = g.client_db_session.query(TargetCommunicationStyle).filter(TargetCommunicationStyle.id == target_communication_style_id).first()
        if not target_communication_style:
            return create_response('Target Communication not found', 404)
        
        data = target_communication_style.to_dict()
        
        return create_response("Target Communication retrieved successfully", data=data)


    @cms_attributes_api.expect(target_communication_style_model)
    def put(self, target_communication_style_id):
        '''Update a target communication'''
        data = request.json
        target_communication_style = g.client_db_session.query(TargetCommunicationStyle).filter(TargetCommunicationStyle.id == target_communication_style_id).first()
        if not target_communication_style:
            return create_response('Target Communication not found', 404)
        
        ## check if the name already exists
        name = data.get('name', '')
        description = data.get('description', '')

        if name and name != target_communication_style.name:
            exist_target_communication_style = g.client_db_session.query(TargetCommunicationStyle).filter(TargetCommunicationStyle.name == name).first()
            if exist_target_communication_style:
                return create_response('Target Communication already exists', 400)
        
        target_communication_style.name = name
        target_communication_style.description = description
        g.client_db_session.commit()

        data = target_communication_style.to_dict()
        return create_response("Target Communication updated successfully", data=data)
    

    def delete(self, target_communication_style_id):
        '''Delete a target communication'''
        target_communication_style = g.client_db_session.query(TargetCommunicationStyle).filter(TargetCommunicationStyle.id == target_communication_style_id).first()
        if not target_communication_style:
            return create_response('Target Communication not found', 404)
        
        g.client_db_session.delete(target_communication_style)
        g.client_db_session.commit()

        return create_response("Target Communication deleted successfully")
    


## get target communications
target_communication_styles_parser = cms_attributes_api.parser()
target_communication_styles_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
target_communication_styles_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
target_communication_styles_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')

@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/target_communication_style/list')
class TargetCommunicationsList(Resource):
    @cms_attributes_api.expect(target_communication_styles_parser)
    def get(self):
        '''Get all target communications'''
        args = target_communication_styles_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')

        query = g.client_db_session.query(TargetCommunicationStyle)
        if search:
            query = query.filter(TargetCommunicationStyle.name.ilike(f"%{search}%") | TargetCommunicationStyle.description.ilike(f"%{search}%"))
        
        total = query.count()
        target_communication_styles = query.order_by(TargetCommunicationStyle.name).offset((page - 1) * limit).limit(limit).all()
        
        data = []
        for target_communication_style in target_communication_styles:
            item = target_communication_style.to_dict()
            data.append(item)
        
        return create_response("Target Communications List", data=data, total=total, page=page, limit=limit)
    


## save, get, update, delete level
level_model = cms_attributes_api.model('Level', {
    'name': fields.String(required=True, description='Level name'),
    'description': fields.String(required=True, description='Level description'),
    'level': fields.Integer(required=True, description='Level number', default=1),
    'xp_required': fields.Integer(required=True, description='Level XP Required'),
})

@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/level/', methods=['POST'])
@cms_attributes_api.route('/level/<string:level_id>', methods=['GET', 'PUT', 'DELETE'])
class LevelItem(Resource):
    @cms_attributes_api.expect(level_model)
    def post(self):
        '''Create a new level'''
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')
        level_number = data.get('level', 1)
        xp_required = data.get('xp_required', 0)

        ## check if the name already exists
        level = g.client_db_session.query(Level).filter(Level.level == level_number).first()
        if level:
            return create_response('Level already exists', 400)
        
        level = Level(name=name, description=description, level=level_number, xp_required=xp_required)
        g.client_db_session.add(level)
        g.client_db_session.commit()

        data = level.to_dict()
        return create_response("Level created successfully", data=data)
        
    def get(self, level_id):
        '''Get one level'''
        level = g.client_db_session.query(Level).filter(Level.id == level_id).first()
        if not level:
            return create_response('Level not found', 404)
        
        data = level.to_dict()
        
        return create_response("Level retrieved successfully", data=data)


    @cms_attributes_api.expect(level_model)
    def put(self, level_id):
        '''Update a level'''
        data = request.json
        level = g.client_db_session.query(Level).filter(Level.id == level_id).first()
        if not level:
            return create_response('Level not found', 404)
        
        ## check if the name already exists
        name = data.get('name', '')
        description = data.get('description', '')
        level_number = data.get('level', 1)
        
        if name and name != level.name:
            level = g.client_db_session.query(Level).filter(Level.level == level_number).first()
            if level:
                return create_response('Level already exists', 400)
        
        level.name = name
        level.description = description
        level.level = level_number
        level.xp_required = data.get('xp_required', 0)
        g.client_db_session.commit()

        data = level.to_dict()
        return create_response("Level updated successfully", data=data)
    

    def delete(self, level_id):
        '''Delete a level'''
        level = g.client_db_session.query(Level).filter(Level.id == level_id).first()
        if not level:
            return create_response('Level not found', 404)
        
        g.client_db_session.delete(level)
        g.client_db_session.commit()

        return create_response("Level deleted successfully")
    

## get levels
levels_parser = cms_attributes_api.parser()
levels_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
levels_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
levels_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')

@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/level/list')
class LevelsList(Resource):
    @cms_attributes_api.expect(levels_parser)
    def get(self):
        '''Get all levels'''
        args = levels_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')

        query = g.client_db_session.query(Level)
        if search:
            query = query.filter(Level.name.ilike(f"%{search}%") | Level.description.ilike(f"%{search}%"))
        
        total = query.count()
        levels = query.order_by(Level.level).offset((page - 1) * limit).limit(limit).all()
        
        data = []
        for level in levels:
            item = level.to_dict()
            data.append(item)
        
        return create_response("Levels List", data=data, total=total, page=page, limit=limit)



## get characters
@cms_attributes_api.doc(security='bearer')
@cms_attributes_api.route('/character/list')
class CharactersList(Resource):
    def get(self):
        '''Get all characters'''
        query = g.db_session.query(Character)
        characters = query.all()
        data = []
        for character in characters:
            animation_keys = json.loads(character.animations) if character.animations else []
            item = {
                "character_id": character.key,
                "name": character.name,
                "animation_keys": json.loads(character.animations) if character.animations else [],
                "default_animation_key": "Idle"
            }
            data.append(item)
    
        return create_response("Characters List", data=data)
