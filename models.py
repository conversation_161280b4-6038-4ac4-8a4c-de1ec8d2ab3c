import datetime
import json

from flask import g
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Text, ForeignKey, Date, DateTime, JSON
from sqlalchemy import event
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import declarative_base, relationship
from uuid_extensions import uuid7str

from api.common.helper import format_datetime_with_timezone
from app import db

# declarative base class
Base = declarative_base()

class BaseModel(db.Model):
    __abstract__ = True
    id = Column(String(36), primary_key=True, default=lambda: uuid7str())
    date_created = Column(DateTime, default=datetime.datetime.utcnow)
    date_updated = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    create_user = Column(String(36))
    update_user = Column(String(36))
    is_deleted = Column(Boolean, default=False)
    status = Column(String(255), default='active')

    def delete(self):
        self.is_deleted = True

@event.listens_for(BaseModel, 'before_insert', propagate=True)
def before_insert(mapper, connection, target):
    if hasattr(g, 'user_id'):
        if not target.create_user:
            target.create_user = g.user_id
            
        state = db.inspect(target)
        if 'update_user' not in state.unmodified:
            target.update_user = g.user_id

@event.listens_for(BaseModel, 'before_update', propagate=True)
def before_update(mapper, connection, target):
    if hasattr(g, 'user_id'):
        state = db.inspect(target)
        if 'update_user' not in state.unmodified:
            target.update_user = g.user_id



class ClientBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'client'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def id_key(cls):
        return Column(String(255), unique=True, nullable=True)

    @declared_attr
    def email_suffix(cls):
        return Column(Text)
    
    @declared_attr
    def name(cls):
        return Column(String(255))

    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def packages(cls):
        return relationship('Package', secondary='client_package_association', back_populates='clients')

    @declared_attr
    def contact_first_name(cls):
        return Column(String(255))
    
    @declared_attr
    def contact_last_name(cls):
        return Column(String(255))

    @declared_attr
    def contact_email(cls):
        return Column(String(255))

    @declared_attr
    def contact_phone(cls):
        return Column(String(255))
    
    @declared_attr
    def contact_title(cls):
        return Column(String(255))

    @declared_attr
    def address1(cls):
        return Column(String(255))
    
    @declared_attr
    def address2(cls):
        return Column(String(255))

    @declared_attr
    def city(cls):
        return Column(String(255))

    @declared_attr
    def state(cls):
        return Column(String(255))
    
    @declared_attr
    def zip(cls):
        return Column(String(255))

    @declared_attr
    def country(cls):
        return Column(String(255))

    @declared_attr
    def timezone(cls):
        return Column(String(255))
    
    @declared_attr
    def language(cls):
        return Column(String(255))
    
    @declared_attr
    def annual_license_fee(cls):
        return Column(String(255))
    
    @declared_attr
    def contract_term(cls):
        return Column(String(255))
    
    @declared_attr
    def client_type(cls):
        return Column(String(255))
    
    @declared_attr
    def support_plan(cls):
        return Column(String(255))

    @declared_attr
    def contract_start_date(cls):
        return Column(Date)
    
    @declared_attr
    def contract_end_date(cls):
        return Column(Date)

    @declared_attr
    def next_renewal_date(cls):
        return Column(Date)
    
    @declared_attr
    def allocated_users(cls):
        return Column(Integer, default=0)

    @declared_attr
    def allocated_packages(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def allocated_programs(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def allocated_xperiences(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def allocated_quests(cls):
        return Column(Integer, default=0)

    @declared_attr
    def settings(cls):
        return Column(JSON)
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'email_suffix': self.email_suffix if self.email_suffix else "",
            'image': self.image if self.image else "",
            'contact_first_name': self.contact_first_name if self.contact_first_name else "",
            'contact_last_name': self.contact_last_name if self.contact_last_name else "",
            'contact_email': self.contact_email if self.contact_email else "",
            'contact_phone': self.contact_phone if self.contact_phone else "",
            'contact_title': self.contact_title if self.contact_title else "",
            'address1': self.address1 if self.address1 else "",
            'address2': self.address2 if self.address2 else "",
            'city': self.city if self.city else "",
            'state': self.state if self.state else "",
            'zip': self.zip if self.zip else "",
            'country': self.country if self.country else "",
            'timezone': self.timezone if self.timezone else "",
            'language': self.language if self.language else "",
            'annual_license_fee': self.annual_license_fee if self.annual_license_fee else "",
            'contract_term': self.contract_term if self.contract_term else "",
            'client_type': self.client_type if self.client_type else "",
            'support_plan': self.support_plan if self.support_plan else "",
            'contract_start_date': self.contract_start_date.strftime('%Y-%m-%d') if self.contract_start_date else "",
            'contract_end_date': self.contract_end_date.strftime('%Y-%m-%d') if self.contract_end_date else "",
            'next_renewal_date': self.next_renewal_date.strftime('%Y-%m-%d') if self.next_renewal_date else "",
            'allocated_users': self.allocated_users if self.allocated_users else 0,
            'allocated_packages': self.allocated_packages if self.allocated_packages else 0,
            'allocated_programs': self.allocated_programs if self.allocated_programs else 0,
            'allocated_xperiences': self.allocated_xperiences if self.allocated_xperiences else 0,
            'allocated_quests': self.allocated_quests if self.allocated_quests else 0,
            'settings': self.settings if self.settings else {},
            'is_active': self.is_active,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class SSOConfigurationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'sso_configuration'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255))

    @declared_attr
    def label(cls):
        return Column(String(255))

    @declared_attr
    def description(cls):
        return Column(Text)
        
    @declared_attr
    def id_key(cls):
        return Column(String(255))
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def sso_type(cls):
        return Column(String(255))
    
    @declared_attr
    def email_suffix(cls):
        return Column(Text)
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'label': self.label if self.label else "",
            'description': self.description if self.description else "",
            'id_key': self.id_key if self.id_key else "",
            'sso_type': self.sso_type if self.sso_type else "",
            'email_suffix': self.email_suffix if self.email_suffix else "",
            'is_active': self.is_active,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class ClientSSOAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'client_sso_association'
    
    @declared_attr
    def client_id(cls):
        return Column(String(36), ForeignKey('client.id'), primary_key=True)
    
    @declared_attr
    def sso_configuration_id(cls):
        return Column(String(36), ForeignKey('sso_configuration.id'), primary_key=True)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self


class MasterUserBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'master_user'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def username(cls):
        return Column(String(255), unique=True, nullable=False)
    
    @declared_attr
    def email(cls):
        return Column(String(255))
    
    @declared_attr
    def phone_number(cls):
        return Column(String(255))
    
    @declared_attr
    def first_name(cls):
        return Column(String(255))
    
    @declared_attr
    def last_name(cls):
        return Column(String(255))
    
    
    def __str__(self):
        return self


class UserBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def email(cls):
        return Column(String(255), unique=True, nullable=False)

    @declared_attr
    def recovery_email(cls):
        return Column(String(255))
    
    @declared_attr
    def phone_number(cls):
        return Column(String(255))
    
    @declared_attr
    def first_name(cls):
        return Column(String(255))
    
    @declared_attr
    def last_name(cls):
        return Column(String(255))

    @declared_attr
    def preferred_name(cls):
        return Column(String(255))

    @declared_attr
    def pronouns(cls):
        return Column(String(255))

    @declared_attr
    def title(cls):
        return Column(String(255))
    
    @declared_attr
    def company(cls):
        return Column(String(255))
    
    @declared_attr
    def department(cls):
        return Column(String(255))
    
    @declared_attr
    def bio(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def date_of_birth(cls):
        return Column(Date)
    
    @declared_attr
    def location(cls):
        return Column(String(255))

    @declared_attr
    def city(cls):
        return Column(String(255))

    @declared_attr
    def state(cls):
        return Column(String(255))

    @declared_attr
    def country(cls):
        return Column(String(255))
    
    @declared_attr
    def timezone(cls):
        return Column(String(255))
    
    @declared_attr
    def language(cls):
        return Column(String(255))
    
    @declared_attr
    def schools(cls):
        return Column(JSON)

    @declared_attr
    def children(cls):
        return Column(JSON)
    
    @declared_attr
    def pets(cls):
        return Column(JSON)
    
    @declared_attr
    def diet(cls):
        return Column(JSON)
    
    @declared_attr
    def activities(cls):
        return Column(JSON)

    @declared_attr
    def interests(cls):
        return Column(JSON)
    
    @declared_attr
    def facts(cls):
        return Column(JSON)
    
    @declared_attr
    def settings(cls):
        return Column(JSONB)

    @declared_attr
    def comment(cls):
        return Column(Text)
    
    @declared_attr
    def status(cls):
        return Column(String(255), default='active')
    
    @declared_attr
    def sso_redirect_token(cls):
        return Column(String(255))

    @declared_attr
    def sso_type(cls):
        return Column(String(255))
    
    @declared_attr
    def last_login(cls):
        return Column(DateTime)
    
    @declared_attr
    def last_invitation_sent(cls):
        return Column(DateTime)
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def is_deleted(cls):
        return Column(Boolean, default=False)
    
    @declared_attr
    def xircles(cls):
        return relationship('Xircle', back_populates='creator')

    @declared_attr
    def stats(cls):
        return relationship('UserStats', backref='user', uselist=False)

    @declared_attr
    def client_id(cls):
        return Column(String(36))

    @declared_attr
    def manager_id(cls):
        return Column(String(36))

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'email': self.email,
            'recovery_email': self.recovery_email if self.recovery_email else "",
            'phone_number': self.phone_number if self.phone_number else "",
            'first_name': self.first_name if self.first_name else "",
            'last_name': self.last_name if self.last_name else "",
            'preferred_name': self.preferred_name if self.preferred_name else "",
            'pronouns': self.pronouns if self.pronouns else "",
            'title': self.title if self.title else "",
            'company': self.company if self.company else "",
            'department': self.department if self.department else "",
            'bio': self.bio if self.bio else "",
            'image': self.image if self.image else "",
            'date_of_birth': self.date_of_birth.strftime('%Y-%m-%d') if self.date_of_birth else "",
            'location': self.location if self.location else "",
            'city': self.city if self.city else "",
            'state': self.state if self.state else "",
            'country': self.country if self.country else "",
            'timezone': self.timezone if self.timezone else "",
            'language': self.language if self.language else "",
            'schools': self.schools if self.schools else [],
            'children': self.children if self.children else [],
            'pets': self.pets if self.pets else [],
            'diet': self.diet if self.diet else [],
            'activities': self.activities if self.activities else [],
            'interests': self.interests if self.interests else [],
            'facts': self.facts if self.facts else [],
            'settings': self.settings if self.settings else {},
            'comment': self.comment if self.comment else "",
            'status': self.status if self.status else "",
            'last_login': format_datetime_with_timezone(self.last_login) if self.last_login else "",
            'last_invitation_sent': format_datetime_with_timezone(self.last_invitation_sent) if self.last_invitation_sent else "",
            'sso_type': self.sso_type if self.sso_type else "",
            'is_active': self.is_active,
            'is_deleted': self.is_deleted,
            'manager_id': self.manager_id if self.manager_id else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }
    
        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class UserAssignmentBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_assignment'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def client_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def master_user_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def is_deleted(cls):
        return Column(Boolean, default=False)
    
    @declared_attr
    def is_verified(cls):
        return Column(Boolean, default=False)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self


class UserTokenBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_token'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def access_token(cls):
        return Column(String(2000), nullable=False)
    
    @declared_attr
    def refresh_token(cls):
        return Column(String(2000), nullable=False)
    
    @declared_attr
    def login_type(cls):
        return Column(String(500))
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_expired(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def device_id(cls):
        return Column(String(500), nullable=False)

    @declared_attr
    def device_token(cls):
        return Column(String(255))

    @declared_attr
    def device_type(cls):
        return Column(String(500))
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    def __str__(self):
        return self


class UserLoginLogBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_login_log'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'))
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def device_id(cls):
        return Column(String(500))
    
    @declared_attr
    def login_type(cls):
        return Column(String(500))
    
    @declared_attr
    def ip_address(cls):
        return Column(String(500))
    
    @declared_attr
    def user_agent(cls):
        return Column(String(255))
    
    @declared_attr
    def is_success(cls):
        return Column(Boolean, default=False)
    
    @declared_attr
    def message(cls):
        return Column(Text)
    
    def __str__(self):
        return self


class UserGraphTokenBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_graph_token'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def access_token(cls):
        return Column(Text, nullable=False)
    
    @declared_attr
    def refresh_token(cls):
        return Column(Text, nullable=False)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_expired(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def last_login(cls):
        return Column(DateTime)
    
    def __str__(self):
        return self
    
    
class AdminBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'admin'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36))
    
    @declared_attr
    def role(cls):
        return Column(String(255))

    @declared_attr
    def permissions(cls):
        return Column(JSON)
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def last_login(cls):
        return Column(DateTime)

    def __str__(self):
        return self


class XircleBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xircle'
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def is_public(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def include_leaderboard(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def creator_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def creator(cls):
        return relationship('User', back_populates='xircles', uselist=False)

    @declared_attr
    def members(cls):
        return relationship('User', secondary='xircle_member', back_populates='xircles')
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'is_public': self.is_public,
            'include_leaderboard': self.include_leaderboard,
            'creator_id': self.creator_id,
            'creator': self.creator.to_dict(['id', 'email', 'first_name', 'last_name', 'image']),
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }
        
        if keys:
            data = {key: data[key] for key in keys}
        
        return data
    

class XircleMemberBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xircle_member'
    
    @declared_attr
    def xircle_id(cls):
        return Column(String(36), ForeignKey('xircle.id'), nullable=False, primary_key=True)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False, primary_key=True)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self


class FeedBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'feed'
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def xircle_id(cls):
        return Column(String(36), ForeignKey('xircle.id'), nullable=True)
    
    @declared_attr
    def message(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))

    @declared_attr
    def media(cls):
        return Column(String(255))

    @declared_attr
    def link(cls):
        return Column(String(255))

    @declared_attr
    def link_type(cls):
        return Column(String(255))

    @declared_attr
    def label(cls):
        return Column(String(255))

    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'xircle_id': self.xircle_id,
            'message': self.message,
            'image': self.image,
            'media': self.media,
            'link': self.link,
            'link_type': self.link_type,
            'label': self.label,
            'date_created': format_datetime_with_timezone(self.date_created),
            'date_updated': format_datetime_with_timezone(self.date_updated),
            'status': self.status
        }
    
        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class FeedCommentBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'feed_comment'
    
    @declared_attr
    def feed_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def message(cls):
        return Column(Text)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'feed_id': self.feed_id,
            'user_id': self.user_id,
            'message': self.message if self.message else "",
            'date_created': format_datetime_with_timezone(self.date_created),
            'date_updated': format_datetime_with_timezone(self.date_updated)
        }
    
        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class FeedLikeBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'feed_like'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def feed_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'feed_id': self.feed_id,
            'user_id': self.user_id,
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class FeedFlagBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'feed_flag'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def feed_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def message(cls):
        return Column(Text)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def user(cls):
        return relationship('User', backref='flag_user', uselist=False)

    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'feed_id': self.feed_id,
            'user_id': self.user_id,
            'message': self.message if self.message else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class FeedSeniorityBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'feed_seniority'
    
    @declared_attr
    def user_id(cls):
        return Column(String(36))
    
    @declared_attr
    def xircle_id(cls):
        return Column(String(36))
    
    @declared_attr
    def days(cls):
        return Column(Integer, nullable=False)
    
    @declared_attr
    def message(cls):
        return Column(String, nullable=False)
    
    @declared_attr
    def image(cls):
        return Column(String)
    
    @declared_attr
    def media(cls):
        return Column(String)

    @declared_attr
    def link(cls):
        return Column(String(255))

    @declared_attr
    def link_type(cls):
        return Column(String(255))

    @declared_attr
    def label(cls):
        return Column(String(255))

    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'xircle_id': self.xircle_id,
            'user_id': self.user_id,
            'message': self.message,
            'days': self.days,
            'image': self.image,
            'media': self.media,
            'link': self.link,
            'link_type': self.link_type,
            'label': self.label,
            'date_created': format_datetime_with_timezone(self.date_created),
            'date_updated': format_datetime_with_timezone(self.date_updated)
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class UserStatsBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_stats'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def xp_count(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def coins_count(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def gems_count(cls):
        return Column(Integer, default=0)

    @declared_attr
    def keys_count(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def streaks_count(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'xp_count': self.xp_count,
            'coins_count': self.coins_count,
            'gems_count': self.gems_count,
            'keys_count': self.keys_count,
            'streaks_count': self.streaks_count,
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserStreakBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_streak'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def source(cls):
        return Column(String(255))
    
    @declared_attr
    def date(cls):
        return Column(Date)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'source': self.source if self.source else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserCheckinBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_checkin'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def checkin_date(cls):
        return Column(Date, nullable=False)
    
    @declared_attr
    def location(cls):
        return Column(String(255))
    
    @declared_attr
    def notes(cls):
        return Column(Text)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'checkin_date': self.checkin_date.strftime('%Y-%m-%d') if self.checkin_date else "",
            'location': self.location if self.location else "",
            'notes': self.notes if self.notes else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class UserXAPADayBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_xapa_day'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def checkin_date(cls):
        return Column(Date, nullable=False)
    
    @declared_attr
    def location(cls):
        return Column(String(255))
    
    @declared_attr
    def notes(cls):
        return Column(Text)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'checkin_date': self.checkin_date.strftime('%Y-%m-%d') if self.checkin_date else "",
            'location': self.location if self.location else "",
            'notes': self.notes if self.notes else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class UserXPBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_xp'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def value(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def source(cls):
        return Column(String(255))
    
    @declared_attr
    def source_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date(cls):
        return Column(Date)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'value': self.value,
            'source': self.source if self.source else "",
            'source_id': self.source_id if self.source_id else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserXPFacetBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_xp_facet'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def facet_id(cls):
        return Column(String(36))

    @declared_attr
    def user_xp_id(cls):
        return Column(String(36))
    
    @declared_attr
    def value(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'facet_id': self.facet_id,
            'user_xp_id': self.user_xp_id,
            'value': self.value,
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserCoinsBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_coins'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def value(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def source(cls):
        return Column(String(255))
    
    @declared_attr
    def source_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date(cls):
        return Column(Date)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'value': self.value,
            'source': self.source if self.source else "",
            'source_id': self.source_id if self.source_id else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserGemsBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_gems'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def value(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def source(cls):
        return Column(String(255))
    
    @declared_attr
    def source_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date(cls):
        return Column(Date)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'value': self.value,
            'source': self.source if self.source else "",
            'source_id': self.source_id if self.source_id else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserKeysBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_keys'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def value(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def source(cls):
        return Column(String(255))
    
    @declared_attr
    def source_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date(cls):
        return Column(Date)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'value': self.value,
            'source': self.source if self.source else "",
            'source_id': self.source_id if self.source_id else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserSpendingBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_spending'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def xp(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def coins(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def gems(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def keys(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def source(cls):
        return Column(String(255))

    @declared_attr
    def source_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'xp_spent': self.xp_spent,
            'coins_spent': self.coins_spent,
            'gems_spent': self.gems_spent,
            'keys_spent': self.keys_spent,
            'source': self.source if self.source else "",
            'source_id': self.source_id if self.source_id else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserAssetBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_asset'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def asset_id(cls):
        return Column(String(36))

    @declared_attr
    def is_starred(cls):
        return Column(Boolean, default=False)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'asset_id': self.asset_id,
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class UserBadgeBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_badge'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def badge_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'badge_id': self.badge_id,
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class DailyAverageXPBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'daily_average_xp'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def date(cls):
        return Column(Date)
    
    @declared_attr
    def value(cls):
        return Column(Integer, default=0)

    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'value': self.value,
            'date': self.date.strftime('%Y-%m-%d') if self.date else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }
    

class UserActivityBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_activity'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def activity(cls):
        return Column(String(255))
    
    @declared_attr
    def duration(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def date(cls):
        return Column(Date)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'activity': self.activity if self.activity else "",
            'duration': self.duration if self.duration else 0,
            'date': self.date.strftime('%Y-%m-%d') if self.date else "",
            'date_created': format_datetime_with_timezone(self.date_created)
        }
    

class UserProgramBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_program'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def program_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date_completed(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def status(cls):
        return Column(String(50), default='incomplete')

    @declared_attr
    def process_percentage(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def reward(cls):
        return Column(JSON)
    
    @declared_attr
    def date_started(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def is_unlocked(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def time_spent(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'program_id': self.program_id,
            'date_completed': format_datetime_with_timezone(self.date_completed) if self.date_completed else "",
            'status': self.status,
            'reward': json.loads(self.reward) if self.reward else {},
            'process_percentage': self.process_percentage if self.process_percentage else 0,
            'date_started': format_datetime_with_timezone(self.date_started) if self.date_started else "",
            'is_unlocked': self.is_unlocked if self.is_unlocked else False,
            'time_spent': self.time_spent if self.time_spent else 0
        }
    

class UserXperienceBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_xperience'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date_completed(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def status(cls):
        return Column(String(50), default='incomplete')

    @declared_attr
    def process_percentage(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def reward(cls):
        return Column(JSON)

    @declared_attr
    def date_started(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def is_unlocked(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def time_spent(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'xperience_id': self.xperience_id,
            'date_completed': format_datetime_with_timezone(self.date_completed) if self.date_completed else "",
            'status': self.status,
            'reward': json.loads(self.reward) if self.reward else {},
            'process_percentage': self.process_percentage if self.process_percentage else 0,
            'date_started': format_datetime_with_timezone(self.date_started) if self.date_started else "",
            'is_unlocked': self.is_unlocked if self.is_unlocked else False,
            'time_spent': self.time_spent if self.time_spent else 0
        }


class UserQuestBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_quest'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36))

    @declared_attr
    def program_id(cls):
        return Column(String(36))

    @declared_attr
    def xperience_id(cls):
        return Column(String(36))

    @declared_attr
    def date_completed(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def last_node_id(cls):
        return Column(String(36))

    @declared_attr
    def status(cls):
        return Column(String(50), default='incomplete')
    
    @declared_attr
    def reward(cls):
        return Column(JSON)

    @declared_attr
    def process_percentage(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def date_started(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def time_spent(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'quest_id': self.quest_id,
            'program_id': self.program_id if self.program_id else "",
            'xperience_id': self.xperience_id if self.xperience_id else "",
            'date_completed': format_datetime_with_timezone(self.date_completed) if self.date_completed else "",
            'last_node_id': self.last_node_id if self.last_node_id else "",
            'status': self.status,
            'reward': json.loads(self.reward) if self.reward else {},
            'process_percentage': self.process_percentage if self.process_percentage else 0,
            'date_started': format_datetime_with_timezone(self.date_started) if self.date_started else ""
        }
    

class UserNodeBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_node'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def quest_id(cls):
        return Column(String(36))
    
    @declared_attr
    def node_id(cls):
        return Column(String(36))

    @declared_attr
    def node_type(cls):
        return Column(String(50))

    @declared_attr
    def value(cls):
        return Column(Text)
    
    @declared_attr
    def points(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def duration(cls):
        return Column(Integer, default=0)

    @declared_attr
    def raw_data(cls):
        return Column(JSON)

    @declared_attr
    def previous_node_id(cls):
        return Column(String(36))

    @declared_attr
    def next_node_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date_completed(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'node_id': self.node_id,
            'node_type': self.node_type,
            'value': self.value if self.value else "",
            'points': self.points,
            'duration': self.duration,
            'raw_data': self.raw_data,
            'previous_node_id': self.previous_node_id if self.previous_node_id else "",
            'next_node_id': self.next_node_id if self.next_node_id else "",
            'date_completed': format_datetime_with_timezone(self.date_completed) if self.date_completed else ""
        }


class UserNodeHistoryBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_node_history'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def quest_id(cls):
        return Column(String(36))
    
    @declared_attr
    def node_id(cls):
        return Column(String(36))

    @declared_attr
    def node_type(cls):
        return Column(String(50))

    @declared_attr
    def value(cls):
        return Column(Text)
    
    @declared_attr
    def points(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def duration(cls):
        return Column(Integer, default=0)

    @declared_attr
    def raw_data(cls):
        return Column(JSON)

    @declared_attr
    def previous_node_id(cls):
        return Column(String(36))
    
    @declared_attr
    def next_node_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date_completed(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def is_retry(cls):
        return Column(Boolean, default=False)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'node_id': self.node_id,
            'node_type': self.node_type,
            'value': self.value if self.value else "",
            'points': self.points,
            'duration': self.duration,
            'raw_data': self.raw_data,
            'previous_node_id': self.previous_node_id if self.previous_node_id else "",
            'next_node_id': self.next_node_id if self.next_node_id else "",
            'date_completed': format_datetime_with_timezone(self.date_completed) if self.date_completed else "",
            'is_coris_retryrect': self.is_retry
        }


class UserChestBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_chest'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def chest_id(cls):
        return Column(String(36))

    @declared_attr
    def source(cls):
        return Column(String(255))

    @declared_attr
    def source_id(cls):
        return Column(String(36))

    @declared_attr
    def chest_type(cls):
        return Column(String(255))
    
    @declared_attr
    def program_id(cls):
        return Column(String(36))

    @declared_attr
    def xperience_id(cls):
        return Column(String(36))

    @declared_attr
    def quest_id(cls):
        return Column(String(36))

    @declared_attr
    def node_id(cls):
        return Column(String(36))
    
    @declared_attr
    def date_received(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_openable(cls):
        return Column(DateTime)
    
    @declared_attr
    def is_opened(cls):
        return Column(Boolean, default=False)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'chest_id': self.chest_id,
            'date_received': format_datetime_with_timezone(self.date_received) if self.date_received else "",
            'date_openable': format_datetime_with_timezone(self.date_openable) if self.date_openable else "",
            'is_opened': self.is_opened,
            'program_id': self.program_id if self.program_id else "",
            'xperience_id': self.xperience_id if self.xperience_id else "",
            'quest_id': self.quest_id if self.quest_id else "",
            'node_id': self.node_id if self.node_id else "",
            'source': self.source if self.source else "",
            'source_id': self.source_id if self.source_id else "",
            'chest_type': self.chest_type if self.chest_type else ""
        }


class ProgramBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))

    @declared_attr
    def image_webapp(cls):
        return Column(String(255))

    @declared_attr
    def level(cls):
        return Column(Integer, default=1)

    @declared_attr
    def categories(cls):
        return relationship('Category', secondary='program_category_association', back_populates='programs')

    @declared_attr
    def tags(cls):
        return relationship('Tag', secondary='program_tag_association', back_populates='programs')

    @declared_attr
    def facets(cls):
        return relationship('Facet', secondary='program_facet_association', back_populates='programs')

    @declared_attr
    def badges(cls):
        return relationship('Badge', secondary='program_badge_association', back_populates='programs')

    @declared_attr
    def quests(cls):
        return relationship('Quest', secondary='program_quest_association', back_populates='programs')

    @declared_attr
    def packages(cls):
        return relationship('Package', secondary='package_program_association', back_populates='programs')

    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='program_xperience_association', back_populates='programs')

    @declared_attr
    def chest_id(cls):
        return Column(String(36), ForeignKey('chest.id'), nullable=True)

    @declared_attr
    def requires_quest_completion(cls):
        return Column(Boolean, default=True)

    @declared_attr
    def keys_required(cls):
        return Column(Integer, default=0)

    @declared_attr
    def learning_objective(cls):
        return Column(Text)

    @declared_attr
    def status(cls):
        return Column(String(50), default='Draft')
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'image_webapp': self.image_webapp if self.image_webapp else "",
            'level': self.level,
            'learning_objective': self.learning_objective if self.learning_objective else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'status': self.status if self.status else "",
            'chest_id': self.chest_id if self.chest_id else "",
            'requires_quest_completion': self.requires_quest_completion if self.requires_quest_completion is not None else True,
            'keys_required': self.keys_required if self.keys_required else 0
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class ProgramDividerBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program_divider'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'program_id': self.program_id,
            'name': self.name if self.name else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'order': self.order if self.order else 0
        }


class ProgramQuestAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program_quest_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    @declared_attr
    def program_divider_id(cls):
        return Column(String(36), ForeignKey('program_divider.id'), nullable=True)
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    
    
class XperienceBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xperience'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def image_webapp(cls):
        return Column(String(255))
    
    @declared_attr
    def level(cls):
        return Column(Integer, default=1)

    @declared_attr
    def categories(cls):
        return relationship('Category', secondary='xperience_category_association', back_populates='xperiences')
    
    @declared_attr
    def tags(cls):
        return relationship('Tag', secondary='xperience_tag_association', back_populates='xperiences')
    
    @declared_attr
    def facets(cls):
        return relationship('Facet', secondary='xperience_facet_association', back_populates='xperiences')

    @declared_attr
    def badges(cls):
        return relationship('Badge', secondary='xperience_badge_association', back_populates='xperiences')

    @declared_attr
    def quests(cls):
        return relationship('Quest', secondary='xperience_quest_association', back_populates='xperiences')

    @declared_attr
    def packages(cls):
        return relationship('Package', secondary='package_xperience_association', back_populates='xperiences')

    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='program_xperience_association', back_populates='xperiences')
    
    @declared_attr
    def chest_id(cls):
        return Column(String(36), ForeignKey('chest.id'), nullable=True)

    @declared_attr
    def requires_quest_completion(cls):
        return Column(Boolean, default=True)

    @declared_attr
    def keys_required(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def level_required(cls):
        return Column(Integer, default=1)
    
    @declared_attr
    def learning_objective(cls):
        return Column(Text)

    @declared_attr
    def status(cls):
        return Column(String(50), default='Draft')
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'image_webapp': self.image_webapp if self.image_webapp else "",
            'level': self.level,
            'learning_objective': self.learning_objective if self.learning_objective else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'status': self.status if self.status else "",
            'requires_quest_completion': self.requires_quest_completion if self.requires_quest_completion is not None else True,
            'keys_required': self.keys_required if self.keys_required else 0,
            'level_required': self.level_required if self.level_required else 1,
            'chest_id': self.chest_id if self.chest_id else ""
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class XperienceQuestAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xperience_quest_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    

class ProgramXperienceAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program_xperience_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    

class CategoryBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'category'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='program_category_association', back_populates='categories')
    
    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='xperience_category_association', back_populates='categories')

    @declared_attr
    def quests(cls):
        return relationship('Quest', secondary='quest_category_association', back_populates='categories')
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'order': self.order if self.order else 0
        }


class TagBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'tag'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    

    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='program_tag_association', back_populates='tags')
    
    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='xperience_tag_association', back_populates='tags')

    @declared_attr
    def quests(cls):
        return relationship('Quest', secondary='quest_tag_association', back_populates='tags')
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else ""
        }
    

class FacetBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'facet'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='program_facet_association', back_populates='facets')
    
    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='xperience_facet_association', back_populates='facets')

    @declared_attr
    def quests(cls):
        return relationship('Quest', secondary='quest_facet_association', back_populates='facets')
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else ""
        }


class TargetCommunicationStyleBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'target_communication_style'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else ""
        }
    

class LevelBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'level'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def level(cls):
        return Column(Integer, default=1)
    
    @declared_attr
    def xp_required(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def xp_bonus(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'level': self.level,
            'xp_required': self.xp_required,
            'xp_bonus': self.xp_bonus,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else ""
        }


class ProgramCategoryAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program_category_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    @declared_attr
    def category_id(cls):
        return Column(String(36), ForeignKey('category.id'), nullable=False)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    

class ClientProgramCategoryAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'client_program_category_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def category_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    

class ProgramTagAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program_tag_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    @declared_attr
    def tag_id(cls):
        return Column(String(36), ForeignKey('tag.id'), nullable=False)
    
    def __str__(self):
        return self
    

class ProgramFacetAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program_facet_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    @declared_attr
    def facet_id(cls):
        return Column(String(36), ForeignKey('facet.id'), nullable=False)
    
    def __str__(self):
        return self
    
    
class XperienceCategoryAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xperience_category_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)
    
    @declared_attr
    def category_id(cls):
        return Column(String(36), ForeignKey('category.id'), nullable=False)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    

class ClientXperienceCategoryAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'client_xperience_category_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def category_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    

class XperienceTagAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xperience_tag_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)
    
    @declared_attr
    def tag_id(cls):
        return Column(String(36), ForeignKey('tag.id'), nullable=False)
    
    def __str__(self):
        return self
    

class XperienceFacetAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xperience_facet_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)
    
    @declared_attr
    def facet_id(cls):
        return Column(String(36), ForeignKey('facet.id'), nullable=False)
    
    def __str__(self):
        return self


class QuestBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'quest'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def level(cls):
        return Column(Integer, default=1)

    @declared_attr
    def categories(cls):
        return relationship('Category', secondary='quest_category_association', back_populates='quests')

    @declared_attr
    def tags(cls):
        return relationship('Tag', secondary='quest_tag_association', back_populates='quests')

    @declared_attr
    def facets(cls):
        return relationship('Facet', secondary='quest_facet_association', back_populates='quests')
    
    @declared_attr
    def badges(cls):
        return relationship('Badge', secondary='quest_badge_association', back_populates='quests')
    
    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='program_quest_association', back_populates='quests')
    
    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='xperience_quest_association', back_populates='quests')

    @declared_attr
    def packages(cls):
        return relationship('Package', secondary='package_quest_association', back_populates='quests')

    @declared_attr
    def chest_id(cls):
        return Column(String(36), ForeignKey('chest.id'), nullable=True)

    @declared_attr
    def learning_objective(cls):
        return Column(Text)

    @declared_attr
    def status(cls):
        return Column(String(50), default='Draft')

    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)

    @declared_attr
    def configs(cls):
        return Column(JSON)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'level': self.level,
            'learning_objective': self.learning_objective if self.learning_objective else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'status': self.status if self.status else "",
            'configs': json.loads(self.configs) if self.configs else {}
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class QuestCategoryAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'quest_category_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)
    
    @declared_attr
    def category_id(cls):
        return Column(String(36), ForeignKey('category.id'), nullable=False)
    
    def __str__(self):
        return self
    

class QuestTagAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'quest_tag_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)
    
    @declared_attr
    def tag_id(cls):
        return Column(String(36), ForeignKey('tag.id'), nullable=False)
    
    def __str__(self):
        return self
    

class QuestFacetAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'quest_facet_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)
    
    @declared_attr
    def facet_id(cls):
        return Column(String(36), ForeignKey('facet.id'), nullable=False)
    
    def __str__(self):
        return self


class NodeBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'node'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)
        
    @declared_attr
    def name(cls):
        return Column(String(255))
    
    @declared_attr
    def screen_type(cls):
        return Column(String(50))

    @declared_attr
    def quest_type(cls):
        return Column(String(50), default="")

    @declared_attr
    def title(cls):
        return Column(String(500))
    
    @declared_attr
    def sub_title(cls):
        return Column(Text)

    @declared_attr
    def quest_character_id(cls):
        return Column(String(355))

    @declared_attr
    def quest_character_animation(cls):
        return Column(String(355))
    
    @declared_attr
    def quest_character_audio(cls):
        return Column(String(355))

    @declared_attr
    def quest_text(cls):
        return Column(Text)

    @declared_attr
    def answer_style(cls):
        return Column(String(50))

    @declared_attr
    def answer_placeholder(cls):
        return Column(Text)
    
    @declared_attr
    def points(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def next_node_id(cls):
        return Column(String(36))

    @declared_attr
    def max_selection(cls):
        return Column(Integer, default=1)

    @declared_attr
    def min_selection(cls):
        return Column(Integer, default=1)
    
    @declared_attr
    def button_text(cls):
        return Column(String(50))
    
    @declared_attr
    def options(cls):
        return relationship('NodeOption', backref='node', lazy='dynamic', foreign_keys='NodeOption.node_id' )
    
    @declared_attr
    def transcripts(cls):
        return relationship('NodeTranscript', backref='node', lazy='dynamic', foreign_keys='NodeTranscript.node_id' )
    
    @declared_attr
    def branches(cls):
        return relationship('NodeBranch', backref='node', lazy='dynamic', foreign_keys='NodeBranch.node_id' )

    @declared_attr
    def branches_from_ids(cls):
        return Column(JSON)
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)
    
    @declared_attr
    def is_deleted(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'quest_id': self.quest_id,
            'screen_type': self.screen_type if self.screen_type else "",
            'quest_type': self.quest_type if self.quest_type else "",
            'title': self.title if self.title else "",
            'sub_title': self.sub_title if self.sub_title else "",
            'quest_character_id': self.quest_character_id if self.quest_character_id else "",
            'quest_character_animation': self.quest_character_animation if self.quest_character_animation else "",
            'quest_character_audio': self.quest_character_audio if self.quest_character_audio else "",
            'quest_text': self.quest_text if self.quest_text else "",
            'answer_style': self.answer_style if self.answer_style else "",
            'answer_placeholder': self.answer_placeholder if self.answer_placeholder else "",
            'points': self.points,
            'next_node_id': self.next_node_id if self.next_node_id else "",
            'button_text': self.button_text if self.button_text else "Continue",
            'max_selection': self.max_selection,
            'min_selection': self.min_selection,
            'branches_from_ids': json.loads(self.branches_from_ids) if self.branches_from_ids and self.branches_from_ids is not None else [],
            'is_active': self.is_active,
            'is_deleted': self.is_deleted,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class NodeTranscriptBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'node_transcript'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def node_id(cls):
        return Column(String(36), ForeignKey('node.id'), nullable=False)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)

    @declared_attr
    def characters(cls):
        return Column(JSON)
    
    @declared_attr
    def character_id(cls):
        return Column(String(255))
    
    @declared_attr
    def animation(cls):
        return Column(String(255))
    
    @declared_attr
    def text(cls):
        return Column(Text)
    
    @declared_attr
    def show_icon(cls):
        return Column(Boolean, default=False)
    
    @declared_attr
    def audio_en(cls):
        return Column(String(255))

    @declared_attr
    def audios(cls):
        return Column(JSON)

    @declared_attr
    def duration(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'index': self.index,
            'text': self.text if self.text else "",
            'character_id': self.character_id if self.character_id else "",
            'animation': self.animation if self.animation else "",
            'show_icon': self.show_icon,
            'audio_en': self.audio_en if self.audio_en else "",
            'audios': json.loads(self.audios) if self.audios and self.audios is not None else [],
            'duration': self.duration,
            'date_created': format_datetime_with_timezone(self.date_created)
        }


class NodeOptionBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'node_option'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def node_id(cls):
        return Column(String(36), ForeignKey('node.id'), nullable=False)
    
    @declared_attr
    def label(cls):
        return Column(String(255))
    
    @declared_attr
    def value(cls):
        return Column(String(255))

    @declared_attr
    def attribute(cls):
        return Column(String(50), default="Neutral")

    @declared_attr
    def position(cls):
        return Column(String(255), default="")

    @declared_attr
    def label_color(cls):
        return Column(String(50), default="")

    @declared_attr
    def is_correct(cls):
        return Column(Boolean, default=False)
    
    @declared_attr
    def index(cls):
        return Column(Integer, default=0)

    @declared_attr
    def points(cls):
        return Column(Integer, default=0)

    @declared_attr
    def feedback(cls):
        return Column(Text)
    
    @declared_attr
    def next_node_id(cls):
        return Column(String(36))

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'node_id': self.node_id,
            'label': self.label if self.label else "",
            'value': self.value if self.value else "",
            'position': self.position if self.position else "",
            'label_color': self.label_color if self.label_color else "",
            'is_correct': self.is_correct,
            'index': self.index if self.index else 0,
            'points': self.points if self.points else 0,
            'next_node_id': self.next_node_id if self.next_node_id else "",
            'feedback': json.loads(self.feedback) if self.feedback else "",
        }
        
        if keys:
            data = {key: data[key] for key in keys}

        return data


class NodeOptionMatchingBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'node_option_matching'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def node_id(cls):
        return Column(String(36), ForeignKey('node.id'), nullable=False)
    
    @declared_attr
    def node_option_id(cls):
        return Column(String(36), ForeignKey('node_option.id'), nullable=False)
    
    @declared_attr
    def matching_node_option_id(cls):
        return Column(String(36), ForeignKey('node_option.id'), nullable=False)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'node_option_id': self.node_option_id,
            'matching_node_option_id': self.matching_node_option_id
        }
        
    
 
class NodeBranchBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'node_branch'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def node_id(cls):
        return Column(String(36), ForeignKey('node.id'), nullable=False)
    
    @declared_attr
    def condition(cls):
        return Column(String(255))
    
    @declared_attr
    def condition_value(cls):
        return Column(String(255))

    @declared_attr
    def next_node_id(cls):
        return Column(String(36))
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'condition': self.condition if self.condition else "",
            'condition_value': self.condition_value if self.condition_value else "",
            'next_node_id': self.next_node_id if self.next_node_id else ""
        }
    

class ChestBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'chest'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def xp(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def coins(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def gems(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def keys(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def assets(cls):
        return relationship('Asset', secondary='chest_asset_association', backref='chests')

    @declared_attr
    def button_label(cls):
        return Column(String(255))

    @declared_attr
    def facet_xp(cls):
        return Column(JSON)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'xp': self.xp,
            'coins': self.coins,
            'gems': self.gems,
            'keys': self.keys,
            'button_label': self.button_label if self.button_label else "",
            'facet_xp': json.loads(self.facet_xp) if self.facet_xp else [],
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class AssetBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'asset'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def file_type(cls):
        return Column(String(50))
    
    @declared_attr
    def file_path(cls):
        return Column(String(255))
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'file_type': self.file_type if self.file_type else "",
            'file_path': self.file_path if self.file_path else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class ChestAssetAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'chest_asset_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def chest_id(cls):
        return Column(String(36), ForeignKey('chest.id'), nullable=False)
    
    @declared_attr
    def asset_id(cls):
        return Column(String(36), ForeignKey('asset.id'), nullable=False)
    
    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'chest_id': self.chest_id,
            'asset_id': self.asset_id
        }


class BadgeBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'badge'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def achievement_message(cls):
        return Column(Text)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def achievement_type(cls):
        return Column(String(255))
    
    @declared_attr
    def achievement_conditions(cls):
        return Column(JSONB)

    @declared_attr
    def rewards(cls):
        return Column(JSON)

    @declared_attr
    def quests(cls):
        return relationship('Quest', secondary='quest_badge_association', back_populates='badges')

    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='xperience_badge_association', back_populates='badges')

    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='program_badge_association', back_populates='badges')

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'achievement_message': self.achievement_message if self.achievement_message else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'achievement_type': self.achievement_type if self.achievement_type else "",
            'achievement_conditions': json.loads(self.achievement_conditions) if self.achievement_conditions else {},
            'rewards': json.loads(self.rewards) if self.rewards else {},
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class ProgramBadgeAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'program_badge_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    @declared_attr
    def badge_id(cls):
        return Column(String(36), ForeignKey('badge.id'), nullable=False)
    
    def __str__(self):
        return self
    

class XperienceBadgeAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xperience_badge_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)
    
    @declared_attr
    def badge_id(cls):
        return Column(String(36), ForeignKey('badge.id'), nullable=False)
    
    def __str__(self):
        return self


class QuestBadgeAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'quest_badge_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)
    
    @declared_attr
    def badge_id(cls):
        return Column(String(36), ForeignKey('badge.id'), nullable=False)
    
    def __str__(self):
        return self


class PackageBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'package'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)

    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='package_program_association', back_populates='packages')
    
    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='package_xperience_association', back_populates='packages')

    @declared_attr
    def quests(cls):
        return relationship('Quest', secondary='package_quest_association', back_populates='packages')

    @declared_attr
    def clients(cls):
        return relationship('Client', secondary='client_package_association', back_populates='packages')

    @declared_attr
    def status(cls):
        return Column(String(50), default='Draft')
    
    @declared_attr
    def is_active(cls):
        return Column(Boolean, default=True)

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'status': self.status if self.status else "",
            'is_active': self.is_active,
            'is_deleted': self.is_deleted,
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class PackageProgramAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'package_program_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def package_id(cls):
        return Column(String(36), ForeignKey('package.id'), nullable=False)
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)
    
    def __str__(self):
        return self
    

class PackageXperienceAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'package_xperience_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def package_id(cls):
        return Column(String(36), ForeignKey('package.id'), nullable=False)
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)
    
    def __str__(self):
        return self
    

class PackageQuestAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'package_quest_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def package_id(cls):
        return Column(String(36), ForeignKey('package.id'), nullable=False)
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), ForeignKey('quest.id'), nullable=False)
    
    def __str__(self):
        return self


class ClientPackageAssociationBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'client_package_association'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def client_id(cls):
        return Column(String(36), ForeignKey('client.id'), nullable=False)
    
    @declared_attr
    def package_id(cls):
        return Column(String(36), ForeignKey('package.id'), nullable=False)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self


class NotificationBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'notification'

    @declared_attr
    def queue_id(cls):
        return Column(String(36))
    
    @declared_attr
    def recipient_type(cls):
        return Column(String(50), nullable=False)
    
    @declared_attr
    def recipient_ids(cls):
        return Column(JSONB)
    
    @declared_attr
    def title(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def body(cls):
        return Column(Text, nullable=False)

    @declared_attr
    def data(cls):
        return Column(JSONB)

    @declared_attr
    def notification_type(cls):
        return Column(String(50), nullable=False)

    @declared_attr
    def update_badge(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def enable_notification_push(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def enable_email_notification(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def scheduled_for(cls):
        return Column(DateTime, nullable=True)

    @declared_attr
    def client_id(cls):
        return Column(String(36), nullable=True)
    
    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'client_id': self.client_id,
            'queue_id': self.queue_id,
            'title': self.title,
            'body': self.body,
            'data': self.data,
            'recipient_type': self.recipient_type,
            'recipient_ids': self.recipient_ids,
            'notification_type': self.notification_type,
            'scheduled_for': format_datetime_with_timezone(self.scheduled_for) if self.scheduled_for else "",
            'enable_notification_push': self.enable_notification_push,
            'enable_email_notification': self.enable_email_notification,
            'update_badge': self.update_badge,
            'status': self.status,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user,
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class NotificationTemplateBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'notification_template'

    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def title(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def body(cls):
        return Column(Text, nullable=False)

    @declared_attr
    def data(cls):
        return Column(JSONB)
    
    @declared_attr
    def notification_type(cls):
        return Column(String(50), nullable=False)

    @declared_attr
    def recipient_ids(cls):
        return Column(JSONB)

    @declared_attr
    def recipient_type(cls):
        return Column(String(50), nullable=False)

    @declared_attr
    def client_id(cls):
        return Column(String(36))

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name,
            'title': self.title,
            'body': self.body,
            'data': self.data,
            'notification_type': self.notification_type,
            'recipient_type': self.recipient_type,  
            'recipient_ids': self.recipient_ids,
            'status': self.status,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class UserNotificationBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_notification'
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def notification_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def is_read(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def is_badge_read(cls):
        return Column(Boolean, default=False)

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'notification_id': self.notification_id,
            'is_read': self.is_read,
            'is_badge_read': self.is_badge_read,
            'status': self.status,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user
        }


class UserNotificationLogBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_notification_log'
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def user_notification_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def device_id(cls):
        return Column(String(255))

    @declared_attr
    def device_type(cls):
        return Column(String(255))

    @declared_attr
    def device_token(cls):
        return Column(String(255))

    @declared_attr
    def error_message(cls):
        return Column(Text)
    
    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'user_notification_id': self.notification_id,
            'device_token': self.device_token,
            'device_id': self.device_id,
            'device_type': self.device_type,
            'error_message': self.error_message,
            'status': self.status,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user
        }


class BannerBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'banner'
    
    @declared_attr
    def title(cls):
        return Column(String(255))
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def image_webapp(cls):
        return Column(String(255))
    
    @declared_attr
    def link(cls):
        return Column(String(255))
    
    @declared_attr
    def link_type(cls):
        return Column(String(50))

    @declared_attr
    def order(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title if self.title else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'image_webapp': self.image_webapp if self.image_webapp else "",
            'link': self.link if self.link else "",
            'link_type': self.link_type if self.link_type else "",
            'order': self.order,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class CharacterBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'character'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def key(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def animations(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=True)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name if self.name else "",
            'key': self.key if self.key else "",
            'animations': json.loads(self.animations) if self.animations else [],
            'user_id': self.user_id if self.user_id else "",
            'index': self.index
        }


class TimezoneMappingBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'timezone_mapping'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def abbreviation(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def full_name(cls):
        return Column(String(255), nullable=False)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'abbreviation': self.abbreviation if self.abbreviation else "",
            'full_name': self.full_name if self.full_name else ""
        }


class ContentPublishLogBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'content_publish_log'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def content_id(cls):
        return Column(String(36))

    @declared_attr
    def content_type(cls):
        return Column(String(255))
    
    @declared_attr
    def user_id(cls):
        return Column(String(36))
        
    @declared_attr
    def user_name(cls):
        return Column(String(255))

    @declared_attr
    def version(cls):
        return Column(String(255))

    @declared_attr
    def backup_path(cls):
        return Column(String(255))
    
    @declared_attr
    def date_published(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'content_id': self.content_id,
            'content_type': self.content_type if self.content_type else "",
            'user_id': self.user_id,
            'user_name': self.user_name if self.user_name else "",
            'version': self.version if self.version else "",
            'backup_path': self.backup_path if self.backup_path else "",
            'date_published': format_datetime_with_timezone(self.date_published) if self.date_published else "",
        }


class FileBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'file'

    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def type(cls):
        return Column(String(100))

    @declared_attr
    def size(cls):
        return Column(Integer)

    @declared_attr
    def info(cls):
        return Column(JSON)

    def __str__(self):
        return self

    def to_dict(self):
        data = {
            'content_type': self.type,
            'size': self.size
        }
        if self.info:
            data.update(self.info)
        return data


class UserBoostBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_boost'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def start(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def end(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def source_id(cls):
        return Column(String(255))

    @declared_attr
    def source(cls):
        return Column(String(255))
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'start': format_datetime_with_timezone(self.start) if self.start else "",
            'end': format_datetime_with_timezone(self.end) if self.end else "",
            'source_id': self.source_id if self.source_id else "",
            'source': self.source if self.source else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class UserPowerUpBase(db.Model):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_powerup'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def start(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def end(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def source_id(cls):
        return Column(String(255))

    @declared_attr
    def source(cls):
        return Column(String(255))
    
    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)
    
    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'start': format_datetime_with_timezone(self.start) if self.start else "",
            'end': format_datetime_with_timezone(self.end) if self.end else "",
            'source_id': self.source_id if self.source_id else "",
            'source': self.source if self.source else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class StoreBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'store'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def description(cls):
        return Column(Text)

    @declared_attr
    def image(cls):
        return Column(String(255))

    @declared_attr
    def store_type(cls):
        return Column(String(50), default="")

    @declared_attr
    def item_type(cls):
        return Column(String(50), default="")

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)

    @declared_attr
    def config(cls):
        return Column(JSON)

    @declared_attr
    def price(cls):
        return Column(Integer, default=0)

    @declared_attr
    def unit(cls):
        return Column(String(50), default="")

    @declared_attr
    def asset_id(cls):
        return Column(String(36))
    
    @declared_attr
    def button_label(cls):
        return Column(String(255))
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'store_type': self.store_type if self.store_type else "",
            'item_type': self.item_type if self.item_type else "",
            'index': self.index,
            'config': json.loads(self.config) if self.config else {},
            'is_deleted': self.is_deleted,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'price': self.price if self.price else 0,
            'unit': self.unit if self.unit else "",
            'asset_id': self.asset_id if self.asset_id else "",
            'button_label': self.button_label if self.button_label else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class UserStoreBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_store'

    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def store_item_id(cls):
        return Column(String(36))
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'store_item_id': self.store_item_id,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class TaskBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'task'

    @declared_attr
    def queue_id(cls):
        return Column(String(36))

    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def func(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def scheduled_for(cls):
        return Column(DateTime, nullable=False)

    @declared_attr
    def data(cls):
        return Column(JSONB)

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'queue_id': self.queue_id,
            'name': self.name,
            'func': self.func,
            'scheduled_for': format_datetime_with_timezone(self.scheduled_for) if self.scheduled_for else "",
            'data': self.data if self.data else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user,
            'status': self.status
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class TaskLogBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'task_log'

    @declared_attr
    def task_id(cls):
        return Column(String(36))

    @declared_attr
    def result(cls):
        return Column(JSON)
    
    @declared_attr
    def error_message(cls):
        return Column(Text)

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'task_id': self.task_id,
            'result': self.result if self.result else "",
            'error_message': self.error_message if self.error_message else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user,
            'status': self.status
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class SchedulerBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'scheduler'

    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def cron(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def func(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def data(cls):
        return Column(JSONB)

    @declared_attr
    def last_run_time(cls):
        return Column(DateTime)

    @declared_attr
    def next_run_time(cls):
        return Column(DateTime)

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name,
            'cron': self.cron,
            'func': self.func,
            'data': self.data if self.data else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user,
            'last_run_time': format_datetime_with_timezone(self.last_run_time) if self.last_run_time else "",
            'next_run_time': format_datetime_with_timezone(self.next_run_time) if self.next_run_time else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class SchedulerLogBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'scheduler_log'
    
    @declared_attr
    def scheduler_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def start_time(cls):
        return Column(DateTime, nullable=False)
    
    @declared_attr
    def end_time(cls):
        return Column(DateTime)
    
    @declared_attr
    def error_message(cls):
        return Column(Text)
    
    @declared_attr
    def result(cls):
        return Column(JSON)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'scheduler_id': self.scheduler_id,
            'start_time': format_datetime_with_timezone(self.start_time),
            'end_time': format_datetime_with_timezone(self.end_time) if self.end_time else None,
            'error_message': self.error_message if self.error_message else None,
            'result': self.result if self.result else None,
            'status': self.status,
            'date_created': format_datetime_with_timezone(self.date_created),
            'date_updated': format_datetime_with_timezone(self.date_updated)
        }


class SettingBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'setting'

    @declared_attr
    def key(cls):
        return Column(String(255), unique=True, nullable=False)
    
    @declared_attr
    def value(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)

    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'date_created': format_datetime_with_timezone(self.date_created),
            'date_updated': format_datetime_with_timezone(self.date_updated)
        }


class ContentLogBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'content_log'
    
    @declared_attr
    def content_id(cls):
        return Column(String(36), nullable=True)
    
    @declared_attr
    def content_type(cls):
        return Column(String(255), nullable=True)
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), nullable=True)
    
    @declared_attr
    def version(cls):
        return Column(String(255), nullable=True)
    
    @declared_attr
    def backup_path(cls):
        return Column(String(255), nullable=True)
    
    @declared_attr
    def operation(cls):
        return Column(String(500), nullable=True)

    @declared_attr
    def status(cls):
        return Column(String(50), nullable=True)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'content_id': self.content_id,
            'content_type': self.content_type,
            'user_id': self.user_id,
            'version': self.version,
            'backup_path': self.backup_path,
            'operation': self.operation,
            'status': self.status,
            'date_created': format_datetime_with_timezone(self.date_created),
            'date_updated': format_datetime_with_timezone(self.date_updated)
        }


class UserBlockBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_block'
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def blocked_user_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def reason(cls):
        return Column(String(255))

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'blocked_user_id': self.blocked_user_id,
            'reason': self.reason,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else None
        }


class EntitlementGroupBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'entitlement_group'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def directory_mapping_key(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def directory_mapping_value(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def entitlement_group_type(cls):
        return Column(String(50), nullable=False)

    @declared_attr
    def users(cls):
        return relationship('User', secondary='entitlement_group_user_assignment', backref='entitlement_groups')

    @declared_attr
    def programs(cls):
        return relationship('Program', secondary='entitlement_group_program_assignment', backref='entitlement_groups')

    @declared_attr
    def xperiences(cls):
        return relationship('Xperience', secondary='entitlement_group_xperience_assignment', backref='entitlement_groups')

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'directory_mapping_key': self.directory_mapping_key if self.directory_mapping_key else "",
            'directory_mapping_value': self.directory_mapping_value if self.directory_mapping_value else "",
            'entitlement_group_type': self.entitlement_group_type if self.entitlement_group_type else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data


class EntitlementGroupUserAssignmentBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'entitlement_group_user_assignment'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)

    @declared_attr
    def entitlement_group_id(cls):
        return Column(String(36), ForeignKey('entitlement_group.id'), nullable=False)

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'entitlement_group_id': self.entitlement_group_id,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class EntitlementGroupXperienceAssignmentBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'entitlement_group_xperience_assignment'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def entitlement_group_id(cls):
        return Column(String(36), ForeignKey('entitlement_group.id'), nullable=False)

    @declared_attr
    def xperience_id(cls):
        return Column(String(36), ForeignKey('xperience.id'), nullable=False)

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'entitlement_group_id': self.entitlement_group_id,
            'xperience_id': self.xperience_id,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class EntitlementGroupProgramAssignmentBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'entitlement_group_program_assignment'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def entitlement_group_id(cls):
        return Column(String(36), ForeignKey('entitlement_group.id'), nullable=False)

    @declared_attr
    def program_id(cls):
        return Column(String(36), ForeignKey('program.id'), nullable=False)

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'entitlement_group_id': self.entitlement_group_id,
            'program_id': self.program_id,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }


class UserLiveActivityBase(BaseModel):
    __tablename__ = 'user_live_activity'
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def device_id(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def activity_token(cls):
        return Column(Text, nullable=True)
    
    @declared_attr
    def activity_id(cls):
        return Column(String(36), nullable=True)

    @declared_attr
    def activity_type(cls):
        return Column(String(50), nullable=True)

    @declared_attr
    def activity_name(cls):
        return Column(String(255), nullable=True)
    
    @declared_attr
    def start_time(cls):
        return Column(DateTime, nullable=False, default=datetime.datetime.utcnow)
    
    @declared_attr
    def end_time(cls):
        return Column(DateTime, nullable=False, default=datetime.datetime.utcnow)

    @declared_attr
    def result(cls):
        return Column(Text, nullable=True)

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'activity_token': self.activity_token,
            'device_token': self.device_token,
            'activity_id': self.activity_id,
            'activity_type': self.activity_type,
            'activity_name': self.activity_name,
            'start_time': format_datetime_with_timezone(self.start_time) if self.start_time else None,
            'end_time': format_datetime_with_timezone(self.end_time) if self.end_time else None,
            'result': self.result if self.result else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else None,
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else None
        }
        if keys:
            data = {key: data[key] for key in keys}
        return data






class PublishedQuestBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_quest'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def quest_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def level(cls):
        return Column(Integer, default=1)

    @declared_attr
    def chest_id(cls):
        return Column(String(36), nullable=True)

    @declared_attr
    def learning_objective(cls):
        return Column(Text)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def category_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def tag_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def facet_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def revision(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def is_latest(cls):
        return Column(Boolean, default=True)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'level': self.level,
            'learning_objective': self.learning_objective if self.learning_objective else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data



class PublishedXperienceBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_xperience'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def xperience_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def level(cls):
        return Column(Integer, default=1)

    @declared_attr
    def learning_objective(cls):
        return Column(Text)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def image_webapp(cls):
        return Column(String(255))

    @declared_attr
    def chest_id(cls):
        return Column(String(36), nullable=True)

    @declared_attr
    def requires_quest_completion(cls):
        return Column(Boolean, default=True)

    @declared_attr
    def keys_required(cls):
        return Column(Integer, default=0)

    @declared_attr
    def level_required(cls):
        return Column(Integer, default=1)
        
    @declared_attr
    def category_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def tag_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def facet_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def quest_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def revision(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def is_latest(cls):
        return Column(Boolean, default=True)
    

    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'level': self.level,
            'learning_objective': self.learning_objective if self.learning_objective else "",
            'image_webapp': self.image_webapp if self.image_webapp else "",
            'requires_quest_completion': self.requires_quest_completion,
            'keys_required': self.keys_required,
            'level_required': self.level_required,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    


class PublishedProgramBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_program'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def program_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def description(cls):
        return Column(Text)
    
    @declared_attr
    def image(cls):
        return Column(String(255))
    
    @declared_attr
    def level(cls):
        return Column(Integer, default=1)

    @declared_attr
    def learning_objective(cls):
        return Column(Text)
    
    @declared_attr
    def image_webapp(cls):
        return Column(String(255))

    @declared_attr
    def chest_id(cls):
        return Column(String(36), nullable=True)

    @declared_attr
    def requires_quest_completion(cls):
        return Column(Boolean, default=True)

    @declared_attr
    def keys_required(cls):
        return Column(Integer, default=0)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def category_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def tag_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def facet_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def quest_ids(cls):
        return Column(JSON, nullable=True)
    
    @declared_attr
    def revision(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def is_latest(cls):
        return Column(Boolean, default=True)
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'name': self.name if self.name else "",
            'description': self.description if self.description else "",
            'image': self.image if self.image else "",
            'level': self.level,
            'learning_objective': self.learning_objective if self.learning_objective else "",
            'image_webapp': self.image_webapp if self.image_webapp else "",
            'requires_quest_completion': self.requires_quest_completion,
            'keys_required': self.keys_required,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class PublishedNodeBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_node'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def node_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def published_quest_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def quest_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)

    @declared_attr
    def name(cls):
        return Column(String(255))

    @declared_attr
    def screen_type(cls):
        return Column(String(50))

    @declared_attr
    def quest_type(cls):
        return Column(String(50), default="")

    @declared_attr
    def title(cls):
        return Column(String(500))

    @declared_attr
    def sub_title(cls):
        return Column(Text)

    @declared_attr
    def quest_character_id(cls):
        return Column(String(355))

    @declared_attr
    def quest_character_animation(cls):
        return Column(String(355))

    @declared_attr
    def quest_character_audio(cls):
        return Column(String(355))

    @declared_attr
    def quest_text(cls):
        return Column(Text)

    @declared_attr
    def answer_style(cls):
        return Column(String(50))

    @declared_attr
    def answer_placeholder(cls):
        return Column(Text)

    @declared_attr
    def points(cls):
        return Column(Integer, default=0)

    @declared_attr
    def next_node_id(cls):
        return Column(String(36))

    @declared_attr
    def max_selection(cls):
        return Column(Integer, default=1)

    @declared_attr
    def min_selection(cls):
        return Column(Integer, default=1)

    @declared_attr
    def button_text(cls):
        return Column(String(50))

    @declared_attr
    def branches_from_ids(cls):
        return Column(JSON)
    
    @declared_attr
    def revision(cls):
        return Column(Integer, default=0)
    
    @declared_attr
    def is_latest(cls):
        return Column(Boolean, default=True)

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'name': self.name if self.name else "",
            'quest_id': self.quest_id,
            'screen_type': self.screen_type if self.screen_type else "",
            'quest_type': self.quest_type if self.quest_type else "",
            'title': self.title if self.title else "",
            'sub_title': self.sub_title if self.sub_title else "",
            'quest_character_id': self.quest_character_id if self.quest_character_id else "",
            'quest_character_animation': self.quest_character_animation if self.quest_character_animation else "",
            'quest_character_audio': self.quest_character_audio if self.quest_character_audio else "",
            'quest_text': self.quest_text if self.quest_text else "",
            'answer_style': self.answer_style if self.answer_style else "",
            'answer_placeholder': self.answer_placeholder if self.answer_placeholder else "",
            'points': self.points,
            'next_node_id': self.next_node_id if self.next_node_id else "",
            'button_text': self.button_text if self.button_text else "Continue",
            'max_selection': self.max_selection,
            'min_selection': self.min_selection,
            'branches_from_ids': json.loads(self.branches_from_ids) if self.branches_from_ids and self.branches_from_ids is not None else []
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    

class PublishedNodeTranscriptBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_node_transcript'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def published_node_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)

    @declared_attr
    def characters(cls):
        return Column(JSON)

    @declared_attr
    def character_id(cls):
        return Column(String(255))

    @declared_attr
    def animation(cls):
        return Column(String(255))

    @declared_attr
    def text(cls):
        return Column(Text)

    @declared_attr
    def show_icon(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def audio_en(cls):
        return Column(String(255))

    @declared_attr
    def audios(cls):
        return Column(JSON)

    @declared_attr
    def duration(cls):
        return Column(Integer, default=0)

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'index': self.index,
            'text': self.text if self.text else "",
            'character_id': self.character_id if self.character_id else "",
            'animation': self.animation if self.animation else "",
            'show_icon': self.show_icon,
            'audio_en': self.audio_en if self.audio_en else "",
            'audios': json.loads(self.audios) if self.audios and self.audios is not None else [],
            'duration': self.duration,
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
        }
    

class PublishedNodeOptionBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_node_option'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def node_option_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def published_node_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def label(cls):
        return Column(String(255))

    @declared_attr
    def value(cls):
        return Column(String(255))

    @declared_attr
    def attribute(cls):
        return Column(String(50), default="Neutral")

    @declared_attr
    def position(cls):
        return Column(String(255), default="")

    @declared_attr
    def label_color(cls):
        return Column(String(50), default="")

    @declared_attr
    def is_correct(cls):
        return Column(Boolean, default=False)

    @declared_attr
    def index(cls):
        return Column(Integer, default=0)

    @declared_attr
    def points(cls):
        return Column(Integer, default=0)

    @declared_attr
    def feedback(cls):
        return Column(Text)

    @declared_attr
    def next_node_id(cls):
        return Column(String(36))
    
    def __str__(self):
        return self
    
    def to_dict(self, keys=None):
        data =  {
            'id': self.id,
            'label': self.label if self.label else "",
            'value': self.value if self.value else "",
            'attribute': self.attribute if self.attribute else "",
            'position': self.position if self.position else "",
            'label_color': self.label_color if self.label_color else "",
            'is_correct': self.is_correct,
            'index': self.index,
            'points': self.points,
            'feedback': json.loads(self.feedback) if self.feedback and self.feedback is not None else "",
            'next_node_id': self.next_node_id if self.next_node_id else ""
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    
        
    

class PublishedNodeOptionMatchingBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_node_option_matching'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def published_node_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def node_option_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def matching_node_option_id(cls):
        return Column(String(36), nullable=False)
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'node_option_id': self.node_option_id,
            'matching_node_option_id': self.matching_node_option_id
        }
    

class PublishedNodeBranchBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'published_node_branch'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def published_node_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def condition(cls):
        return Column(String(255))

    @declared_attr
    def condition_value(cls):
        return Column(String(255))

    @declared_attr
    def next_node_id(cls):
        return Column(String(36))
    
    def __str__(self):
        return self
    
    def to_dict(self):
        return {
            'id': self.id,
            'condition': self.condition if self.condition else "",
            'condition_value': self.condition_value if self.condition_value else "",
            'next_node_id': self.next_node_id if self.next_node_id else ""
        }
    


class XapaDayBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'xapa_day'

    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())

    @declared_attr
    def date(cls):
        return Column(Date, nullable=False)

    @declared_attr
    def text(cls):
        return Column(Text, nullable=False)

    def __str__(self):
        return self

    def to_dict(self, keys=None):
        data = {
            'id': self.id,
            'date': self.date.strftime('%Y-%m-%d') if self.date else "",
            'text': self.text if self.text else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else "",
            'create_user': self.create_user,
            'update_user': self.update_user,
            'status': self.status
        }

        if keys:
            data = {key: data[key] for key in keys}

        return data
    


class UserStyleBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_style'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str()) 

    @declared_attr
    def user_id(cls):
        return Column(String(36), nullable=False)

    @declared_attr
    def style_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def style_value(cls):
        return Column(String(255), nullable=False)

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'style_id': self.style_id,
            'style_value': self.style_value if self.style_value else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else ""
        }
    


class UserStyleMappingBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_style_mapping'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str()) 
    
    @declared_attr
    def style_name(cls):
        return Column(String(255), nullable=False)
    
    @declared_attr
    def mapping_quest_id(cls):
        return Column(String(36), nullable=False)
    
    @declared_attr
    def mapping_configuration(cls):
        return Column(JSON, nullable=True)

    @declared_attr
    def date_created(cls):
        return Column(DateTime, default=datetime.datetime.utcnow)

    @declared_attr
    def date_updated(cls):
        return Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __str__(self):
        return self

    def to_dict(self):
        return {
            'id': self.id,
            'style_name': self.style_name if self.style_name else "",
            'mapping_configuration': json.loads(self.mapping_configuration) if self.mapping_configuration and self.mapping_configuration is not None else [],
            'mapping_quest_id': self.mapping_quest_id if self.mapping_quest_id else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else ""
        }


class UserAgreementBase(BaseModel):
    __abstract__ = True

    @declared_attr
    def __tablename__(cls):
        return 'user_agreement'
    
    @declared_attr
    def id(cls):
        return Column(String(36), primary_key=True, default=lambda: uuid7str())
    
    @declared_attr
    def user_id(cls):
        return Column(String(36), ForeignKey('user.id'), nullable=False)
    
    @declared_attr
    def client_id(cls):
        return Column(String(36), nullable=True)  # Blank for global
    
    @declared_attr
    def terms_type(cls):
        return Column(String(255), nullable=False)  # e.g., 'AI'
    
    @declared_attr
    def terms_version(cls):
        return Column(String(255), nullable=False)  # e.g., '1.0'

    def __str__(self):
        return f"{self.user_id}-{self.terms_type}-{self.terms_version}"

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'client_id': self.client_id if self.client_id else "",
            'terms_type': self.terms_type if self.terms_type else "",
            'terms_version': self.terms_version if self.terms_version else "",
            'date_created': format_datetime_with_timezone(self.date_created) if self.date_created else "",
            'date_updated': format_datetime_with_timezone(self.date_updated) if self.date_updated else ""
        }