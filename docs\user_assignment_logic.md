
# User Client Assignment Logic Documentation

## Overview

The `UserClientAssignment` class in `/api/cms/users.py` (lines 657-764) handles the assignment of users to multiple clients in the multi-tenant XAPA system.

---

## Key Features

### GET Method
**Endpoint:**
```http
GET /api/cms/users/{user_id}/clients
```

- **Purpose:** Retrieve all clients assigned to a specific user
- **Permission:** Requires `super_admin` role
- **Logic:**
  1. Finds user by ID
  2. Queries `UserAssignment` table for the user's assignments
  3. Uses `master_user_id` to find all related client assignments
  4. Returns list of client IDs

### POST Method
**Endpoint:**
```http
POST /api/cms/users/{user_id}/clients
```

- **Purpose:** Assign/unassign clients to a user
- **Permission:** Requires `super_admin` role
- **Input:** `client_ids` array and optional `send_invite` boolean

---

## Assignment Process

### Master User Creation

- If user has no existing assignments, creates a new `MasterUser` record
- Generates random username using `secrets.token_hex(8)`
- Links user to master user account for multi-client support

### Client Assignment Logic

1. **Remove Unassigned Clients:** Deletes assignments for clients not in the new `client_ids` list
2. **Add New Clients:** Creates `UserAssignment` records for new client assignments
3. **User Sync:** Calls `sync_user()` to ensure user exists in each client's database
4. **Client Cleanup:** Calls `delete_client_user()` for removed client assignments

### User Status Updates

- **With Assignments:** Sets `is_active=True`, `status="active"`, `is_deleted=False`
- **Without Assignments:** Sets `is_active=False`, `status="unassigned"`
- **Lead Users:** If user has company info but no assignments, sets `status="lead"`

### Email Invitations

- Optional `send_invite` parameter triggers invitation email
- Updates `last_invitation_sent` timestamp on successful email delivery
- Uses `EmailService` for email delivery

---

## Database Tables Involved

- **UserAssignment:** Links users to clients via `master_user_id`
- **MasterUser:** Central user record for multi-client assignments
- **Client:** Client information with `id_key` for tenant identification
- **User:** User records (exists in both master and client databases)

---

## Multi-Tenant Support

The system maintains user records in both:

- **Master Database:** Global user records and assignments
- **Client Databases:** Tenant-specific user data via `sync_user()`

---

This architecture allows users to be assigned to multiple clients while maintaining data isolation between tenants.