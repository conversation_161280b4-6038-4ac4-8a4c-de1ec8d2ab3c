import unittest
from unittest.mock import Mock, patch
from flask import <PERSON>lask, g

from api.app.client_settings import app_client_settings_api
from api.common.helper import create_response
from clientmodels import Client


class TestClientSettingsAPI(unittest.TestCase):
    def setUp(self):
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        
        # Mock the namespace
        self.api = app_client_settings_api
        
    @patch('api.app.client_settings.g')
    @patch('api.app.client_settings.Client')
    def test_get_client_settings_success(self, mock_client_model, mock_g):
        """Test successful retrieval of client settings"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        
        # Mock client data
        mock_client = Mock()
        mock_client.id = 'client-123'
        mock_client.name = 'Test Client'
        mock_client.settings = {'feature_x': True, 'theme': 'dark'}
        
        # Mock database query
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        mock_filter.first.return_value = mock_client
        mock_db_session.query.return_value = mock_query
        
        # Import and instantiate the resource
        from api.app.client_settings import ClientSettings
        resource = ClientSettings()
        
        # Call the method
        response = resource.get()
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['client_id'], 'client-123')
        self.assertEqual(response_data['data']['client_name'], 'Test Client')
        self.assertEqual(response_data['data']['client_settings'], {'feature_x': True, 'theme': 'dark'})
        
    @patch('api.app.client_settings.g')
    @patch('api.app.client_settings.Client')
    def test_get_client_settings_not_found(self, mock_client_model, mock_g):
        """Test client not found scenario"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'non-existent-tenant'
        
        # Mock database query returning None
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        mock_filter.first.return_value = None
        mock_db_session.query.return_value = mock_query
        
        # Import and instantiate the resource
        from api.app.client_settings import ClientSettings
        resource = ClientSettings()
        
        # Call the method
        response = resource.get()
        
        # Verify the result
        self.assertEqual(response.status_code, 404)
        response_data = response.get_json()
        self.assertEqual(response_data['message'], 'Client not found')

    @patch('api.app.client_settings.g')
    @patch('api.app.client_settings.Client')
    def test_get_client_settings_empty_fields(self, mock_client_model, mock_g):
        """Test client with empty/null fields"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        
        # Mock client data with empty fields
        mock_client = Mock()
        mock_client.id = 'client-456'
        mock_client.name = None  # Empty name
        mock_client.settings = None  # Empty settings
        
        # Mock database query
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        mock_filter.first.return_value = mock_client
        mock_db_session.query.return_value = mock_query
        
        # Import and instantiate the resource
        from api.app.client_settings import ClientSettings
        resource = ClientSettings()
        
        # Call the method
        response = resource.get()
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['client_id'], 'client-456')
        self.assertEqual(response_data['data']['client_name'], '')  # Should default to empty string
        self.assertEqual(response_data['data']['client_settings'], {})  # Should default to empty dict

    @patch('api.app.client_settings.g')
    @patch('api.app.client_settings.Client')
    def test_ai_preferences_fallback_to_global(self, mock_client_model, mock_g):
        """Test AI preferences fallback to global client settings"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        
        # Mock client data without AI preferences
        mock_client = Mock()
        mock_client.id = 'client-789'
        mock_client.name = 'Test Client'
        mock_client.settings = {
            'feature_configuration': {
                'ai': {
                    'enabled': True,
                    'description': 'Enabled AI'
                    # Missing preferences
                }
            }
        }
        
        # Mock global client with AI preferences
        mock_global_client = Mock()
        mock_global_client.settings = {
            'feature_configuration': {
                'ai': {
                    'enabled': True,
                    'description': 'Enabled AI',
                    'preferences': {
                        'agreement_title': 'Global AI Agreement',
                        'agreement_terms_body': '<p>Global terms</p>',
                        'checkbox_label': 'Global checkbox',
                        'button_label': 'Global button',
                        'version': '1.0'
                    }
                }
            }
        }
        
        # Mock database queries
        def mock_query_side_effect(model):
            query_mock = Mock()
            filter_mock = Mock()
            query_mock.filter.return_value = filter_mock
            
            if model == Client:
                # First call for the tenant client, second for global client
                if mock_db_session.query.call_count == 1:
                    filter_mock.first.return_value = mock_client
                else:
                    filter_mock.first.return_value = mock_global_client
            
            return query_mock
        
        mock_db_session.query.side_effect = mock_query_side_effect
        
        # Import and instantiate the resource
        from api.app.client_settings import ClientSettings
        resource = ClientSettings()
        
        # Call the method
        response = resource.get()
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['client_id'], 'client-789')
        
        # Check that AI preferences were filled from global settings
        ai_preferences = response_data['data']['client_settings']['feature_configuration']['ai']['preferences']
        self.assertEqual(ai_preferences['agreement_title'], 'Global AI Agreement')
        self.assertEqual(ai_preferences['agreement_terms_body'], '<p>Global terms</p>')
        self.assertEqual(ai_preferences['checkbox_label'], 'Global checkbox')
        self.assertEqual(ai_preferences['button_label'], 'Global button')
        self.assertEqual(ai_preferences['version'], '1.0')

    @patch('api.app.client_settings.g')
    @patch('api.app.client_settings.Client')
    def test_ai_preferences_existing_not_overridden(self, mock_client_model, mock_g):
        """Test that existing AI preferences are not overridden by global settings"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        
        # Mock client data with complete AI preferences
        mock_client = Mock()
        mock_client.id = 'client-999'
        mock_client.name = 'Test Client'
        mock_client.settings = {
            'feature_configuration': {
                'ai': {
                    'enabled': True,
                    'description': 'Enabled AI',
                    'preferences': {
                        'agreement_title': 'Client-specific AI Agreement',
                        'agreement_terms_body': '<p>Client-specific terms</p>',
                        'checkbox_label': 'Client-specific checkbox',
                        'button_label': 'Client-specific button',
                        'version': '2.0'
                    }
                }
            }
        }
        
        # Mock database query
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        mock_filter.first.return_value = mock_client
        mock_db_session.query.return_value = mock_query
        
        # Import and instantiate the resource
        from api.app.client_settings import ClientSettings
        resource = ClientSettings()
        
        # Call the method
        response = resource.get()
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['client_id'], 'client-999')
        
        # Check that existing AI preferences were preserved
        ai_preferences = response_data['data']['client_settings']['feature_configuration']['ai']['preferences']
        self.assertEqual(ai_preferences['agreement_title'], 'Client-specific AI Agreement')
        self.assertEqual(ai_preferences['agreement_terms_body'], '<p>Client-specific terms</p>')
        self.assertEqual(ai_preferences['checkbox_label'], 'Client-specific checkbox')
        self.assertEqual(ai_preferences['button_label'], 'Client-specific button')
        self.assertEqual(ai_preferences['version'], '2.0')


if __name__ == '__main__':
    unittest.main()