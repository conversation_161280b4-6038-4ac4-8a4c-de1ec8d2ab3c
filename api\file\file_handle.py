import logging
import mimetypes
from io import BytesIO
import datetime
import os

import werkzeug
from flask import request, send_file, g
from flask_restx import Namespace, Resource, reqparse
from PIL import Image

from api.common.helper import create_response
from api.common.file import FileService
from app import storage, cache
from clientmodels import Client, get_db_session
import clientmodels

# Configure logger
logger = logging.getLogger(__name__)

api_file = Namespace('api_file', description='File related operations')

file_upload_parser = reqparse.RequestParser()
file_upload_parser.add_argument('file', type=werkzeug.datastructures.FileStorage, location='files', required=True, help='File to upload')

@api_file.doc(security='bearer')
@api_file.route('<path:folder>/<filename>', methods=['GET'])
@api_file.route('', methods=['POST'])
class FileObject(Resource):
    def get(self, folder, filename):
        file_data = storage.download_file(folder, filename)
        if file_data:
            # Read the file data into bytes
            file_bytes = file_data.read()
            stream = BytesIO(file_bytes)
            content_type, _ = mimetypes.guess_type(filename)
            if content_type is None:
                content_type = 'application/octet-stream'
            # Get file size
            file_size = len(file_bytes)
            # Create response data
            response_data = {
                'size': file_size,
                'content_type': content_type
            }
            # If it's an image, get additional metadata
            if content_type and content_type.startswith('image/'):
                try:
                    # Create a copy of the stream for image processing
                    image_stream = BytesIO(file_bytes)
                    with Image.open(image_stream) as img:
                        response_data.update({
                            'width': img.width,
                            'height': img.height,
                            'format': img.format.lower()
                        })
                except Exception as e:
                    logger.error(f"Error getting image metadata: {str(e)}")
            # Reset stream position for file sending
            stream.seek(0)
            if content_type.startswith('application/'):
                return send_file(
                    stream, 
                    mimetype=content_type, 
                    as_attachment=True,
                    download_name=filename
                )
            else:
                response = send_file(
                    stream, 
                    mimetype=content_type, 
                    as_attachment=False
                )
                # Add metadata headers
                response.headers['X-File-Metadata'] = str(response_data)
                return response
        else:
            return create_response("api.file.not_found", 404)

    @api_file.expect(file_upload_parser, validate=True)
    @api_file.doc(description='Upload a file.')
    def post(self):
        try:
            args = file_upload_parser.parse_args()
            file_data = args.get('file')

            if not file_data:
                logger.error("No file provided in request")
                return create_response("No file provided", status=400)

            success, result = FileService.process_upload(file_data=file_data)

            if success:
                return create_response("File uploaded", data={"filename": result})
            else:
                logger.error(f"File upload failed - Error: {result}")
                return create_response(result, 400 if "size" in result or "type" in result else 500)
        except Exception as e:
            logger.exception("Unexpected error during file upload", exc_info=True)
            return create_response(f"Upload failed: {str(e)}", 500)


@api_file.doc(security='bearer')
@api_file.route('webapp_image/regenerate_thumbnail/<string:client_id>/<string:entity_type>/<int:index>', methods=['POST'])
class WebappImageThumbnailRegenerate(Resource):
    @api_file.doc(description='Regenerate thumbnail for existing webapp image with correct sizing.')
    def post(self, client_id, entity_type, index):
        try:
            # Validate entity type
            valid_entity_types = ['Program', 'Xperience', 'Banner']
            if entity_type not in valid_entity_types:
                return create_response(f"Invalid entity type. Must be one of: {', '.join(valid_entity_types)}", status=400)

            # Get client and set tenant
            client = g.db_session.query(Client).filter_by(id=client_id).first()
            if not client:
                return create_response("Client not found", status=404)

            tenant_id = client.id_key
            storage.container_name = tenant_id

            size = 50
            offset = (index - 1) * size

            db_session = get_db_session(tenant_id)
            if not db_session:
                return create_response("Invalid tenant ID", status=400)

            obj_class = getattr(clientmodels, entity_type)
            count = db_session.query(obj_class).filter(obj_class.image_webapp.isnot(None)).count()
            processed_count = 0
            success_count = 0
            
            try:
                # Get entities with image_webapp in batches
                entities = db_session.query(obj_class).filter(
                    obj_class.image_webapp.isnot(None),
                    obj_class.is_deleted == False
                ).order_by(obj_class.id.asc()).offset(offset).limit(size).all()

                for entity in entities:
                    processed_count += 1
                    
                    # Get the webapp image path
                    image_webapp_path = getattr(entity, 'image_webapp', None)
                    if not image_webapp_path:
                        continue

                    try:
                        # Parse the image path to extract container and filename
                        path_parts = image_webapp_path.split('/')
                        if len(path_parts) < 2:
                            logger.warning(f"Invalid webapp image path format for {entity_type} {entity.id}: {image_webapp_path}")
                            continue

                        folder = '/'.join(path_parts[:-1])
                        filename = path_parts[-1]

                        # Download the existing webapp image
                        file_data = storage.download_file(folder, filename)
                        if not file_data:
                            logger.warning(f"Webapp image file not found in storage for {entity_type} {entity.id}: {image_webapp_path}")
                            continue

                        # Get mime type
                        mime_type = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
                        if not mime_type.startswith('image/'):
                            logger.warning(f"File is not an image for {entity_type} {entity.id}: {image_webapp_path}")
                            continue

                        # Generate thumbnail using the webapp image field
                        from api.common.file import FileHandler
                        file_handler = FileHandler(file_data=file_data, entity_type=entity_type.lower(), mime_type=mime_type)
                        thumbnail_data = file_handler._generate_thumbnail('image_webapp')

                        if not thumbnail_data:
                            logger.warning(f"Failed to generate thumbnail for {entity_type} {entity.id}: {image_webapp_path}")
                            continue

                        # Generate thumbnail filename in same location as webapp image
                        name_part, ext = os.path.splitext(filename)
                        thumb_filename = f"{name_part}_thumb{ext}"

                        # Upload the new thumbnail to same container as webapp image
                        success = storage.upload_file(folder, thumb_filename, thumbnail_data)
                        if success:
                            success_count += 1
                            logger.info(f"Successfully regenerated thumbnail for {entity_type} {entity.id}: {folder}/{thumb_filename}")
                        else:
                            logger.warning(f"Failed to upload thumbnail for {entity_type} {entity.id}: {folder}/{thumb_filename}")

                    except Exception as e:
                        logger.error(f"Error processing webapp image for {entity_type} {entity.id}: {str(e)}")
                        continue

                return create_response("Webapp image thumbnails processed", data={
                    "client_id": client_id,
                    "entity_type": entity_type,
                    "page": index,
                    "processed_count": processed_count,
                    "success_count": success_count,
                    "total_count": count,
                    "has_next": count > index * size
                })

            except Exception as e:
                db_session.rollback()
                logger.error(f"Error processing webapp thumbnails: {str(e)}")
                return create_response(f"Error processing webapp thumbnails: {str(e)}", status=500)
            finally:
                db_session.close()

        except Exception as e:
            logger.exception("Unexpected error during webapp thumbnail regeneration", exc_info=True)
            return create_response(f"Thumbnail regeneration failed: {str(e)}", 500)


@api_file.route('signed_url/<path:folder>/<string:filename>', methods=['GET'])
class SignedUrl(Resource):
    @cache.cached(timeout=3300, key_prefix=lambda: f"{g.tenant_id}:signed_url:{request.path}")
    def get(self, folder, filename):
        try:
            signed_url = storage.get_signed_url(folder, filename)
            return create_response("Signed URL generated", data={"signed_url": signed_url})
        except Exception as e:
            logger.exception("Unexpected error generating signed URL", exc_info=True)
            return create_response(f"Failed to generate signed URL: {str(e)}", 500)
