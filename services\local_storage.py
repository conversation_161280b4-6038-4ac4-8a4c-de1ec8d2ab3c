import logging
import os
import shutil
import urllib.parse
from io import BytesIO

from api.common.storage import StorageProvider

logger = logging.getLogger(__name__)

class LocalStorage(StorageProvider):
    def __init__(self):
        self.base_path = os.environ.get('LOCAL_STORAGE_PATH', './file_upload')
        os.makedirs(self.base_path, exist_ok=True)
        logger.info(f'LocalStorage initialized with base path: {self.base_path}')

    def _ensure_directory(self, folder_name):
        """Ensure the directory exists for the given folder path"""
        full_path = os.path.join(self.base_path, folder_name)
        os.makedirs(full_path, exist_ok=True)
        return full_path

    def blob_exists(self, folder_name, blob_name):
        """Check if a file exists in local storage
        Args:
            folder_name: The folder path
            blob_name: The file name
        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            file_path = os.path.join(self.base_path, folder_name, blob_name)
            return os.path.exists(file_path)
        except Exception as e:
            logger.error(f'Error checking file existence: {str(e)}', exc_info=True)
            return False

    def upload_blob(self, folder_name, file_name, data):
        """Save file to local storage"""
        try:
            folder_path = self._ensure_directory(folder_name)
            file_path = os.path.join(folder_path, file_name)
            
            data.seek(0)
            with open(file_path, 'wb') as f:
                f.write(data.read())
            
            logger.info(f'File saved locally: {file_path}')
            return file_name
        except Exception as e:
            logger.error(f'Error saving file locally: {str(e)}', exc_info=True)
            return None

    def download_blob(self, folder_name, file_name):
        """Read file from local storage"""
        try:
            file_path = os.path.join(self.base_path, folder_name, file_name)
            if not os.path.exists(file_path):
                logger.warning(f'File not found: {file_path}')
                return None

            with open(file_path, 'rb') as f:
                data = BytesIO(f.read())
            return data
        except Exception as e:
            logger.error(f'Error reading file locally: {str(e)}', exc_info=True)
            return None

    def delete_blob(self, folder_name, file_name):
        """Delete file from local storage"""
        try:
            file_path = os.path.join(self.base_path, folder_name, file_name)
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f'File deleted: {file_path}')
                return True
            return False
        except Exception as e:
            logger.error(f'Error deleting file locally: {str(e)}', exc_info=True)
            return None

    def list_blobs(self, folder_name):
        """List all files in a folder"""
        try:
            folder_path = os.path.join(self.base_path, folder_name)
            if not os.path.exists(folder_path):
                return []
            
            files = []
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    stat = os.stat(file_path)
                    files.append({
                        'name': filename,
                        'last_modified': stat.st_mtime,
                        'size': stat.st_size
                    })
            return files
        except Exception as e:
            logger.error(f'Error listing files locally: {str(e)}', exc_info=True)
            return []

    def move_blob(self, source_container_name, source_blob_name, destination_container_name, destination_blob_name):
        """Move a file from one location to another in local storage
        Args:
            source_container_name (str): Source container name
            source_blob_name (str): Source file path
            destination_container_name (str): Destination container name
            destination_blob_name (str): Destination file path
        Returns:
            str: Destination file path if successful, None otherwise
        """
        try:
            source_path = os.path.join(self.base_path, source_container_name, source_blob_name)
            destination_dir = os.path.join(self.base_path, destination_container_name)
            destination_path = os.path.join(destination_dir, destination_blob_name)
            if not os.path.exists(source_path):
                logger.error(f'Source file does not exist: {source_path}')
                return None
            os.makedirs(os.path.dirname(destination_path), exist_ok=True)
            shutil.move(source_path, destination_path)
            return destination_blob_name
        except Exception as e:
            logger.error(f'Error moving file locally: {str(e)}', exc_info=True)
            return None
            
    def copy_blob(self, source_container_name, source_blob_name, destination_container_name, destination_blob_name):
        """Copy a file from one location to another in local storage
        Args:
            source_container_name (str): Source container name
            source_blob_name (str): Source file path
            destination_container_name (str): Destination container name
            destination_blob_name (str): Destination file path
        Returns:
            str: Destination file path if successful, None otherwise
        """
        try:
            source_path = os.path.join(self.base_path, source_container_name, source_blob_name)
            destination_dir = os.path.join(self.base_path, destination_container_name)
            destination_path = os.path.join(destination_dir, destination_blob_name)
            if not os.path.exists(source_path):
                logger.error(f'Source file does not exist: {source_path}')
                return None
            os.makedirs(os.path.dirname(destination_path), exist_ok=True)
            shutil.copy2(source_path, destination_path)
            return destination_blob_name
            
        except Exception as e:
            logger.error(f'Error copying file locally: {str(e)}', exc_info=True)
            return None
            
    def get_signed_url(self, folder_name, blob_name, expiry_minutes=60):
        """Generate a URL for a local file.
        
        For local storage, this generates a local URL path. In a real application,
        this would usually be a route that serves the file with proper authentication.
        
        Args:
            folder_name (str): Folder path
            blob_name (str): File name
            expiry_minutes (int): Expiry time in minutes (not used for local storage)
            
        Returns:
            str: URL path to the file
        """
        try:
            file_path = os.path.join(self.base_path, folder_name, blob_name)
            if not os.path.exists(file_path):
                logger.warning(f'File not found for URL: {file_path}')
                return None
            relative_path = os.path.join(folder_name, blob_name)
            safe_path = urllib.parse.quote(relative_path)
            return f'/files/{safe_path}'    
            
        except Exception as e:
            logger.error(f'Error generating URL for local file: {str(e)}', exc_info=True)
            return None
