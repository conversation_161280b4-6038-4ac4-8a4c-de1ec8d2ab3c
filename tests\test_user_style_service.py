import unittest
import json
import datetime
from unittest.mock import patch, MagicMock

from services.user_style_service import update_user_style_on_quest_completion


class TestUserStyleService(unittest.TestCase):
    """Test cases for user style service functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.quest_id = "test-quest-123"
        self.user_id = "test-user-456"
        self.style_mapping_id = "style-mapping-789"
        self.node_id_1 = "node-1"
        self.node_id_2 = "node-2"
        
        # Mock g.db_session
        self.mock_db_session = MagicMock()
        
    @patch('services.user_style_service.g')
    @patch('services.user_style_service.logging')
    def test_update_user_style_no_mapping(self, mock_logging, mock_g):
        """Test when no style mapping exists for the quest"""
        mock_g.db_session = self.mock_db_session
        mock_g.db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        result = update_user_style_on_quest_completion(self.quest_id, self.user_id)
        
        self.assertIsNone(result)
        mock_logging.debug.assert_called_once()
        
    @patch('services.user_style_service.g')
    @patch('services.user_style_service.logging')
    def test_update_user_style_empty_mapping_configuration(self, mock_logging, mock_g):
        """Test when mapping configuration is empty"""
        mock_g.db_session = self.mock_db_session
        
        # Mock style mapping with empty configuration
        mock_style_mapping = MagicMock()
        mock_style_mapping.id = self.style_mapping_id
        mock_style_mapping.mapping_configuration = None
        mock_g.db_session.query.return_value.filter_by.return_value.first.return_value = mock_style_mapping
        
        result = update_user_style_on_quest_completion(self.quest_id, self.user_id)
        
        self.assertIsNone(result)
        mock_logging.warning.assert_called_once()
        
    @patch('services.user_style_service.g')
    @patch('services.user_style_service.logging')
    def test_update_user_style_invalid_json_mapping(self, mock_logging, mock_g):
        """Test when mapping configuration contains invalid JSON"""
        mock_g.db_session = self.mock_db_session
        
        # Mock style mapping with invalid JSON
        mock_style_mapping = MagicMock()
        mock_style_mapping.id = self.style_mapping_id
        mock_style_mapping.mapping_configuration = "invalid json"
        mock_g.db_session.query.return_value.filter_by.return_value.first.return_value = mock_style_mapping
        
        result = update_user_style_on_quest_completion(self.quest_id, self.user_id)
        
        self.assertIsNone(result)
        mock_logging.error.assert_called_once()
        
    @patch('services.user_style_service.g')
    @patch('services.user_style_service.logging')
    @patch('services.user_style_service.UserStyle')
    def test_update_user_style_create_new_record(self, mock_user_style_class, mock_logging, mock_g):
        """Test creating a new user style record"""
        mock_g.db_session = self.mock_db_session
        
        # Mock style mapping
        mock_style_mapping = MagicMock()
        mock_style_mapping.id = self.style_mapping_id
        mock_style_mapping.style_name = "Test Style"
        mock_style_mapping.mapping_configuration = [
            {"node_id": self.node_id_1, "value": "value1"},
            {"node_id": self.node_id_2, "value": "value2"}
        ]
        
        # Mock user node history - latest node
        mock_node_history = MagicMock()
        mock_node_history.node_id = self.node_id_2
        mock_node_history.date_completed = datetime.datetime.utcnow()
        
        # Set up query chain mocks
        mock_style_mapping_query = MagicMock()
        mock_style_mapping_query.filter_by.return_value.first.return_value = mock_style_mapping
        
        mock_node_history_query = MagicMock()
        mock_node_history_query.filter.return_value.order_by.return_value.first.return_value = mock_node_history
        
        mock_existing_style_query = MagicMock()
        mock_existing_style_query.filter_by.return_value.first.return_value = None
        
        # Configure the query method to return different mocks based on the model
        def query_side_effect(model):
            if model.__name__ == 'UserStyleMapping':
                return mock_style_mapping_query
            elif model.__name__ == 'UserNodeHistory':
                return mock_node_history_query
            elif model.__name__ == 'UserStyle':
                return mock_existing_style_query
            return MagicMock()
        
        mock_g.db_session.query.side_effect = query_side_effect
        
        result = update_user_style_on_quest_completion(self.quest_id, self.user_id)
        
        # Verify the result
        self.assertIsNotNone(result)
        self.assertEqual(result["action"], "created")
        self.assertEqual(result["style_id"], self.style_mapping_id)
        self.assertEqual(result["style_name"], "Test Style")
        self.assertEqual(result["style_value"], "value2")
        self.assertEqual(result["latest_node_id"], self.node_id_2)
        self.assertEqual(result["quest_id"], self.quest_id)
        
        # Verify that a new UserStyle was created and added
        mock_user_style_class.assert_called_once()
        mock_g.db_session.add.assert_called_once()
        mock_g.db_session.commit.assert_called_once()
        
    @patch('services.user_style_service.g')
    @patch('services.user_style_service.logging')
    def test_update_user_style_update_existing_record(self, mock_logging, mock_g):
        """Test updating an existing user style record"""
        mock_g.db_session = self.mock_db_session
        
        # Mock style mapping
        mock_style_mapping = MagicMock()
        mock_style_mapping.id = self.style_mapping_id
        mock_style_mapping.style_name = "Test Style"
        mock_style_mapping.mapping_configuration = [
            {"node_id": self.node_id_1, "value": "value1"},
            {"node_id": self.node_id_2, "value": "value2"}
        ]
        
        # Mock user node history - latest node
        mock_node_history = MagicMock()
        mock_node_history.node_id = self.node_id_1
        mock_node_history.date_completed = datetime.datetime.utcnow()
        
        # Mock existing user style record
        mock_existing_style = MagicMock()
        mock_existing_style.style_value = "old_value"
        
        # Set up query chain mocks
        mock_style_mapping_query = MagicMock()
        mock_style_mapping_query.filter_by.return_value.first.return_value = mock_style_mapping
        
        mock_node_history_query = MagicMock()
        mock_node_history_query.filter.return_value.order_by.return_value.first.return_value = mock_node_history
        
        mock_existing_style_query = MagicMock()
        mock_existing_style_query.filter_by.return_value.first.return_value = mock_existing_style
        
        # Configure the query method to return different mocks based on the model
        def query_side_effect(model):
            if model.__name__ == 'UserStyleMapping':
                return mock_style_mapping_query
            elif model.__name__ == 'UserNodeHistory':
                return mock_node_history_query
            elif model.__name__ == 'UserStyle':
                return mock_existing_style_query
            return MagicMock()
        
        mock_g.db_session.query.side_effect = query_side_effect
        
        result = update_user_style_on_quest_completion(self.quest_id, self.user_id)
        
        # Verify the result
        self.assertIsNotNone(result)
        self.assertEqual(result["action"], "updated")
        self.assertEqual(result["style_id"], self.style_mapping_id)
        self.assertEqual(result["style_name"], "Test Style")
        self.assertEqual(result["style_value"], "value1")
        self.assertEqual(result["latest_node_id"], self.node_id_1)
        self.assertEqual(result["quest_id"], self.quest_id)
        
        # Verify that the existing style was updated
        self.assertEqual(mock_existing_style.style_value, "value1")
        mock_g.db_session.commit.assert_called_once()
        
    @patch('services.user_style_service.g')
    @patch('services.user_style_service.logging')
    def test_update_user_style_no_matching_nodes(self, mock_logging, mock_g):
        """Test when user has not completed any nodes from mapping configuration"""
        mock_g.db_session = self.mock_db_session
        
        # Mock style mapping
        mock_style_mapping = MagicMock()
        mock_style_mapping.id = self.style_mapping_id
        mock_style_mapping.mapping_configuration = [
            {"node_id": self.node_id_1, "value": "value1"},
            {"node_id": self.node_id_2, "value": "value2"}
        ]
        
        # Set up query chain mocks
        mock_style_mapping_query = MagicMock()
        mock_style_mapping_query.filter_by.return_value.first.return_value = mock_style_mapping
        
        mock_node_history_query = MagicMock()
        mock_node_history_query.filter.return_value.order_by.return_value.first.return_value = None
        
        # Configure the query method to return different mocks based on the model
        def query_side_effect(model):
            if model.__name__ == 'UserStyleMapping':
                return mock_style_mapping_query
            elif model.__name__ == 'UserNodeHistory':
                return mock_node_history_query
            return MagicMock()
        
        mock_g.db_session.query.side_effect = query_side_effect
        
        result = update_user_style_on_quest_completion(self.quest_id, self.user_id)
        
        self.assertIsNone(result)
        mock_logging.debug.assert_called()
        

if __name__ == '__main__':
    unittest.main()