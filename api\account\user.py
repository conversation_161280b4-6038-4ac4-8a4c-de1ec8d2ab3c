from flask import g
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from clientmodels import USER_TENANT, User, Admin, MasterUser, UserAssignment, Client

account_user_api = Namespace('api_account_user', description='Account User related operations')


## get user client assignments
@account_user_api.doc(security='bearer')
@account_user_api.route('/my_clients')
class MyClients(Resource):
    def get(self):
        ## get current user profile
        user = g.db_session.query(User).filter_by(id=g.user_id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        
        client_ids = []
        ## get user master account from user assignment
        user_assignments = g.db_session.query(UserAssignment).filter_by(user_id=user.id).first()
        if user_assignments:
            master_user = g.db_session.query(MasterUser).filter_by(id=user_assignments.master_user_id).first()
            if master_user:
                master_user_assignments = g.db_session.query(UserAssignment).filter_by(master_user_id=master_user.id).all()
                client_ids = [x.client_id for x in master_user_assignments]

        ## get client information
        data = []
        for client_id in client_ids:
            client = g.db_session.query(Client).filter_by(id=client_id).first()
            if client:
                data.append(client.to_dict(["id", "name", "image"]))

        ## check user admin account
        admin = g.db_session.query(Admin).filter_by(user_id=user.id).first()
        if admin:
            if admin.role == "super_admin":
                clients = g.db_session.query(Client).all()
                client_ids = [x.id for x in clients]
                data = [x.to_dict(["id", "name", "image"]) for x in clients]

        
        if admin:
            global_client = g.db_session.query(Client).filter_by(id_key=USER_TENANT).first()
            if global_client and global_client.id not in client_ids:
                client_data = global_client.to_dict(["id", "name", "image"])
                data.append(client_data)

        return create_response("User clients retrieved successfully", data=data)