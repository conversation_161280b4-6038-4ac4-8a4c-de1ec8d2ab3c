[{"name": "Onboarding 03 - Welcome", "id": "onboarding_03", "node": {"branches": [], "branches_from_ids": [], "button_text": "Get started", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_04", "node_id": "onboarding_03", "node_type": "onboarding_introduction", "points": 0, "quest": {"character_id": "xappy", "actions": ["Happiness"], "text": "<b>Welcome to Xapa!</b>\nWe're glad you're here and looking to develop yourself personally and professionally! Let's start by getting you onboarded with some important information and options!", "audio_en": "onboarding_node1.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 0, "min_selection": 0, "options": []}}, "field_name": "", "api": ""}, {"name": "Onboarding 04 - Job level", "id": "onboarding_04", "node": {"branches": [], "branches_from_ids": [], "button_text": "Continue", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_05", "node_id": "onboarding_04", "node_type": "onboarding_single_answer", "points": 0, "quest": {"character_id": "xappy", "actions": ["Curiosity"], "text": "Xapa has programs tailored to every <b>stage of your career</b>. Where are you right now??", "audio_en": "onboarding_node2.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 1, "min_selection": 1, "options": [{"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_01", "is_correct": false, "label": "C-Level", "next_node_id": "", "points": 0, "position": "", "value": "C-Level"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_02", "is_correct": false, "label": "VP-Level", "next_node_id": "", "points": 0, "position": "", "value": "VP-Level"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_03", "is_correct": false, "label": "Director-Level", "next_node_id": "", "points": 0, "position": "", "value": "Director-Level"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_04", "is_correct": false, "label": "Manager-Level", "next_node_id": "", "points": 0, "position": "", "value": "Manager-Level"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_05", "is_correct": true, "label": "Individual Contributor", "next_node_id": "", "points": 0, "position": "", "value": "Individual Contributor"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_06", "is_correct": false, "label": "Student", "next_node_id": "", "points": 0, "position": "", "value": "Student"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_07", "is_correct": false, "label": "Self-Employed", "next_node_id": "", "points": 0, "position": "", "value": "Self-Employed"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_04_08", "is_correct": false, "label": "Something else", "next_node_id": "", "points": 0, "position": "", "value": "Something else"}]}}, "field_name": "settings['job_level']", "api": "/user/me"}, {"name": "Onboarding 05 - Type of learner", "id": "onboarding_05", "node": {"branches": [], "branches_from_ids": [], "button_text": "Continue", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_06", "node_id": "onboarding_05", "node_type": "onboarding_single_answer", "points": 0, "quest": {"character_id": "xappy", "actions": ["Curiosity"], "text": "<b>How often</b> would you like <b>to engage</b> with us?", "audio_en": "onboarding_node3.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 1, "min_selection": 1, "options": [{"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_05_01", "is_correct": false, "label": "Multiple times a day", "next_node_id": "", "points": 0, "position": "", "value": "Multiple times a day"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_05_02", "is_correct": false, "label": "Every day", "next_node_id": "", "points": 0, "position": "", "value": "Every day"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_05_03", "is_correct": true, "label": "Every 2-3 days", "next_node_id": "", "points": 0, "position": "", "value": "Every 2-3 days"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_05_04", "is_correct": false, "label": "Once a week", "next_node_id": "", "points": 0, "position": "", "value": "Once a week"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_05_05", "is_correct": false, "label": "Every other week", "next_node_id": "", "points": 0, "position": "", "value": "Every other week"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_05_06", "is_correct": false, "label": "Once a month", "next_node_id": "", "points": 0, "position": "", "value": "Once a month"}]}}, "field_name": "settings['type_of_learner']", "api": "/user/me"}, {"name": "Onboarding 06 - Learning Goal", "id": "onboarding_06", "node": {"branches": [], "branches_from_ids": [], "button_text": "Continue", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_07", "node_id": "onboarding_06", "node_type": "onboarding_single_answer", "points": 0, "quest": {"character_id": "xappy", "actions": ["Curiosity"], "text": "Let's set a learning goal. How much time would you like to spend growing per day?", "audio_en": "onboarding_node4.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 1, "min_selection": 1, "options": [{"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_06_01", "is_correct": false, "label": "30 min", "next_node_id": "", "points": 0, "position": "", "value": "30"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_06_02", "is_correct": false, "label": "15 min", "next_node_id": "", "points": 0, "position": "", "value": "15"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_06_03", "is_correct": true, "label": "10 min", "next_node_id": "", "points": 0, "position": "", "value": "10"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_06_04", "is_correct": false, "label": "5 min", "next_node_id": "", "points": 0, "position": "", "value": "5"}]}}, "field_name": "settings['learning_goal']", "api": "/user/me"}, {"name": "Onboarding 07 - How do you feel?", "id": "onboarding_07", "node": {"branches": [], "branches_from_ids": [], "button_text": "Continue", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_08", "node_id": "onboarding_07", "node_type": "onboarding_daily_check", "points": 0, "quest": {"character_id": "xappy", "actions": ["Curiosity"], "text": "So, tell me, <b>how are you doing today?</b>", "audio_en": "onboarding_node5.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 1, "min_selection": 1, "options": [{"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_01", "is_correct": false, "label": "Happy", "next_node_id": "", "points": 0, "position": "", "value": "Happy"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_02", "is_correct": false, "label": "Valued", "next_node_id": "", "points": 0, "position": "", "value": "Valued"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_03", "is_correct": false, "label": "Excited", "next_node_id": "", "points": 0, "position": "", "value": "Excited"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_04", "is_correct": false, "label": "Confident", "next_node_id": "", "points": 0, "position": "", "value": "Confident"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_05", "is_correct": false, "label": "<PERSON><PERSON>", "next_node_id": "", "points": 0, "position": "", "value": "<PERSON><PERSON>"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_06", "is_correct": false, "label": "Okay", "next_node_id": "", "points": 0, "position": "", "value": "Okay"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_07", "is_correct": false, "label": "Under-Appreciated", "next_node_id": "", "points": 0, "position": "", "value": "Under-Appreciated"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_08", "is_correct": false, "label": "Tired", "next_node_id": "", "points": 0, "position": "", "value": "Tired"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_09", "is_correct": false, "label": "Stressed", "next_node_id": "", "points": 0, "position": "", "value": "Stressed"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_10", "is_correct": false, "label": "Frustrated", "next_node_id": "", "points": 0, "position": "", "value": "Frustrated"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_11", "is_correct": false, "label": "Anxious", "next_node_id": "", "points": 0, "position": "", "value": "Anxious"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_07_12", "is_correct": false, "label": "Angry", "next_node_id": "", "points": 0, "position": "", "value": "Angry"}]}}, "field_name": "status", "api": "/me/status"}, {"name": "Onboarding 08 - Streaks", "id": "onboarding_08", "node": {"branches": [], "branches_from_ids": [], "button_text": "Continue", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_09", "node_id": "onboarding_08", "node_type": "onboarding_streak", "points": 0, "quest": {"character_id": "xappy", "actions": ["Pride"], "text": "You've started your <b>first streak by checking in</b> for today! Keep coming back and learning each day to keep your streak going and earn achievements and other special bonuses!", "audio_en": "onboarding_node6.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 0, "min_selection": 0, "options": []}}, "field_name": "", "api": "/me/streak"}, {"name": "Onboarding 09 - streak goal", "id": "onboarding_09", "node": {"branches": [], "branches_from_ids": [], "button_text": "Continue", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_10", "node_id": "onboarding_09", "node_type": "onboarding_single_answer", "points": 0, "quest": {"character_id": "xappy", "actions": ["<PERSON><PERSON>"], "text": "Let's <b>set a goal for your first streak</b>! Meeting your streak goals earns achievements and other great items."}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 1, "min_selection": 1, "options": [{"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_09_01", "is_correct": true, "label": "90 days", "next_node_id": "", "points": 0, "position": "", "value": "90"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_09_02", "is_correct": false, "label": "60 days", "next_node_id": "", "points": 0, "position": "", "value": "60"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_09_03", "is_correct": false, "label": "30 days", "next_node_id": "", "points": 0, "position": "", "value": "30"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_09_04", "is_correct": false, "label": "14 days", "next_node_id": "", "points": 0, "position": "", "value": "14"}, {"feedback": {"correct": "", "incorrect": ""}, "id": "onboarding_09_05", "is_correct": false, "label": "7 days", "next_node_id": "", "points": 0, "position": "", "value": "7"}]}}, "field_name": "settings['streak_goal']", "api": "/user/me"}, {"name": "Onboarding 10 - Notifications", "id": "onboarding_10", "node": {"branches": [], "branches_from_ids": [], "button_text": "Continue", "character_ids": [], "is_quiz": false, "next_node_id": "onboarding_11", "node_id": "onboarding_10", "node_type": "onboarding_notification", "points": 0, "quest": {"character_id": "xappy", "actions": [], "text": "To help you hit that goal, I can send you <b>notifications</b> about new content, streaks, reminders, and messages. Select '<b>Allow</b>' or 'Don't Allow' in the permissions dialog after you click 'Continue' to specify if you want to receive notifications. No worries if you change your mind later—you’ll be able to adjust this anytime in your <b>profile settings</b>.", "audio_en": "onboarding_node8.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 0, "min_selection": 0, "options": []}}, "field_name": "", "api": ""}, {"name": "Onboarding 11 - <PERSON><PERSON><PERSON>", "id": "onboarding_11", "node": {"branches": [], "branches_from_ids": [], "button_text": "Done!", "character_ids": [], "is_quiz": false, "next_node_id": "", "node_id": "onboarding_11", "node_type": "onboarding_introduction", "points": 0, "quest": {"character_id": "xappy", "actions": ["Happiness", "Smile", "Peaceful"], "text": "<b>Okay, we're all set!</b> \n\nReady to dive in? \nLet's go!", "audio_en": "onboarding_node9.mp3"}, "sub_title": "", "title": "", "transcripts": [], "value_list": {"max_selection": 0, "min_selection": 0, "options": []}}, "field_name": "", "api": ""}]