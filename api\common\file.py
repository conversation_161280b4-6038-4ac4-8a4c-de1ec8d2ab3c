import datetime
import logging
import mimetypes
import os
from typing import Tuple, Optional, Dict
import uuid
from io import BytesIO

from PIL import Image, ImageFilter

from app import storage

logger = logging.getLogger(__name__)

class FileHandler:
    MAX_FILE_SIZE = 40 * 1024 * 1024  # 40MB in bytes
    
    # Thumbnail size configurations
    THUMBNAIL_SIZES = {
        'client': [240, 240],
        'user': [240, 240],
        'xircle': [240, 240],
        'feed': [0.5, 0.5],
        'feedseniority': [0.5, 0.5],
        'program': [720, 400],
        'xperience': [720, 400],
        'quest': [720, 400],
        'badge': [240, 240],
        'package': [720, 400],
        'banner': [1080, 1080],
        'store': [240, 240],
        'asset': [240, 240],
        'default': [1, 1],
    }
    
    # Supported MIME types and their extensions
    MIME_TO_EXTENSION = {
        # Images
        'image/jpeg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/bmp': '.bmp',
        'image/webp': '.webp',
        'image/svg+xml': '.svg',
        # Documents
        'text/plain': '.txt',
        'application/pdf': '.pdf',
        'application/msword': '.doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'application/vnd.ms-excel': '.xls',
        'text/csv': '.csv',
        'text/x-csv': '.csv',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
        'application/vnd.ms-powerpoint': '.ppt',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
        # Audio
        'audio/mpeg': '.mp3',
        'audio/ogg': '.ogg',
        'audio/wav': '.wav',
        'audio/aac': '.aac',
        # Video
        'video/mp4': '.mp4',
        'video/ogg': '.ogg',
        'video/webm': '.webm',
        'video/hls': '.m3u8',
        'video/avi': '.avi',
        'video/mov': '.mov',
        # Add riv file
        'application/octet-stream': '.riv'
    }

    def __init__(self, file_data, entity_type='unknown', mime_type=None):   
        self.file_data = file_data
        self.entity_type = entity_type
        self.mime_type = mime_type

    def _generate_filename(self, width = None, height = None):
        """Generate a unique and structured filename"""
        try:
            date_str = datetime.datetime.utcnow().strftime('%Y%m%d')
            unique_id = str(uuid.uuid4())[:8]
            extension = self.MIME_TO_EXTENSION.get(self.mime_type, '')
            if width and height:
                filename = f"{date_str}_{unique_id}_{width}x{height}{extension}"
            else:
                filename = f"{date_str}_{unique_id}{extension}"
            filename = filename.lower()  # Convert to lowercase for consistency
            return filename
            
        except Exception as e:
            logger.error("Error generating filename", exc_info=True)
            return None

    def _generate_thumbname(self, filename):
        """Generate thumbnail filename by appending '_thumb' to the base name."""
        try:
            name, ext = os.path.splitext(filename)
            thumb_filename = f"{name}_thumb{ext}"
            return thumb_filename
            
        except Exception as e:
            logger.error("Error generating thumbnail filename", exc_info=True)
            return None

    def _optimize_image(self) -> BytesIO:
        """
        Optimize image using lossless compression techniques based on image format.
        Returns optimized image data as BytesIO object.
        Supports lossless compression for PNG, JPEG, WebP formats.
        """
        # Initialize output buffer
        output = BytesIO()
        
        try:
            # Early return if not an image
            if not self.mime_type or not self.mime_type.startswith('image/'):
                self.file_data.seek(0)
                output.write(self.file_data.read())
                self.file_data.seek(0)
                output.seek(0)
                return output
                
            # Extract format from mime type
            format_lower = self.mime_type.split('/')[1].lower()
            
            # Only process supported formats
            if format_lower not in ('png', 'jpeg', 'jpg', 'webp'):
                self.file_data.seek(0)
                output.write(self.file_data.read())
                self.file_data.seek(0)
                output.seek(0)
                return output
            
            # Process the image
            with Image.open(self.file_data) as image:
                # Handle RGBA mode for JPEG format (JPEG doesn't support alpha channel)
                img = image.copy()
                if format_lower in ('jpeg', 'jpg') and img.mode == 'RGBA':
                    # Convert RGBA to RGB by compositing on a white background
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[3])  # 3 is the alpha channel
                    img = background
                
                # Format-specific optimization
                save_params = {
                    'png': {'format': 'PNG', 'optimize': True, 'quality': 100},
                    'jpeg': {'format': 'JPEG', 'optimize': True, 'quality': 90, 'subsampling': 0},
                    'jpg': {'format': 'JPEG', 'optimize': True, 'quality': 90, 'subsampling': 0},
                    'webp': {'format': 'WebP', 'lossless': True, 'quality': 100}
                }
                
                # Get parameters for this format
                params = save_params.get(format_lower)
                img.save(output, **params)
            
            output.seek(0)
            return output

        except Exception as e:
            logger.error(f"Error during image optimization: {str(e)}")
            # Reset file pointer and return original data on error
            self.file_data.seek(0)
            return self.file_data
    
    def _generate_thumbnail(self, image_field: str = 'image') -> Optional[BytesIO]:
        """Generate a high-quality thumbnail for an image while preserving the original.
        
        Returns:
            Optional[BytesIO]: BytesIO object containing the thumbnail image data, or None if generation failed
        """
        # Early returns for conditions where we can't generate a thumbnail
        if self.entity_type == 'unknown':
            return None
            
        if not self.mime_type or not self.mime_type.startswith('image/'):
            return None
        
        format_lower = self.mime_type.split('/')[1].lower()
        if format_lower not in ('png', 'jpeg', 'jpg', 'webp'):
            return None
        
        try:
            # Get thumbnail size configuration based on content type
            if image_field == 'image_webapp':
                width, height = self.THUMBNAIL_SIZES['default']
            else:
                width, height = self.THUMBNAIL_SIZES.get(self.entity_type, self.THUMBNAIL_SIZES['default'])
            
            # Open and process the image
            with Image.open(self.file_data) as image:
                # Calculate dimensions
                img_width = width if width > 1 else int(image.width * width)
                img_height = height if height > 1 else int(image.height * height)
                
                # Use high-quality Lanczos resampling instead of basic thumbnail
                thumb_img = image.resize((img_width, img_height), Image.Resampling.LANCZOS)
                
                # Apply light sharpening for small thumbnails to improve clarity
                if img_width <= 300 or img_height <= 300:
                    try:
                        sharpening_filter = ImageFilter.UnsharpMask(radius=0.5, percent=120, threshold=3)
                        thumb_img = thumb_img.filter(sharpening_filter)
                    except Exception:
                        pass  # Continue without sharpening if it fails
                
                # Handle RGBA mode for JPEG format (JPEG doesn't support alpha channel)
                if format_lower in ('jpeg', 'jpg') and thumb_img.mode == 'RGBA':
                    # Convert RGBA to RGB by compositing on a white background
                    background = Image.new('RGB', thumb_img.size, (255, 255, 255))
                    background.paste(thumb_img, mask=thumb_img.split()[3])  # 3 is the alpha channel
                    thumb_img = background
                
                # Save to BytesIO buffer with better quality settings
                thumb_bytes = BytesIO()
                
                # Use better compression settings
                if format_lower in ('jpeg', 'jpg'):
                    thumb_img.save(thumb_bytes, format='JPEG', quality=95, optimize=True)
                elif format_lower == 'png':
                    thumb_img.save(thumb_bytes, format='PNG', optimize=True)
                elif format_lower == 'webp':
                    thumb_img.save(thumb_bytes, format='WebP', quality=95, optimize=True)
                else:
                    thumb_img.save(thumb_bytes, format=format_lower.upper())
                
                thumb_bytes.seek(0)
                return thumb_bytes

        except Exception as e:
            logger.error(f"Error generating thumbnail: {str(e)}")
            return None

    def _get_width_height(self):
        """Get image width height for filename generation"""
        with Image.open(self.file_data) as image:
            width = image.width
            height = image.height
            self.file_data.seek(0)
            return width, height

    def _validate(self):
        """Validate file size and type"""
        try:
            if not self.file_data:
                logger.error("No file data provided for validation")
                return False, "api.file.no_file"

            # Use more efficient size checking
            size = self.file_data.content_length if hasattr(self.file_data, 'content_length') else None
            if size is None:
                self.file_data.seek(0, os.SEEK_END)
                size = self.file_data.tell()
                self.file_data.seek(0)

            if size > self.MAX_FILE_SIZE:
                return False, f"File size exceeds {self.MAX_FILE_SIZE/1024/1024}MB limit"

            # Cache mimetype check
            if self.mime_type not in self.MIME_TO_EXTENSION:
                return False, f"Unsupported file type: {self.mime_type}"

            return True, None
        except Exception as e:
            logger.error("Error during file validation", exc_info=True)
            return False, f"Validation failed: {str(e)}"


class FileService:
    """Service for handling file operations with proper organization and optimization"""
    
    @staticmethod
    def process_upload(file_data, target_folder: str = 'temp') -> Tuple[bool, str]:
        """Process file upload with optimization and thumbnail generation
        
        Args:
            file_data: The file data from the request
            target_folder: Target folder for the upload (default: 'temp')
            
        Returns:
            Tuple[bool, str]: Success flag and result (path or error message)
        """
        optimize_data = None
        try:
            # Create a handler for validation and processing
            handler = FileHandler(file_data=file_data, mime_type=file_data.mimetype)
            
            # Validate file
            is_valid, error_message = handler._validate()
            if not is_valid:
                logger.error(f"File validation failed: {error_message}")
                return False, error_message
            
            # Apply lossless compression for images
            if file_data.mimetype.startswith('image/'):
                # Generate filename and prepare file data
                width, height = handler._get_width_height()
                filename = handler._generate_filename(width, height)
                optimize_data = handler._optimize_image()
                new_file_name = storage.upload_file(target_folder, filename, optimize_data)
                
                if not new_file_name:
                    logger.error("File upload failed - Storage returned None")
                    return False, "api.file.upload_error"
            else:
                filename = handler._generate_filename()
                new_file_name = storage.upload_file(target_folder, filename, file_data)
                
                if not new_file_name:
                    logger.error("File upload failed - Storage returned None")
                    return False, "api.file.upload_error"

            final_path = f"{target_folder}/{new_file_name}"
            return True, final_path

        except Exception as e:
            logger.exception("FileService: Unexpected error during file upload")
            return False, f"Upload failed: {str(e)}"
        finally:
            # Safely close file objects if they exist
            if optimize_data and hasattr(optimize_data, 'close'):
                try:
                    optimize_data.close()
                except Exception:
                    pass
    
    @staticmethod
    def move_to_destination(temp_path: str, destination_folder: str, entity_id: str, image_field: str = 'image') -> Optional[str]:
        """Move a file from temp storage to its final destination folder
        
        Args:
            temp_path: Path to the temporary file
            destination_folder: Destination folder (e.g., 'feed', 'user', 'quest')
            entity_id: ID of the entity owning the file (user_id, feed_id, etc.)
            
        Returns:
            Optional[str]: New file path if successful, None otherwise
        """
        thumbnail_data = None
        try:
            if not temp_path or not temp_path.strip():
                return None
                
            # Extract filename from path
            temp_filepath = temp_path.split('/')[0]
            temp_filename = temp_path.split('/')[-1]
            
            # Generate destination path with proper structure
            destination_path = f"{destination_folder}/{entity_id}/{temp_filename}"

            
            # Move the main file
            success = storage.move_file(temp_path, destination_path)
            if not success:
                logger.error(f"Failed to move file from {temp_path} to {destination_path}")
                return None
                
            # Check if there's a thumbnail and move it too
            # Extract the base name and extension
            name, ext = os.path.splitext(temp_filename)
            thumb_filename = f"{name}_thumb{ext}"
            temp_thumb_path = temp_path.replace(temp_filename, thumb_filename)
            
            # If thumbnail exists, move it
            # If thumbnail doesn't exist, generate it
            if storage.file_exists(temp_filepath, thumb_filename):
                destination_thumb_path = f"{destination_folder}/{entity_id}/{thumb_filename}"
                storage.move_file(temp_thumb_path, destination_thumb_path)
            else:
                file_data = storage.download_file(f"{destination_folder}/{entity_id}", temp_filename)
                mime_type = mimetypes.guess_type(temp_filename)[0] or 'application/octet-stream'
                file_handler = FileHandler(file_data=file_data, entity_type=destination_folder, mime_type=mime_type)
                thumbnail_data = file_handler._generate_thumbnail(image_field)
                storage.upload_file(f"{destination_folder}/{entity_id}", thumb_filename, thumbnail_data)
            
            return destination_path
            
        except Exception as e:
            logger.exception(f"Error moving file from temp to destination: {str(e)}")
            return None
        
        finally:
            if thumbnail_data and hasattr(thumbnail_data, 'close'):
                try:
                    thumbnail_data.close()
                except Exception:
                    pass

    @staticmethod
    def process_entity_image(entity, temp_image_path: str, destination_folder: str, entity_id: str, image_field: str = 'image') -> bool:
        """Complete process to handle entity image from temp to final destination
        
        Args:
            entity: Entity object (feed, user, etc.) to update
            temp_image_path: Path to temporary image
            destination_folder: Destination folder name
            entity_id: ID of the entity
            
        Returns:
            bool: Success flag
        """
        # Skip if no image or unchanged
        if not temp_image_path or temp_image_path == getattr(entity, image_field, None):
            if temp_image_path is not None:
                setattr(entity, image_field, temp_image_path)
            return True
            
        # Move file to destination
        new_image_path = FileService.move_to_destination(
            temp_image_path, destination_folder, entity_id, image_field
        )
        
        if not new_image_path:
            return False
            
        # Update entity with new image path
        setattr(entity, image_field, new_image_path)
        
        return True
