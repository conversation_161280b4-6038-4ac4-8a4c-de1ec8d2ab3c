"""add user_agreement table

Revision ID: aa5ae284aba8
Revises: 3cd78cad4b42
Create Date: 2025-01-08 04:44:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'aa5ae284aba8'
down_revision = '3cd78cad4b42'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_agreement',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('date_created', sa.DateTime(), nullable=True),
    sa.Column('date_updated', sa.DateTime(), nullable=True),
    sa.Column('create_user', sa.String(length=36), nullable=True),
    sa.Column('update_user', sa.String(length=36), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('status', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=True),
    sa.Column('terms_type', sa.String(length=255), nullable=False),
    sa.Column('terms_version', sa.String(length=255), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_agreement')
    # ### end Alembic commands ###