"""
Demo script showing the new CMS Xperience List API response format
This demonstrates the three key changes made:
1. Sorting by last update (newest first)
2. Added "last_published_date" field 
3. Added "is_published_to_clients" field
"""

def demo_api_response():
    """
    Example of what the updated CMS Xperience List API response would look like
    """
    
    # Sample response showing the new fields
    sample_response = {
        "message": "success",
        "data": [
            {
                "id": "xperience-123",
                "name": "Leadership Excellence Program",
                "description": "Advanced leadership development xperience",
                "status": "Published",
                "date_created": "2024-01-15 09:00:00Z",
                "date_updated": "2024-01-20 14:30:00Z",  # ← Sorted by this field (newest first)
                
                # NEW FIELD 1: Last published date from content_publish_log table
                "last_published_date": "2024-01-20 14:35:00Z",
                
                # NEW FIELD 2: Published to clients status from published_xperience table
                "is_published_to_clients": True,  # ← True if latest revision has is_latest=True
                
                "create_user": {
                    "id": "user-456",
                    "first_name": "<PERSON>",
                    "last_name": "<PERSON><PERSON>",
                    "email": "<EMAIL>",
                    "image": "profile.jpg"
                },
                "update_user": {
                    "id": "user-789", 
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "email": "<EMAIL>",
                    "image": "jane.jpg"
                }
            },
            {
                "id": "xperience-456",
                "name": "Communication Skills Workshop",
                "description": "Improve your communication skills",
                "status": "Ready",
                "date_created": "2024-01-10 10:00:00Z",
                "date_updated": "2024-01-18 16:45:00Z",  # ← Older update, appears second
                
                # NEW FIELD 1: Never published, so null
                "last_published_date": None,
                
                # NEW FIELD 2: Not published to clients (still under testing)
                "is_published_to_clients": False,
                
                "create_user": {
                    "id": "user-321",
                    "first_name": "Alice",
                    "last_name": "Johnson", 
                    "email": "<EMAIL>",
                    "image": "alice.jpg"
                },
                "update_user": {
                    "id": "user-321",
                    "first_name": "Alice", 
                    "last_name": "Johnson",
                    "email": "<EMAIL>",
                    "image": "alice.jpg"
                }
            }
        ],
        "total": 2,
        "page": 1,
        "limit": 10
    }
    
    print("🎯 Updated CMS Xperience List API Response Demo")
    print("=" * 50)
    
    print("\n📋 Key Changes Implemented:")
    print("1. ✅ Sorting: Now sorted by 'date_updated' (newest first)")
    print("2. ✅ Added 'last_published_date' field from content_publish_log table")
    print("3. ✅ Added 'is_published_to_clients' field from published_xperience table")
    
    print(f"\n📊 Sample Response (showing {len(sample_response['data'])} items):")
    
    for i, item in enumerate(sample_response['data'], 1):
        print(f"\n📄 Item {i}: {item['name']}")
        print(f"   Last Updated: {item['date_updated']}")
        print(f"   Last Published: {item['last_published_date'] or 'Never published'}")
        print(f"   Published to Clients: {'✅ Yes' if item['is_published_to_clients'] else '❌ No (still testing)'}")
        print(f"   Status: {item['status']}")
    
    print(f"\n📈 Total Items: {sample_response['total']}")
    print(f"📄 Page: {sample_response['page']}")
    print(f"📑 Items per page: {sample_response['limit']}")
    
    return sample_response

if __name__ == "__main__":
    demo_api_response()