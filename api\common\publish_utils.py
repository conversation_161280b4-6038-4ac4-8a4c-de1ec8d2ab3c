import datetime
import json
import logging
from flask import g
from sqlalchemy import inspect

from app import storage
from app import app
from clientmodels import (
    Asset, Badge, Category, Character, Chest, ChestAssetAssociation, ClientPackageAssociation, ContentPublishLog, Facet, Feed, FeedSeniority, Package, PackageQuestAssociation, Program, ProgramBadgeAssociation, ProgramCategoryAssociation, ProgramDivider, ProgramFacetAssociation, ProgramQuestAssociation, ProgramTagAssociation,
    QuestBadgeAssociation, QuestCategoryAssociation, QuestFacetAssociation, QuestTagAssociation, Tag, UserChest, UserProgram, UserQuest,
    UserXperience, Xircle, User, Client,
    Quest, Node, NodeOption, NodeOptionMatching, NodeTranscript, NodeBranch, Xperience, XperienceBadgeAssociation,
    XperienceCategoryAssociation, XperienceFacetAssociation, XperienceQuestAssociation, XperienceTagAssociation,
    PublishedNode, PublishedNodeOption, PublishedNodeOptionMatching, PublishedNodeTranscript, PublishedNodeBranch,
    Published<PERSON><PERSON>ram, PublishedXperience, PublishedQuest,
    get_db_session
)


def publish_xapa_xircle(client_id=None): 
    """
    Publish Xapa to a client's database and create the xircle if it doesn't exist
    Returns (success, message)
    """
    # Main database session
    source_session = get_db_session()
    
    # Get the feed with all relationships
    xapa_xircle = source_session.query(Xircle).filter(
        Xircle.name.ilike('xapa'),
        Xircle.is_deleted == False
    ).first()

    if not client_id:
        # Get all client
        clients = g.db_session.query(Client).filter(Client.id_key != 'global').all()
    else:
        clients = g.db_session.query(Client).filter(Client.id_key == client_id).all()

    for client in clients:
        # Client database session
        target_session = get_db_session(client.id_key)
        try:
    
            # Get or create Xapa admin user
            admin_user = target_session.query(User).filter(
                User.id == '00000000-0000-0000-0000-000000000000'
            ).first()
            
            if not admin_user:
                admin_user = User()
                admin_user.id = '00000000-0000-0000-0000-000000000000'
                admin_user.first_name = 'Xapa'
                admin_user.email = 'admin@xapa'
                admin_user.is_active = False
                admin_user.is_deleted = False
                target_session.add(admin_user)
                target_session.flush()

            # Get or create Xapa xircle
            xircle = target_session.query(Xircle).filter(
                Xircle.name.ilike('xapa'),
                Xircle.is_deleted == False
            ).first()
            
            if not xircle:
                xircle = Xircle()
                xircle.name = 'Xapa'
                xircle.image = xapa_xircle.image
                xircle.description = xapa_xircle.description
                xircle.is_public = True
                xircle.include_leaderboard = True
                xircle.creator_id = admin_user.id
                target_session.add(xircle)
            else:
                xircle.name = 'Xapa'
                xircle.image = xapa_xircle.image
                xircle.description = xapa_xircle.description
    
            # Commit all changes
            target_session.commit()

        except Exception as e:
            target_session.rollback()
        finally:
            target_session.close()
            continue
    
    return True, f"Xapa successfully published to client database"


def publish_xapa_feed(feed_id, client_id=None): 
    """
    Publish a feed to a client's database and create the xircle if it doesn't exist
    Returns (success, message)
    """
    # Main database session
    source_session = get_db_session()
    
    # Get the feed with all relationships
    feed = source_session.query(Feed).filter(
        Feed.id == feed_id
    ).first()

    if not feed:
        return False, "Feed not found"

    if not client_id:
        # Get all client except global
        clients = source_session.query(Client).filter(Client.id_key != 'global').all()
    else:
        clients = source_session.query(Client).filter(Client.id_key == client_id).all()

    for client in clients:
        # Client database session
        target_session = get_db_session(client.id_key)
        try:

            # Get or create Xapa xircle
            xircle = target_session.query(Xircle).filter(
                Xircle.name.ilike('xapa'),
                Xircle.is_deleted == False
            ).first()

            existing_feed = target_session.query(Feed).filter(
                Feed.id == feed_id
            ).first()

            if existing_feed:
                existing_feed.user_id = feed.user_id
                existing_feed.xircle_id = xircle.id
                existing_feed.message = feed.message
                existing_feed.image = feed.image
                existing_feed.media = feed.media
                existing_feed.link = feed.link
                existing_feed.link_type = feed.link_type
                existing_feed.label = feed.label
                existing_feed.is_deleted = feed.is_deleted
                existing_feed.status = feed.status
                existing_feed.date_created = feed.date_created
            else:
                new_feed = Feed()
                new_feed.id = feed.id
                new_feed.user_id = feed.user_id
                new_feed.xircle_id = xircle.id
                new_feed.message = feed.message
                new_feed.image = feed.image
                new_feed.media = feed.media
                new_feed.link = feed.link
                new_feed.link_type = feed.link_type
                new_feed.label = feed.label
                new_feed.is_deleted = feed.is_deleted
                new_feed.status = feed.status
                new_feed.date_created = feed.date_created
                target_session.add(new_feed)

            # Commit all changes
            target_session.commit()

        except Exception as e:
            target_session.rollback()
        finally:
            target_session.close()
            continue

    return True, f"Feed successfully published to client database"


def public_all_xapa_feed(client_id=None):
    """
    Publish all Xapa feeds to a client's database and create the xircle if it doesn't exist
    Returns (success, message)
    """
    # Main database session
    source_session = get_db_session()

    if not client_id:
        # Get all client
        clients = g.db_session.query(Client).filter(Client.id_key != 'global').all()
    else:
        clients = g.db_session.query(Client).filter(Client.id_key == client_id).all()

    for client in clients:
        # Get all xapa feeds
        xapa_feeds = source_session.query(Feed).join(Xircle).filter(
            Xircle.name.ilike('xapa')
        ).all()
        
        for feed in xapa_feeds:
            publish_xapa_feed(feed.id, client.id_key)
        
    return True, f"Xapa successfully published all Xapa feeds to client database"





def release_xperience(xperience, is_latest=True):
    ## copy xperience data from original xperience table to publish xperience table

    ## Only update if is_latest is True
    if is_latest:
        # Set all previous published xperience for this xperience to is_latest = False
        g.client_db_session.query(PublishedXperience).filter(
            PublishedXperience.xperience_id == xperience.id,
            PublishedXperience.is_latest == True
        ).update({"is_latest": False})

    # Get the last revision number
    last_revision = g.client_db_session.query(PublishedXperience.revision).filter(
        PublishedXperience.xperience_id == xperience.id
    ).order_by(PublishedXperience.revision.desc()).first()

    last_revision = last_revision[0] if last_revision else 0
    
    # Create new published xperience
    new_xperience = PublishedXperience()

    ## copy xperience data
    for column in inspect(new_xperience).mapper.column_attrs:
        key = column.key
        if hasattr(xperience, key) and key != "id":
            setattr(new_xperience, key, getattr(xperience, key))

    if is_latest:
        new_xperience.is_latest = True
    else:
        new_xperience.is_latest = False

    new_xperience.revision = last_revision + 1
    new_xperience.xperience_id = xperience.id

    ## update quest_ids with current assoiciated quests
    ## Get all quests associated with the xperience

    quests = g.client_db_session.query(Quest).join(XperienceQuestAssociation).filter(
        XperienceQuestAssociation.xperience_id == xperience.id
    ).all()
    quest_ids = [quest.id for quest in quests]
    new_xperience.quest_ids = json.dumps(quest_ids) 

    ## save xperience
    g.client_db_session.add(new_xperience)
    g.client_db_session.commit()

    print(f"New xperience revision: {new_xperience.revision}")

    return True, f"Xperience {xperience.name} successfully released"



def release_program(program, is_latest=True):
    ## copy program data from original program table to publish program table
    ## Only update if is_latest is True

    if is_latest:
        # Set all previous published programs for this program to is_latest = False
        g.client_db_session.query(PublishedProgram).filter(
            PublishedProgram.program_id == program.id,
            PublishedProgram.is_latest == True
        ).update({"is_latest": False})

    # Get the last revision number
    last_revision = g.client_db_session.query(PublishedProgram.revision).filter(
        PublishedProgram.program_id == program.id
    ).order_by(PublishedProgram.revision.desc()).first()

    last_revision = last_revision[0] if last_revision else 0
    # Create new published program
    new_program = PublishedProgram()
    ## copy program data
    for column in inspect(new_program).mapper.column_attrs:
        key = column.key
        if hasattr(program, key) and key != "id":
            setattr(new_program, key, getattr(program, key))

    if is_latest:
        new_program.is_latest = True
    else:
        new_program.is_latest = False

    new_program.revision = last_revision + 1
    new_program.program_id = program.id

    ## update quest_ids with current assoiciated quests
    ## Get all quests associated with the program
    quests = g.client_db_session.query(Quest).join(ProgramQuestAssociation).filter(
        ProgramQuestAssociation.program_id == program.id
    ).all()
    quest_ids = [quest.id for quest in quests]
    new_program.quest_ids = json.dumps(quest_ids)

    ## save program
    g.client_db_session.add(new_program)
    g.client_db_session.commit()

    print(f"New program revision: {new_program.revision}")
    
    return True, f"Program {program.name} successfully released"


def release_quest(quest, is_latest=True):
    ## copy quest data from original quest table to publish quest table
    ## Only update if is_latest is True

    if is_latest:
        # Set all previous published quests for this quest to is_latest = False
        g.client_db_session.query(PublishedQuest).filter(
            PublishedQuest.quest_id == quest.id,
            PublishedQuest.is_latest == True
        ).update({"is_latest": False})

    # Get the last revision number
    last_revision = g.client_db_session.query(PublishedQuest.revision).filter(
        PublishedQuest.quest_id == quest.id
    ).order_by(PublishedQuest.revision.desc()).first()
    last_revision = last_revision[0] if last_revision else 0

    # Create new published quest
    new_quest = PublishedQuest()
    for column in inspect(new_quest).mapper.column_attrs:
        key = column.key
        if hasattr(quest, key) and key != "id":
            setattr(new_quest, key, getattr(quest, key))

    if is_latest:
        new_quest.is_latest = True
    else:
        new_quest.is_latest = False

    new_quest.revision = last_revision + 1
    new_quest.quest_id = quest.id

    g.client_db_session.add(new_quest)
    g.client_db_session.commit()

    ## copy all nodes under the quest into publish nodes table
    nodes = g.client_db_session.query(Node).filter(Node.quest_id == quest.id).all()
    for node in nodes:
        new_node = PublishedNode()
        for column in inspect(new_node).mapper.column_attrs:
            key = column.key
            if hasattr(node, key) and key != "id":
                setattr(new_node, key, getattr(node, key))

        new_node.quest_id = quest.id
        new_node.node_id = node.id
        new_node.published_quest_id = new_quest.id

        ## update all published nodes to is_latest = False
        if is_latest:
            g.client_db_session.query(PublishedNode).filter(
                PublishedNode.node_id == node.id,
                PublishedNode.is_latest == True
            ).update({"is_latest": False})

        last_revision = g.client_db_session.query(PublishedNode.revision).filter(
            PublishedNode.node_id == node.id
        ).order_by(PublishedNode.revision.desc()).first()
        last_revision = last_revision[0] if last_revision else 0

        if is_latest:
            new_node.is_latest = True
        else:
            new_node.is_latest = False
            
        new_node.revision = last_revision + 1

        g.client_db_session.add(new_node)
        g.client_db_session.commit()

        ## copy node transcripts, node options, node option matching, node branch
        node_transcripts = g.client_db_session.query(NodeTranscript).filter(NodeTranscript.node_id == node.id).all()
        for node_transcript in node_transcripts:
            new_node_transcript = PublishedNodeTranscript()
            for column in inspect(new_node_transcript).mapper.column_attrs:
                key = column.key
                if hasattr(node_transcript, key) and key != "id":
                    setattr(new_node_transcript, key, getattr(node_transcript, key))

            new_node_transcript.node_id = node.id
            new_node_transcript.published_node_id = new_node.id
            g.client_db_session.add(new_node_transcript)
            g.client_db_session.commit()

        node_options = g.client_db_session.query(NodeOption).filter(NodeOption.node_id == node.id).all()
        for node_option in node_options:
            new_node_option = PublishedNodeOption()
            for column in inspect(new_node_option).mapper.column_attrs:
                key = column.key
                if hasattr(node_option, key) and key != "id":
                    setattr(new_node_option, key, getattr(node_option, key))

            new_node_option.node_id = node.id
            new_node_option.published_node_id = new_node.id
            new_node_option.node_option_id = node_option.id
            g.client_db_session.add(new_node_option)
            g.client_db_session.commit()

            node_option_matchings = g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_option_id == node_option.id).all()
            for node_option_matching in node_option_matchings:
                new_node_option_matching = PublishedNodeOptionMatching()
                for column in inspect(new_node_option_matching).mapper.column_attrs:
                    key = column.key
                    if hasattr(node_option_matching, key) and key != "id":
                        setattr(new_node_option_matching, key, getattr(node_option_matching, key))

                new_node_option_matching.node_option_id = node_option.id
                new_node_option_matching.published_node_id = new_node.id
                g.client_db_session.add(new_node_option_matching)
                g.client_db_session.commit()

        node_branches = g.client_db_session.query(NodeBranch).filter(NodeBranch.node_id == node.id).all()
        for node_branch in node_branches:
            new_node_branch = PublishedNodeBranch()
            for column in inspect(new_node_branch).mapper.column_attrs:
                key = column.key
                if hasattr(node_branch, key) and key != "id":
                    setattr(new_node_branch, key, getattr(node_branch, key))

            new_node_branch.node_id = node.id
            new_node_branch.published_node_id = new_node.id
            g.client_db_session.add(new_node_branch)
            g.client_db_session.commit()

    return True, f"Quest {quest.name} successfully released"




def release_package_to_clients(package, clients=[], last_publish_date=None):
    from sqlalchemy.orm import joinedload
    from sqlalchemy import or_

    ## get source db session
    source_db_session = get_db_session()

    ## get all xperiences associated with the package
    xperiences = package.xperiences

    ## get all programs associated with the package
    programs = package.programs

    ## get updated xperiences and programs
    updated_xperiences = []
    updated_programs = []

    if last_publish_date:
        updated_xperiences = source_db_session.query(Xperience).join(Quest.xperiences).filter(
            or_(
                Quest.date_updated > last_publish_date,
                Xperience.date_updated > last_publish_date
            ),
            Xperience.packages.any(Package.id == package.id)
        ).options(joinedload(Xperience.quests)).distinct().all()
    else:
        updated_xperiences = xperiences

    if last_publish_date:
        updated_programs = source_db_session.query(Program).join(Quest.programs).filter(
            or_(
                Quest.date_updated > last_publish_date,
                Program.date_updated > last_publish_date
            ),
            Program.packages.any(Package.id == package.id)
        ).distinct().all()
    else:
        updated_programs = programs


    print(f"Updated xperience ids: {[xperience.id for xperience in updated_xperiences]}")
    print(f"Updated program ids: {[program.id for program in updated_programs]}")

    # Get all quests associated with the package from xperiences and programs
    xperience_quest_ids = []
    program_quest_ids = []

    if xperiences:
        xperience_quest_ids = [
            q.id for x in xperiences for q in x.quests
        ]

    if programs:
        program_quest_ids = [
            q.id for p in programs for q in p.quests
        ]

    all_quest_ids = set(xperience_quest_ids + program_quest_ids)

    if all_quest_ids:
        quests_query = source_db_session.query(Quest).filter(Quest.id.in_(all_quest_ids))
        if last_publish_date:
            quests_query = quests_query.filter(Quest.date_updated > last_publish_date)

        all_quests = quests_query.all()
    else:
        all_quests = []

    print(f"All quest ids: {[quest.id for quest in all_quests]}")

    ## publish quests
    for quest in all_quests:
        release_quest(quest)

    ## publish xperiences
    for xperience in updated_xperiences:
        release_xperience(xperience)

    ## publish programs
    for program in updated_programs:
        release_program(program)

    
    return True, f"Package {package.name} successfully released to clients"



def delete_quest(quest, db_session=None):
    if not db_session:
        db_session = g.client_db_session

    ## keep soft delete only, add is_deleted status to the quest
    quest.is_deleted = True
    db_session.add(quest)
    db_session.commit()
    # return True, f"Quest {quest.name} successfully deleted"

    ## delete quest category associations
    associations = g.client_db_session.query(QuestCategoryAssociation).filter(QuestCategoryAssociation.quest_id == quest.id).all()

    for association in associations:
        g.client_db_session.delete(association)

    ## delete quest tag associations
    associations = g.client_db_session.query(QuestTagAssociation).filter(QuestTagAssociation.quest_id == quest.id).all()

    for association in associations:
        g.client_db_session.delete(association)

    ## delete quest facet associations
    associations = g.client_db_session.query(QuestFacetAssociation).filter(QuestFacetAssociation.quest_id == quest.id).all()

    for association in associations:
        g.client_db_session.delete(association)

    ## delete quest badge associations
    associations = g.client_db_session.query(QuestBadgeAssociation).filter(QuestBadgeAssociation.quest_id == quest.id).all()

    for association in associations:
        g.client_db_session.delete(association)

    ## delete quest nodes related tables
    nodes = g.client_db_session.query(Node).filter(Node.quest_id == quest.id).all()

    for node in nodes:
        # Delete node relations where next_node_id is the current node
        node_relations = g.client_db_session.query(Node).filter(Node.next_node_id == node.id).all()
        for node_relation in node_relations:
            node_relation.next_node_id = None
            g.client_db_session.add(node_relation)

        ## Delete node option relations where next_node_id is the current node
        node_options = g.client_db_session.query(NodeOption).filter(NodeOption.next_node_id == node.id).all()
        for node_option in node_options:
            node_option.next_node_id = None
            g.client_db_session.add(node_option)

        ## delete node transcript
        transcripts = g.client_db_session.query(NodeTranscript).filter(NodeTranscript.node_id == node.id).all()
        for transcript in transcripts:
            g.client_db_session.delete(transcript)

        ## delete node branches
        branches = g.client_db_session.query(NodeBranch).filter(NodeBranch.node_id == node.id).all()
        for branch in branches:
            g.client_db_session.delete(branch)

        ## delete node branches related tables
        branches = g.client_db_session.query(NodeBranch).filter(NodeBranch.next_node_id == node.id).all()
        for branch in branches:
            g.client_db_session.delete(branch)

        ## delete node option matching
        option_matchings = g.client_db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_id == node.id).all()
        for option_matching in option_matchings:
            g.client_db_session.delete(option_matching)

        ## delete node options
        options = g.client_db_session.query(NodeOption).filter(NodeOption.node_id == node.id).all()
        for option in options:
            g.client_db_session.delete(option)

        ## delete node
        g.client_db_session.delete(node)

    ## delete quest chest
    if quest.chest_id:
        quest.chest_id = None

    ## delete program quest associations
    associations = g.client_db_session.query(ProgramQuestAssociation).filter(ProgramQuestAssociation.quest_id == quest.id).all()
    for association in associations:
        g.client_db_session.delete(association)

    ## delete xperience quest associations
    associations = g.client_db_session.query(XperienceQuestAssociation).filter(XperienceQuestAssociation.quest_id == quest.id).all()
    for association in associations:
        g.client_db_session.delete(association)

    ## delete package quest associations
    associations = g.client_db_session.query(PackageQuestAssociation).filter(PackageQuestAssociation.quest_id == quest.id).all()
    for association in associations:
        g.client_db_session.delete(association)

    # delete user quest
    user_quests = g.client_db_session.query(UserQuest).filter(UserQuest.quest_id == quest.id).all()
    for user_quest in user_quests:
        g.client_db_session.delete(user_quest)

    ## delete related chest
    chest = g.client_db_session.query(Chest).filter(Chest.id == quest.chest_id).first()
    if chest:
        ## delete user chest records
        user_chests = g.client_db_session.query(UserChest).filter(UserChest.chest_id == chest.id).all()
        for user_chest in user_chests:
            g.client_db_session.delete(user_chest)

        ## delete chest asset associations
        associations = g.client_db_session.query(ChestAssetAssociation).filter(ChestAssetAssociation.chest_id == chest.id).all()
        for association in associations:
            g.client_db_session.delete(association)

        ## delete chest
        g.client_db_session.delete(chest)

    g.client_db_session.delete(quest)

    g.client_db_session.commit()



def publish_attribute_to_client(client):
    ## publish category, tag, facet to client
    
    # Main database session
    source_session = get_db_session()

    ## get client db session
    client_db_session = get_db_session(client.id_key)

    ## get all categories
    categories = source_session.query(Category).all()
    for category in categories:
        new_category = client_db_session.query(Category).get(category.id)
        if new_category is None:
            new_category = Category()
            new_category.id = category.id

        ## copy category data
        for column in inspect(new_category).mapper.column_attrs:
            key = column.key
            if hasattr(category, key):
                setattr(new_category, key, getattr(category, key))

        ## save category
        client_db_session.add(new_category)

        ## update sorting for category xperiences
        category_xperiences = source_session.query(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.category_id == category.id).all()
        for category_xperience in category_xperiences:
            new_category_xperience = client_db_session.query(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.xperience_id == category_xperience.xperience_id, XperienceCategoryAssociation.category_id == category_xperience.category_id).first() 
            if new_category_xperience:
                new_category_xperience.order = category_xperience.order
                client_db_session.add(new_category_xperience)
                client_db_session.commit()

        ## update sorting for category programs
        category_programs = source_session.query(ProgramCategoryAssociation).filter(ProgramCategoryAssociation.category_id == category.id).all()
        for category_program in category_programs:
            new_category_program = client_db_session.query(ProgramCategoryAssociation).filter(ProgramCategoryAssociation.program_id == category_program.program_id, ProgramCategoryAssociation.category_id == category_program.category_id).first()
            if new_category_program:
                new_category_program.order = category_program.order
                client_db_session.add(new_category_program)
                client_db_session.commit()

    ## get all tags
    tags = source_session.query(Tag).all()
    for tag in tags:
        new_tag = client_db_session.query(Tag).get(tag.id)
        if new_tag is None:
            new_tag = Tag()
            new_tag.id = tag.id

        ## copy tag data
        for column in inspect(new_tag).mapper.column_attrs:
            key = column.key
            if hasattr(tag, key):
                setattr(new_tag, key, getattr(tag, key))

        ## save tag
        client_db_session.add(new_tag)

    ## get all facets
    facets = source_session.query(Facet).all()
    for facet in facets:
        new_facet = client_db_session.query(Facet).get(facet.id)
        if new_facet is None:
            new_facet = Facet()
            new_facet.id = facet.id

        ## copy facet data
        for column in inspect(new_facet).mapper.column_attrs:
            key = column.key
            if hasattr(facet, key):
                setattr(new_facet, key, getattr(facet, key))

        ## save facet
        client_db_session.add(new_facet)

    client_db_session.commit()




def publish_database_structure(client_key):
    from sqlalchemy import create_engine, MetaData, text
    from sqlalchemy.schema import CreateTable, CreateIndex
    last_slash_index = app.config.get('SQLALCHEMY_DATABASE_URI').rfind("/")
    base_url = app.config.get('SQLALCHEMY_DATABASE_URI')[:last_slash_index + 1]

    # Create engines for two databases
    source_engine = create_engine(base_url + 'global', future=True)
    target_engine = create_engine(base_url + client_key, future=True)

    # Reflect metadata of two databases
    source_metadata = MetaData()
    source_metadata.reflect(bind=source_engine)

    target_metadata = MetaData()
    target_metadata.reflect(bind=target_engine)

    # Generate SQL statements to update the target database
    def compare_schemas(source_metadata, target_metadata):
        update_statements = []

        # Check if there are new tables
        for table_name, table in source_metadata.tables.items():
            if table_name not in target_metadata.tables:
                # Create table with all its constraints
                update_statements.append(str(CreateTable(table).compile(dialect=source_engine.dialect)))
                
                # Create indexes
                for index in table.indexes:
                    update_statements.append(str(CreateIndex(index).compile(dialect=source_engine.dialect)))

        # Check if there are any changes in table structure
        for table_name, table in source_metadata.tables.items():
            if table_name in target_metadata.tables:
                source_columns = {col.name: col for col in table.columns}
                target_columns = {col.name: col for col in target_metadata.tables[table_name].columns}

                # Check if there are new columns
                for col_name, col in source_columns.items():
                    if col_name not in target_columns:
                        # Include column constraints in the ADD COLUMN statement
                        constraints = []
                        if not col.nullable:
                            constraints.append('NOT NULL')
                        if col.server_default:
                            constraints.append(f'DEFAULT {col.server_default.arg}')
                        
                        constraint_str = ' '.join(constraints)
                        update_statements.append(
                            f'ALTER TABLE "{table_name}" ADD COLUMN "{col_name}" '
                            f"{col.type.compile(dialect=source_engine.dialect)} {constraint_str};"
                        )

                # Check if there are dropped columns
                for col_name in target_columns.keys():
                    if col_name not in source_columns:
                        update_statements.append(f'ALTER TABLE "{table_name}" DROP COLUMN "{col_name}";')

                # Check if the type or constraints of a column have changed
                for col_name, source_col in source_columns.items():
                    if col_name in target_columns:
                        target_col = target_columns[col_name]
                        
                        # Check type changes
                        if source_col.type.compile(dialect=source_engine.dialect) != target_col.type.compile(dialect=source_engine.dialect):
                            update_statements.append(
                                f'ALTER TABLE "{table_name}" ALTER COLUMN "{col_name}" '
                                f"TYPE {source_col.type.compile(dialect=source_engine.dialect)};"
                            )
                        
                        # Check nullable constraint
                        if source_col.nullable != target_col.nullable:
                            if source_col.nullable:
                                update_statements.append(
                                    f'ALTER TABLE "{table_name}" ALTER COLUMN "{col_name}" DROP NOT NULL;'
                                )
                            else:
                                update_statements.append(
                                    f'ALTER TABLE "{table_name}" ALTER COLUMN "{col_name}" SET NOT NULL;'
                                )

                # Sync indexes
                source_indexes = {idx.name: idx for idx in table.indexes}
                target_indexes = {idx.name: idx for idx in target_metadata.tables[table_name].indexes}

                # Add new indexes
                for idx_name, idx in source_indexes.items():
                    if idx_name not in target_indexes:
                        update_statements.append(str(CreateIndex(idx).compile(dialect=source_engine.dialect)))

                # Drop obsolete indexes
                for idx_name in target_indexes.keys():
                    if idx_name not in source_indexes:
                        update_statements.append(f"DROP INDEX IF EXISTS {idx_name};")

                # Sync foreign keys
                source_fks = {fk.name: fk for fk in table.foreign_key_constraints}
                target_fks = {fk.name: fk for fk in target_metadata.tables[table_name].foreign_key_constraints}

                # Drop obsolete foreign keys first
                for fk_name in target_fks.keys():
                    if fk_name not in source_fks:
                        update_statements.append(f'ALTER TABLE "{table_name}" DROP CONSTRAINT IF EXISTS "{fk_name}";')

                # Add new foreign keys
                for fk_name, fk in source_fks.items():
                    if fk_name not in target_fks:
                        # Get the foreign key definition
                        fk_columns = [f'"{col.name}"' for col in fk.columns]
                        ref_columns = [f'"{col.name}"' for col in fk.referenced_columns]
                        
                        update_statements.append(
                            f'ALTER TABLE "{table_name}" ADD CONSTRAINT "{fk_name}" '
                            f'FOREIGN KEY ({", ".join(fk_columns)}) '
                            f'REFERENCES "{fk.referred_table}" ({", ".join(ref_columns)}) '
                            f'ON DELETE {fk.ondelete if fk.ondelete else "NO ACTION"} '
                            f'ON UPDATE {fk.onupdate if fk.onupdate else "NO ACTION"};'
                        )

        return update_statements

    # Generate SQL statements to update the target database
    update_statements = compare_schemas(source_metadata, target_metadata)

    # Execute the update statements in the target database
    with target_engine.connect() as connection:
        with connection.begin():
            for statement in update_statements:
                print(f"Execute update statement: {statement}")
                connection.execute(text(statement))
        connection.commit()


def publish_seniority_feed(feed_id): 
    """
    Publish a seniority feed to a client's database and associate it with the Xapa xircle
    Returns (success, message)
    """
    # Main database session
    source_session = get_db_session()
    
    # Get the feed with all relationships
    feed = source_session.query(FeedSeniority).filter(
        FeedSeniority.id == feed_id
    ).first()

    if not feed:
        return False, "Seniority feed not found"

    # Get all client except global
    clients = source_session.query(Client).filter(Client.id_key != 'global').all()

    for client in clients:
        # Client database session
        target_session = get_db_session(client.id_key)
        try:
            # Get or create Xapa xircle
            xircle = target_session.query(Xircle).filter(
                Xircle.name == 'Xapa',
                Xircle.is_deleted == False
            ).first()

            if not xircle:
                continue

            existing_feed = target_session.query(FeedSeniority).filter(
                FeedSeniority.id == feed_id
            ).first()

            if existing_feed:
                existing_feed.user_id = feed.user_id
                existing_feed.xircle_id = xircle.id
                existing_feed.message = feed.message
                existing_feed.image = feed.image
                existing_feed.days = feed.days
                existing_feed.link = feed.link
                existing_feed.link_type = feed.link_type
                existing_feed.label = feed.label
                existing_feed.is_deleted = feed.is_deleted
                existing_feed.status = feed.status
                existing_feed.date_created = feed.date_created
            else:
                new_feed = FeedSeniority()
                new_feed.id = feed.id
                new_feed.user_id = feed.user_id
                new_feed.xircle_id = xircle.id
                new_feed.message = feed.message
                new_feed.image = feed.image
                new_feed.days = feed.days
                new_feed.link = feed.link
                new_feed.link_type = feed.link_type
                new_feed.label = feed.label
                new_feed.is_deleted = feed.is_deleted
                new_feed.status = feed.status
                new_feed.date_created = feed.date_created
                target_session.add(new_feed)

            # Commit all changes
            target_session.commit()

        except Exception as e:
            target_session.rollback()
        finally:
            target_session.close()
            continue

    return True, "Seniority feed published successfully"




def publish_cast_to_client(client):
    # Main database session
    source_session = get_db_session()

    # Get client database session
    client_db_session = get_db_session(client.id_key)

    # Get all casts from global tenant
    casts = source_session.query(Character).all()

    for cast in casts:
        new_cast = client_db_session.query(Character).get(cast.id)
        if new_cast is None:
            new_cast = Character()
            new_cast.id = cast.id

        user_id = cast.user_id
        if user_id:
            user = source_session.query(User).get(user_id)
            if not user:
                continue

            new_user = client_db_session.query(User).get(user.id)
            if new_user is None:
                new_user = User()
                new_user.id = user.id

            # Copy user data
            for column in inspect(new_user).mapper.column_attrs:
                key = column.key
                if hasattr(user, key):
                    setattr(new_user, key, getattr(user, key))

            # Save user
            if new_user.id is not None:
                client_db_session.add(new_user)
                client_db_session.commit()

        # Copy cast data
        for column in inspect(new_cast).mapper.column_attrs:
            key = column.key
            if hasattr(cast, key):
                setattr(new_cast, key, getattr(cast, key))

        # Save cast
        client_db_session.add(new_cast)

    client_db_session.commit()



