import logging

from flask import Flask
from opencensus.ext.azure.log_exporter import AzureLogHandler
from opencensus.ext.azure.trace_exporter import AzureExporter
from opencensus.ext.flask.flask_middleware import FlaskMiddleware
from opencensus.trace.samplers import ProbabilitySampler
from opencensus.trace.tracer import Tracer

logger = logging.getLogger(__name__)

def init_app_insights(app: Flask) -> None:
    """
    Initialize Azure Application Insights for the Flask application.
    
    Args:
        app: Flask application instance
        
    Returns:
        FlaskMiddleware instance if running in production, None otherwise
    """
    # Get environment from app config
    is_production = app.config.get('IS_PRODUCTION', False)
    environment = app.config.get('ENVIRONMENT', 'dev')
    
    print(f"🔍 App Insights Debug - IS_PRODUCTION: {is_production}, ENVIRONMENT: {environment}")
    
    if not is_production:
        print("⚠️ App Insights: Not initializing - not in production mode")
        return None

    # Get instrumentation key from app config
    instrumentation_key = app.config.get('INSTRUMENTATION_KEY')
    print(f"🔍 App Insights Debug - INSTRUMENTATION_KEY found: {bool(instrumentation_key)}")
    if instrumentation_key:
        print(f"🔍 App Insights Debug - Key starts with: {instrumentation_key[:8]}...")
    
    if not instrumentation_key:
        logger.warning(f"Application Insights instrumentation key not found in config for {environment} environment")
        print("❌ App Insights: Instrumentation key not found!")
        return None
        
    connection_string = f'InstrumentationKey={instrumentation_key}'
    
    print(f"✅ App Insights: Initializing with connection string")
    
    # Set up logging
    logger.addHandler(AzureLogHandler(connection_string=connection_string))
    
    # Set up tracing
    tracer = Tracer(
        exporter=AzureExporter(connection_string=connection_string),
        sampler=ProbabilitySampler(1.0),
    )
    with tracer.span(name='example'):
        logger.info(f'Tracing example for {environment} environment')
    
    # Log successful configuration
    logger.info(f'Application Insights configured successfully for {environment} environment')
    print(f"✅ App Insights: Configuration completed successfully!")
    
    # Initialize Flask middleware for request tracking
    middleware = FlaskMiddleware(
        app,
        exporter=AzureExporter(connection_string=connection_string),
        sampler=ProbabilitySampler(1.0),
    )
    
    print(f"✅ App Insights: Flask middleware initialized")
    return middleware