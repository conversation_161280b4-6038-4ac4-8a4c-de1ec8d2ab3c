import datetime

from flask import g, request
import json

from flask import g, request
from flask_restx import Namespace, Resource, fields, reqparse

from api.common.file import FileService
from api.common.helper import create_response
from clientmodels import MASTER_TENANT, Badge, User, Character, UserAssignment, UserBadge, UserBlock, get_db_session, UserLiveActivity, UserStyle, UserStyleMapping

app_user_api = Namespace('api_app_user', description='App User related operations')

## load default user settings from static/files/user_setting.json
user_settings = {}
try:
    with open('static/files/user_setting.json', 'r') as f:
        user_settings = json.load(f)
except:
    pass

def merge_settings(user_settings_dict, default_settings):
    """
    Recursively merge user settings with default settings.
    User settings take precedence, but missing keys from defaults are added.
    """
    if not isinstance(default_settings, dict):
        return user_settings_dict
    
    result = dict(default_settings)  # Start with defaults
    
    if isinstance(user_settings_dict, dict):
        for key, value in user_settings_dict.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # Recursively merge nested dictionaries
                result[key] = merge_settings(value, result[key])
            else:
                # Override with user value
                result[key] = value
    
    return result

profile_model = app_user_api.model('ProfileItem', {
    'date_of_birth': fields.String(required=False, description='Date of Birth', default="%Y-%m-%d"),
    'preferred_name': fields.String(required=False, description='Preferred Name'),
    'pronouns': fields.String(required=False, description='Pronouns'),
    'phone_number': fields.String(required=False, description='Mobile Number'),
    'recovery_email': fields.String(required=False, description='Recovery Email'),
    'bio': fields.String(required=False, description='User Bio'),
    'location': fields.String(required=False, description='Location'),
    'city': fields.String(required=False, description='City'),
    'state': fields.String(required=False, description='State'),
    'country': fields.String(required=False, description='Country'),
    'schools': fields.List(fields.String, required=False, description='List of Schools'),
    'children': fields.List(fields.String, required=False, description='List of Children'),
    'pets': fields.List(fields.String, required=False, description='List of Pets'),
    'diet': fields.List(fields.String, required=False, description='List of Dietary Preferences'),
    'activities': fields.List(fields.String, required=False, description='List of Activities'),
    'interests': fields.List(fields.String, required=False, description='List of Interests'),
    'image': fields.String(required=False, description='Profile Image'),
    'settings': fields.Raw(required=False, description='User Settings', default=user_settings),
    'comment': fields.String(required=False, description='User Comment'),
    'company': fields.String(required=False, description='Company'),
    'title': fields.String(required=False, description='Title'),
    'first_name': fields.String(required=False, description='First Name'),
    'last_name': fields.String(required=False, description='Last Name')
})


## get user information
@app_user_api.doc(security='bearer')
@app_user_api.route('/me')
class MyProfile(Resource):
    def get(self):
        ## get current user profile
        user = g.db_session.query(User).filter_by(id=g.user_id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        data = user.to_dict()
        
        # Get user settings and merge with default settings to ensure all new preferences are included
        settings = merge_settings(user.settings or {}, user_settings)
        
        # Fetch user's style values with names in a single query using JOIN
        style_query = g.db_session.query(
            UserStyleMapping.style_name,
            UserStyle.style_value
        ).join(
            UserStyle, UserStyleMapping.id == UserStyle.style_id
        ).filter(
            UserStyle.user_id == g.user_id
        ).all()
        
        # Create a mapping of style names to values
        style_values = {style_name: style_value for style_name, style_value in style_query}
        
        # Update xip_code section with actual user values if they exist
        if 'xip_code' in settings:
            # Update with actual style values from database
            if 'Communication Style' in style_values:
                settings['xip_code']['communication_style'] = style_values['Communication Style']
            if 'Work Style' in style_values:
                settings['xip_code']['work_style'] = style_values['Work Style']
            if 'Conflict Management Style' in style_values:
                settings['xip_code']['conflict_management_style'] = style_values['Conflict Management Style']
        
        data['settings'] = settings

        ## print current header information
        print('Current User ID:', g.user_id)
        print(request.headers.get('X-Timezone', ''))

        user_timezone = request.headers.get('X-Timezone', '')
        if user_timezone:
            user.timezone = user_timezone
            g.db_session.commit()


        tenant_id = g.tenant_id
        data['client_id'] = ""
        if tenant_id:
            from clientmodels import Client
            master_db_session = get_db_session(MASTER_TENANT)
            client = master_db_session.query(Client).filter_by(id_key=tenant_id).first()
            if client:
                data['client_id'] = client.id

        return create_response("Get user profile", data=data)
    
    @app_user_api.expect(profile_model)
    def put(self):
        ## update user profile
        user = g.db_session.query(User).filter_by(id=g.user_id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        data = request.json

        # Update user optional info
        if 'first_name' in data:
            user.first_name = data.get('first_name', '').strip()

        if 'last_name' in data:
            user.last_name = data.get('last_name', '').strip()

        if 'company' in data:
            user.company = data.get('company', '').strip()

        if 'title' in data:
            user.title = data.get('title', '').strip()

        if 'preferred_name' in data:
            user.preferred_name = data.get('preferred_name', '').strip()

        if 'date_of_birth' in data:
            date_of_birth = data.get('date_of_birth', '').strip()
            if date_of_birth:
                try:
                    user.date_of_birth = datetime.datetime.strptime(date_of_birth, '%Y-%m-%d').date()
                except ValueError:
                    return create_response("Invalid date format. Please use YYYY-MM-DD", status=400)
            else:
                user.date_of_birth = None

        if 'pronouns' in data:
            user.pronouns = data.get('pronouns', '').strip()

        if 'phone_number' in data:
            user.phone_number = data.get('phone_number', '').strip()

        if 'recovery_email' in data:
            user.recovery_email = data.get('recovery_email', '').strip()

        if 'bio' in data:
            user.bio = data.get('bio', '').strip()
            
        # Update location info
        if 'location' in data:
            user.location = data.get('location', '').strip()
        if 'city' in data:
            user.city = data.get('city', '').strip()
        if 'state' in data:
            user.state = data.get('state', '').strip()
        if 'country' in data:
            user.country = data.get('country', '').strip()
            
        # Update arrays
        if 'schools' in data:
            user.schools = data.get('schools')
        if 'children' in data:
            user.children = data.get('children')
        if 'pets' in data:
            user.pets = data.get('pets')
        if 'diet' in data:
            user.diet = data.get('diet')
        if 'activities' in data:
            user.activities = data.get('activities')
        if 'interests' in data:
            user.interests = data.get('interests')
            
        # Update image if provided
        if 'image' in data:
            image = data.get('image', '').strip()
            FileService.process_entity_image(user, image, 'user', user.id)
        
        # Update settings if provided
        if 'settings' in data:
            new_settings = data.get('settings')
            # Merge user's current settings with new settings, ensuring all default preferences are preserved
            current_settings = merge_settings(user.settings or {}, user_settings)
            # Update with new values from the request
            if isinstance(new_settings, dict):
                updated_settings = merge_settings(new_settings, current_settings)
                user.settings = updated_settings # Assign back to trigger change detection

        ## Add user comment
        if 'comment' in data:
            user.comment = data.get('comment', '')

        ## update unassign user status
        if not user.is_active and user.status == 'unassigned':
            ## if user update their company info, then change status to lead
            if user.company:
                user.status = 'lead'


        # Save changes
        g.db_session.commit()

        ## sync user to other tenants
        from api.common.users_utils import sync_user
        sync_user(user)
        
        data = user.to_dict()
        return create_response("Update user profile", data=data)
    
    def delete(self):
        ## delete user profile
        user = g.db_session.query(User).filter_by(id=g.user_id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        ## update user is_deleted to True, is_active to False, and remove all client assignments
        user.is_deleted = True
        user.is_active = False
        user.status = 'deleted'
        user.client_id = None

        client_assignments = g.db_session.query(UserAssignment).filter_by(user_id=user.id).all()
        for assignment in client_assignments:
            g.db_session.delete(assignment)

            ## remove user data from client db
            client_db_session = get_db_session(assignment.client_id)
            client_user = client_db_session.query(User).filter_by(id=user.id).first()
            if client_user:
                client_db_session.delete(client_user)
                client_db_session.commit()
            
        g.db_session.commit()
        
        return create_response("Delete user profile", data={})


## get user information
@app_user_api.doc(security='bearer')
@app_user_api.route('/<string:id>')
class UserProfile(Resource):
    def get(self, id):
        ## get user profile by id
        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        data = user.to_dict()
        return create_response("Get user profile", data=data)


## get user list
user_list_parser = reqparse.RequestParser()
user_list_parser.add_argument('page', type=int, help='Page number', default=1)
user_list_parser.add_argument('limit', type=int, help='Items per page', default=10)
user_list_parser.add_argument('search', type=str, help='Search query', default='')

@app_user_api.doc(security='bearer')
@app_user_api.route('/list', methods=['GET'])
class UserList(Resource):
    @app_user_api.expect(user_list_parser)
    def get(self):
        args = user_list_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        offset = (page - 1) * limit
        search = args.get('search', '')

        from sqlalchemy import func
        
        query = g.client_db_session.query(User).filter(User.is_deleted != True, User.is_active == True)

        ## exclude admin user account
        query = query.filter(User.id != "********-0000-0000-0000-********0000")

        if search:
            query = query.filter(
                func.concat(User.first_name, ' ', User.last_name).ilike(f"%{search}%") | 
                User.email.ilike(f"%{search}%") | 
                User.preferred_name.ilike(f"%{search}%")
            )
        
        total = query.count()
        users = query.order_by(User.first_name).limit(limit).offset(offset).all()
        data = []
        for user in users:
            data.append(user.to_dict(["id", "first_name", "last_name", "preferred_name", "image", "phone_number", "email", "company", "title"]))
        
        return create_response("User list", data=data, total=total, page=page, limit=limit)


## get casts
@app_user_api.doc(security='bearer')
@app_user_api.route('/casts')
class UserCasts(Resource):
    def get(self):
        data = []
        casts = g.db_session.query(Character).order_by('index').all()
        for cast in casts:
            if cast.user_id:
                cast_user = g.db_session.query(User).filter_by(id=cast.user_id).first()
                if cast_user:
                    user_dict = cast_user.to_dict(["id", "first_name", "last_name", "preferred_name", "image", "phone_number", "email", "company", "title", "bio"])
                    user_dict['character'] = cast.to_dict()
                    data.append(user_dict)
        return create_response("Get user casts", data=data)


## get user badges
@app_user_api.doc(security='bearer')
@app_user_api.route('/<string:user_id>/badges')
class UserBadges(Resource):
    def get(self, user_id):
        ## get user id from g
        data = []
        
        ## get user badges
        user_badges = g.db_session.query(UserBadge).filter_by(user_id=user_id).all()
        for user_badge in user_badges:
            badge = g.db_session.query(Badge).filter_by(id=user_badge.badge_id).first()
            if badge:
                data.append(badge.to_dict())
        
        return create_response("Get user badges", data=data)

## block user endpoints
@app_user_api.doc(security='bearer')
@app_user_api.route('/block/<string:user_id>', methods=['PUT'])
class BlockUser(Resource):
    def put(self, user_id):
        """Block or unblock a user"""
        # Check if user exists
        user = g.client_db_session.query(User).filter(User.id == user_id).first()
        if not user:
            return create_response("User not found", status=404)

        # Check if already blocked
        block = g.client_db_session.query(UserBlock).filter(
            UserBlock.user_id == g.user_id,
            UserBlock.blocked_user_id == user_id
        ).first()

        if block:
            # If already blocked, unblock
            g.client_db_session.delete(block)
            g.client_db_session.commit()
            return create_response("User unblocked successfully", status=200)
        else:
            # Block the user
            block = UserBlock(
                user_id=g.user_id,
                blocked_user_id=user_id
            )
            g.client_db_session.add(block)
            g.client_db_session.commit()
            return create_response("User blocked successfully", status=200)


# Live activity endpoints
live_activity_model = app_user_api.model('LiveActivity', {
    'activity_token': fields.String(required=True, description='Activity token'),
    'data': fields.List(fields.Raw, required=True, description='List of activity objects, each containing id, type, name, start, and end')
})

@app_user_api.doc(security='bearer')
@app_user_api.route('/live-activity')
class UserLiveActivityList(Resource):
    @app_user_api.expect(live_activity_model)
    def post(self):
        """Start a new live activity"""
        data = request.json
        device_id = request.headers.get('x-uuid', '')
        if not device_id:
            return create_response("Device token is required", status=400)

        activity_token = data.get('activity_token')
        activity_data = data.get('data', [])

        activities = []
        for item in activity_data:
            activities.append(UserLiveActivity(
                user_id=g.user_id,
                activity_token=activity_token,
                device_id=device_id,
                activity_id=item['id'],
                activity_type=item['type'],
                activity_name=item['name'],
                start_time=datetime.datetime.fromtimestamp(item['start'], tz=datetime.timezone.utc),
                end_time=datetime.datetime.fromtimestamp(item['end'], tz=datetime.timezone.utc)
            ))

        g.db_session.add_all(activities)
        g.db_session.commit()
        
        return create_response("Live activity started", status=200)
