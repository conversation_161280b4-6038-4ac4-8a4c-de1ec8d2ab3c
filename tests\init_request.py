## init the test request api, create a header with token and make requests
import os

import requests
from flask import g

from clientmodels import UserToken


class InitTestRequest:
    def __init__(self):
        ## get user id and token
        user_id = g.user_id
        db_session = g.db_session

        ## get latest user token
        token = db_session.query(UserToken).filter(UserToken.user_id == user_id).order_by(UserToken.date_created.desc()).first()

        ## add token to header
        self.headers = {
            'Authorization': f'Bearer {token.access_token}'
        }

        self.client = requests
        self.host = os.environ.get('HOST', 'http://127.0.0.1:5000/')
        self.timeout = 240

    def set_token(self, token):
        self.token = token
        self.headers = {
            'Authorization': f'Bearer {self.token}'
        }

    def make_request(self, method, url, **kwargs):
        try:
            response = self.client.request(method, f"{self.host}{url}", headers=self.headers, timeout=self.timeout, **kwargs)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return response

    def get(self, url):
        try:
            url = f'{self.host}{url}'
            response = self.client.get(url, headers=self.headers, timeout=self.timeout)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return response

    def post(self, url, json):
        try:
            url = f'{self.host}{url}'
            response = self.client.post(url, headers=self.headers, json=json, timeout=self.timeout)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return response

    def put(self, url, json):
        try:
            url = f'{self.host}{url}'
            response = self.client.put(url, headers=self.headers, json=json, timeout=self.timeout)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return response

    def delete(self, url):
        try:
            url = f'{self.host}{url}'
            response = self.client.delete(url, headers=self.headers, timeout=self.timeout)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return response
