from flask import g
from flask_restx import Namespace, Resource
from sqlalchemy import func, Boolean

from api.common.helper import create_response
from clientmodels import USER_TENANT, Client, User, UserAssignment, UserStats, Xircle

app_leader_board_api = Namespace('api_app_leader_board', description='Leader Board API')

parser = app_leader_board_api.parser()
## add pagnation
parser.add_argument('page', type=int, help='Page number', location='args', required=False, default=1)
parser.add_argument('limit', type=int, help='Limit number', location='args', required=False, default=20)
parser.add_argument('search', type=str, help='Search keyword', location='args', required=False)
parser.add_argument('xircle_id', type=str, help='Xircle ID', location='args', required=False)

@app_leader_board_api.doc(security='bearer')
@app_leader_board_api.route('/list', methods=['GET'])
class LeaderBoardList(Resource):
    @app_leader_board_api.expect(parser)
    def get(self):
        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)

        ## get user total xp count from user_stats table
        xircle_id = args.get('xircle_id', None)
        
        # First, get user_ids for xircle if specified
        xircle_user_ids = None
        if xircle_id:
            xircle = g.client_db_session.query(Xircle).filter_by(id=xircle_id).first()
            if not xircle:
                return create_response("api.xircle_not_exist", status=200)
            xircle_user_ids = [user_xircle.id for user_xircle in xircle.members]

        # Calculate ranks based on xircle filter and visibility settings
        rank_query = g.db_session.query(
            UserStats.user_id,
            func.rank().over(order_by=UserStats.xp_count.desc()).label('rank')
        ).join(
            User,
            User.id == UserStats.user_id
        ).filter(
            # Only include users who have enabled show_me_in_leader_board in their settings
            (User.settings.is_(None)) |  # No settings at all (include by default)
            (User.settings['xircle_perference'].is_(None)) |  # No xircle preferences (include by default)
            (User.settings['xircle_perference']['show_me_in_leader_board'].is_(None)) |  # Explicitly enabled
            (User.settings['xircle_perference']['show_me_in_leader_board'].cast(Boolean).is_(True))  # Explicitly enabled
        )
        
        if xircle_user_ids:
            rank_query = rank_query.filter(UserStats.user_id.in_(xircle_user_ids))
        
        rank_subquery = rank_query.subquery()

        # Base query with user join and rank
        base_query = g.db_session.query(
            UserStats,
            User,
            rank_subquery.c.rank
        ).join(
            User, 
            User.id == UserStats.user_id
        ).join(
            rank_subquery,
            rank_subquery.c.user_id == UserStats.user_id
        )

        ## if the current tenant id is not the user tenant, then filter users only assigned to current tenant
        tenant_id = g.tenant_id
        if tenant_id != USER_TENANT:
            ## find client info
            client =  g.db_session.query(Client).filter_by(id_key=tenant_id).first()
            if client:
                client_id = client.id
                base_query = base_query.join(
                    UserAssignment,
                    UserAssignment.user_id == User.id
                ).filter(
                    UserAssignment.client_id == client_id
                )
            else:
                return create_response("api.client_not_exist", status=200)
            

        ## search user by first name, last name, preferred name, email
        search = args.get('search', None)
        if search:
            base_query = base_query.filter(
                func.concat(User.first_name, ' ', User.last_name).ilike(f"%{search}%") | 
                User.email.ilike(f"%{search}%") | 
                User.preferred_name.ilike(f"%{search}%")
            )


        # Get total count of filtered results
        total = base_query.count()
        
        # Apply pagination while maintaining the rank
        offset = (page - 1) * limit
        results = base_query.order_by(UserStats.xp_count.desc()).offset(offset).limit(limit).all()

        ## create a new rank index based on current page
        if page > 1:
            rank = (page - 1) * limit
        else:
            rank = 0

        data = []
        for user_stat, user, global_rank in results:
            user_dict = user.to_dict(["id", "first_name", "last_name", "preferred_name", "image", "phone_number", "email", "company", "title"])
            user_dict['xp_count'] = user_stat.xp_count
            user_dict['xp'] = user_stat.xp_count
            user_dict['rank'] = rank + 1  # This is the rank within xircle (if specified) or global rank
            data.append(user_dict)

            rank += 1

        return create_response("api.success", data=data, total=total, page=page, limit=limit)
