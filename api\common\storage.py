import logging

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any, BinaryIO

from flask import session

logger = logging.getLogger(__name__)

class Storage:
    """Main storage service that delegates to concrete storage implementations."""
    def __init__(self, storage_type):
        self.storage_provider = None
        
        # Import storage implementations here to avoid circular imports
        from services.azure_storage import AzureStorage
        from services.local_storage import LocalStorage

        if storage_type == "azure":
            self.storage_provider = AzureStorage()
            logger.info("AzureStorage backend initialized for Storage service.")
        elif storage_type == "local":
            self.storage_provider = LocalStorage()
            logger.info("LocalStorage backend initialized for Storage service.")
        else:
            logger.error(f"Invalid storage type provided: {storage_type}")
            raise ValueError(f"Invalid storage type: {storage_type}. Cannot initialize Storage service.")

        self.default_container_name = 'global'
        self.provider_type = storage_type
        self._container_name_override: Optional[str] = None  # For explicit override

    @property
    def container_name(self) -> str:
        """
        Dynamically retrieves the container name.
        It returns an overridden value if one is set,
        otherwise retrieves from the session.
        """
        if self._container_name_override is not None:
            return self._container_name_override
        try:
            tenant = session.get('tenant_id_key')
            if tenant:
                return str(tenant)
            else:
                logger.warning("Tenant ID not found in session. Using empty container name.")
                return ""
        except RuntimeError:
            # This occurs if session is accessed outside of a request context (e.g. app startup)
            logger.warning("Session context not available for determining container_name. Using empty container name.")
            return ""

    @container_name.setter
    def container_name(self, value: Optional[str]):
        """
        Sets an override for the container name.
        If set to None, the session-based retrieval or default will be used.
        """
        self._container_name_override = value

    def file_exists(self, folder_name, file_name):
        if self.storage_provider:
            try:
                folder_path = self.container_name + '/' + folder_name
                return self.storage_provider.blob_exists(folder_path, file_name)
            except Exception as e:
                logger.error(f"Error checking file existence: {str(e)}")
                return False
        else:
            logger.error("Storage backend not initialized.")
            return False

    def _file_exists(self, container_name, folder_name, file_name):
        if self.storage_provider:
            try:
                folder_path = container_name + '/' + folder_name
                return self.storage_provider.blob_exists(folder_path, file_name)
            except Exception as e:
                logger.error(f"Error checking file existence: {str(e)}")
                return False
        else:
            logger.error("Storage backend not initialized.")
            return False

    def upload_file(self, folder_name, file_name, data, overwrite=False):
        if self.storage_provider:
            try:
                current_container = self.container_name
                if not current_container:
                    logger.error("Cannot upload file: Container name could not be determined.")
                    return None

                if overwrite and self._file_exists(current_container, folder_name, file_name):
                    self.delete_file(folder_name, file_name)

                path_in_container = current_container + '/' + folder_name
                return self.storage_provider.upload_blob(path_in_container, file_name, data)
            except Exception as e:
                logger.error(f"Error uploading file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None

    def download_file(self, folder_name, file_name):
        if self.storage_provider:
            try:
                current_container = self.container_name
                if not current_container and self.default_container_name:
                     logger.info(f"Current container not available, attempting download from default_container: {self.default_container_name}")
                # Try tenant-specific container first
                if current_container and self._file_exists(current_container, folder_name, file_name):
                    tenant_folder_path = current_container + '/' + folder_name
                    return self.storage_provider.download_blob(tenant_folder_path, file_name)
                
                # Fallback to default/global container
                if self._file_exists(self.default_container_name, folder_name, file_name):
                    global_folder_path = self.default_container_name + '/' + folder_name
                    return self.storage_provider.download_blob(global_folder_path, file_name)
                
                logger.error(f"File {file_name} not found in folder {folder_name} in either tenant or default container.")
                return None
            except Exception as e:
                logger.error(f"Error downloading file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None
    
    def delete_file(self, folder_name, file_name):
        if self.storage_provider:
            try:
                current_container = self.container_name
                if not current_container:
                    logger.error("Cannot delete file: Container name could not be determined.")
                    return None
                path_in_container = current_container + '/' + folder_name
                return self.storage_provider.delete_blob(path_in_container, file_name)
            except Exception as e:
                logger.error(f"Error deleting file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None

    def save_file_from_temp(self, temp_file_name, folder_name, file_name=None):
        if self.storage_provider:
            try:
                data = self.download_file('temp', temp_file_name)
                if data:
                    actual_file_name = file_name if file_name else temp_file_name
                    new_file_path_in_upload = self.upload_file(folder_name, actual_file_name, data)
                    if new_file_path_in_upload:
                        self.delete_file('temp', temp_file_name) 
                        return f"{self.container_name}/{folder_name}/{new_file_path_in_upload}" 
                    else:
                        logger.error(f"Failed to re-upload file from temp to {folder_name}")
                        return None
                else:
                    logger.error(f"Failed to download temp file {temp_file_name}")
                    return None 
            except Exception as e:
                logger.error(f"Error saving file from temp: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None

    def upload_temp_file(self, file_name, data):
        if self.storage_provider:
            try:
                current_container = self.container_name
                if not current_container:
                    logger.error("Cannot upload temp file: Container name could not be determined.")
                    return None
                folder_path = current_container + '/' + 'temp'
                return self.storage_provider.upload_blob(folder_path, file_name, data)
            except Exception as e:
                logger.error(f"Error uploading temp file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None
        
    def upload_public_file(self, file_name, data):
        if self.storage_provider:
            try:
                current_container = self.container_name
                if not current_container:
                    logger.error("Cannot upload public file: Container name could not be determined.")
                    return None
                folder_path = current_container + '/' + 'public'
                return self.storage_provider.upload_blob(folder_path, file_name, data)
            except Exception as e:
                logger.error(f"Error uploading public file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None

    def list_files(self, folder_name):
        """List all files in a folder, checking tenant then default container."""
        if self.storage_provider:
            files_list = []
            try:
                current_container = self.container_name
                # List from tenant-specific container
                if current_container:
                    tenant_folder_path = current_container + '/' + folder_name
                    tenant_files = self.storage_provider.list_blobs(tenant_folder_path)
                    if tenant_files: files_list.extend(tenant_files)

                # List from default/global container (if different from tenant or tenant yielded no results)
                # Avoid double listing if current_container is same as default_container_name AND files were found
                # This logic might need adjustment based on how list_blobs structures return values
                # and whether you want to merge or prioritize.
                # For simplicity, appending if distinct or if tenant list was empty.
                if not files_list or current_container != self.default_container_name:
                    global_folder_path = self.default_container_name + '/' + folder_name
                    global_files = self.storage_provider.list_blobs(global_folder_path)
                    if global_files: 
                        # Simple extend; for unique items, more complex merge might be needed
                        files_list.extend(global_files) 
                
                if not files_list:
                    logger.info(f"No files found in folder {folder_name} in tenant or default container.")
                return files_list
            except Exception as e:
                logger.error(f"Error listing files: {str(e)}")
                return [] # Return empty list on error
        else:
            logger.error("Storage backend not initialized.")
            return []

    def move_file(self, source_file_name, destination_file_name):
        if self.storage_provider:
            try:
                current_container = self.container_name
                if not current_container:
                    logger.error("Cannot move file: Container name could not be determined.")
                    return None
                # Assuming source_file_name and destination_file_name are full paths within the container
                # or need to be prefixed with current_container.
                return self.storage_provider.move_blob(current_container, source_file_name, current_container, destination_file_name)
            except Exception as e:
                logger.error(f"Error moving file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None
        
    def copy_file(self, source_file_name, destination_file_name):
        if self.storage_provider:
            try:
                current_container = self.container_name
                if not current_container:
                    logger.error("Cannot copy file: Container name could not be determined.")
                    return None
                # Assuming source_file_name and destination_file_name are full paths within the container
                # or need to be prefixed with current_container.
                return self.storage_provider.copy_blob(current_container, source_file_name, current_container, destination_file_name)
            except Exception as e:
                logger.error(f"Error copying file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None
        
    def copy_tenant_file(self, source_file_name, destination_file_name, tenant_key):
        if self.storage_provider:
            try:
                source_container = self.container_name
                if not source_container:
                    logger.error("Cannot copy file: Source container name could not be determined.")
                    return None
                # tenant_key is the destination container
                # source_file_name and destination_file_name are relative to container roots
                return self.storage_provider.copy_blob(source_container, source_file_name, str(tenant_key), destination_file_name)
            except Exception as e:
                logger.error(f"Error copy_tenant_file: {str(e)}")
                return None
        else:
            logger.error("Storage backend not initialized.")
            return None
            
    def get_signed_url(self, folder_name, file_name, expiry_minutes=60):
        """Get a signed URL for a file, with expiration time in minutes
        
        Args:
            folder_name: Folder path relative to container
            file_name: The file name
            expiry_minutes: Expiry time in minutes (default: 60 minutes)
            
        Returns:
            str: Signed URL if successful, None otherwise
        """
        if not self.storage_provider:
            logger.error("Storage backend not initialized.")
            return None
            
        try:
            current_container = self.container_name
            
            # Try tenant-specific container first
            if current_container and self._file_exists(current_container, folder_name, file_name):
                tenant_folder_path = current_container + '/' + folder_name
                return self.storage_provider.get_signed_url(tenant_folder_path, file_name, expiry_minutes)
            
            # Fallback to default/global container
            if self._file_exists(self.default_container_name, folder_name, file_name):
                global_folder_path = self.default_container_name + '/' + folder_name
                return self.storage_provider.get_signed_url(global_folder_path, file_name, expiry_minutes)
            
            logger.error(f"File {file_name} not found in folder {folder_name} in either tenant or default container.")
            return None
        except Exception as e:
            logger.error(f"Error getting signed URL: {str(e)}")
            return None


class StorageProvider(ABC):
    """Abstract base class for storage providers"""
    
    @abstractmethod
    def blob_exists(self, folder_path: str, blob_name: str) -> bool:
        """Check if a blob exists in the storage"""
        pass
    
    @abstractmethod
    def upload_blob(self, folder_path: str, blob_name: str, data: BinaryIO) -> Optional[str]:
        """Upload a blob to the storage"""
        pass
    
    @abstractmethod
    def download_blob(self, folder_path: str, blob_name: str) -> Optional[BinaryIO]:
        """Download a blob from the storage"""
        pass
    
    @abstractmethod
    def delete_blob(self, folder_path: str, blob_name: str) -> bool:
        """Delete a blob from the storage"""
        pass
    
    @abstractmethod
    def list_blobs(self, folder_path: str) -> List[Dict[str, Any]]:
        """List all blobs in a folder"""
        pass
    
    @abstractmethod
    def move_blob(self, source_folder: str, source_blob: str, 
                destination_folder: str, destination_blob: str) -> Optional[str]:
        """Move a blob from one location to another"""
        pass
    
    @abstractmethod
    def copy_blob(self, source_folder: str, source_blob: str,
                destination_folder: str, destination_blob: str) -> Optional[str]:
        """Copy a blob from one location to another"""
        pass
    
    @abstractmethod
    def get_signed_url(self, folder_path: str, blob_name: str, 
                    expiry_minutes: int = 60) -> Optional[str]:
        """Get a signed URL for a blob that expires after a specified time"""
        pass
