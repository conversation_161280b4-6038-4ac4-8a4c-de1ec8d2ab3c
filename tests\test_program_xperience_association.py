"""
Test for Program-Xperience association functionality
"""
import unittest
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from clientmodels import Program, Xperience, ProgramXperienceAssociation


class TestProgramXperienceAssociation(unittest.TestCase):
    """Test Program-Xperience association functionality"""
    
    def test_program_xperience_association_creation(self):
        """Test that ProgramXperienceAssociation can be created"""
        # This is a basic test to ensure the model can be instantiated
        association = ProgramXperienceAssociation(
            program_id="test-program-id",
            xperience_id="test-xperience-id",
            order=1
        )
        
        self.assertEqual(association.program_id, "test-program-id")
        self.assertEqual(association.xperience_id, "test-xperience-id")
        self.assertEqual(association.order, 1)
    
    def test_models_have_association_relationships(self):
        """Test that Program and Xperience models have the association relationships"""
        # Test that the relationships exist as attributes
        self.assertTrue(hasattr(Program, 'xperiences'))
        self.assertTrue(hasattr(Xperience, 'programs'))
    
    def test_association_ordering(self):
        """Test that associations can be ordered"""
        associations = [
            ProgramXperienceAssociation(
                program_id="test-program-id",
                xperience_id="xperience-1",
                order=2
            ),
            ProgramXperienceAssociation(
                program_id="test-program-id",
                xperience_id="xperience-2",
                order=1
            ),
            ProgramXperienceAssociation(
                program_id="test-program-id",
                xperience_id="xperience-3",
                order=3
            )
        ]
        
        # Sort by order
        sorted_associations = sorted(associations, key=lambda x: x.order)
        
        # Verify the order
        self.assertEqual(sorted_associations[0].xperience_id, "xperience-2")
        self.assertEqual(sorted_associations[1].xperience_id, "xperience-1")
        self.assertEqual(sorted_associations[2].xperience_id, "xperience-3")
    
    def test_api_endpoints_documented(self):
        """Test that the API endpoints are properly defined"""
        # Import the API to check it compiles correctly
        try:
            from api.cms.programs import cms_programs_api
            # Check that the namespace is defined
            self.assertIsNotNone(cms_programs_api)
            self.assertEqual(cms_programs_api.name, 'api_cms_programs')
        except ImportError as e:
            # This test may fail if environment dependencies are not available
            # but the import should at least be syntactically correct
            if "LOCAL_HOST" not in str(e):  # Allow for environment-related import errors
                raise


class TestAPIFunctionality(unittest.TestCase):
    """Test API functionality (mock tests)"""
    
    def test_program_model_includes_xperience_ids(self):
        """Test that the program model includes xperience_ids field"""
        # This would be a more comprehensive test in a real testing environment
        # with a test database setup
        pass
    
    def test_xperience_ordering_endpoint(self):
        """Test that xperience ordering endpoint handles requests correctly"""
        # Mock test - in a real environment this would test the actual API endpoint
        test_data = {
            'xperience_ids': ['xperience-1', 'xperience-2', 'xperience-3']
        }
        
        # Validate the data structure
        self.assertIn('xperience_ids', test_data)
        self.assertEqual(len(test_data['xperience_ids']), 3)
    
    def test_xperience_association_management(self):
        """Test xperience association/dissociation"""
        # Mock test for association management
        test_association_data = {
            'xperience_id': 'test-xperience-id',
            'order': 1
        }
        
        # Validate the data structure
        self.assertIn('xperience_id', test_association_data)
        self.assertIn('order', test_association_data)
        self.assertEqual(test_association_data['order'], 1)


if __name__ == '__main__':
    unittest.main()