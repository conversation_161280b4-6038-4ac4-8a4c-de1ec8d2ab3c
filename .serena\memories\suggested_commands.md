# XAPA Backend Suggested Commands

## Development Environment Setup

### Python Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment (Windows)
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Environment Configuration
```bash
# Set required environment variables for local development
set LOCAL_HOST=localhost
set LOCAL_DATABASE=xapa_db
set LOCAL_USERNAME=postgres
set LOCAL_PASSWORD=your_password
set LOCAL_BINDS=client1,client2
set LOCAL_SECRET_KEY=your_secret_key
set CHATBOT_WEBHOOK_URL=your_webhook_url
```

## Database Management
```bash
# Initialize database migrations
flask db init

# Create migration
flask db migrate -m "Description of changes"

# Apply migrations
flask db upgrade

# Downgrade migration
flask db downgrade
```

## Running the Application
```bash
# Development server
python app.py

# Alternative development server
flask run

# Production server (with <PERSON><PERSON>)
gunicorn -c gunicorn_config.py app:app
```

## Testing
```bash
# Run specific test file
python -m pytest tests/test_quest_api.py

# Run all tests
python -m pytest tests/

# Run with verbose output
python -m pytest -v tests/
```

## Useful Git Commands
```bash
# Check status
git status

# View changes
git diff

# Add changes
git add .

# Commit changes
git commit -m "commit message"

# Push changes
git push origin branch_name
```

## Windows-specific Utilities
```bash
# List directory contents
dir

# Find files
where python
where flask

# Environment variables
set
echo %VARIABLE_NAME%

# Process management
tasklist
taskkill /PID process_id
```

## API Documentation Access
- Global API: http://localhost:5000/
- CMS API: http://localhost:5000/api/cms/document/  
- Account API: http://localhost:5000/api/account/document/
- App API: http://localhost:5000/api/app/document/
- File API: http://localhost:5000/api/file/document/