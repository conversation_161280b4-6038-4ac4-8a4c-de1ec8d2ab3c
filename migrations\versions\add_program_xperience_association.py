"""add program_xperience_association table

Revision ID: add_program_xperience_association
Revises: 3b2d2fd84832
Create Date: 2024-07-29 07:20:00.000000

"""
from alembic import op
import sqlalchemy as sa
from uuid_extensions import uuid7str


# revision identifiers, used by Alembic.
revision = 'add_program_xperience_association'
down_revision = '3b2d2fd84832'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('program_xperience_association',
        sa.<PERSON>umn('id', sa.String(length=36), nullable=False, default=lambda: uuid7str()),
        sa.Column('program_id', sa.String(length=36), nullable=False),
        sa.Column('xperience_id', sa.String(length=36), nullable=False),
        sa.Column('order', sa.Integer(), nullable=True, default=0),
        sa.ForeignKeyConstraint(['program_id'], ['program.id'], ),
        sa.ForeignKeyConstraint(['xperience_id'], ['xperience.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('program_xperience_association')
    # ### end Alembic commands ###