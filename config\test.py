import os
from pathlib import Path

from services.azure_key_vault import Azure<PERSON><PERSON><PERSON><PERSON>

# Build paths inside the project like this: BASE_DIR / 'subdir.
BASE_DIR = Path(__file__).resolve().parent.parent

DEBUG = True

SECRET_KEY = '123456'

if 'USE_REMOTE_POSTGRESQL' in os.environ:
    ## Load the environment variables from azure keyvault
    get_secret = AzureKeyVault().get_secret
    DATABASE_URI = get_secret('db-global-uri')

    ## load binds from key vault
    DATABASE_BINDS = {}
    bind_names = get_secret('db-bind-names').split(',')
    for bind in bind_names:
        DATABASE_BINDS[bind] = get_secret(f'db-{bind}-uri')

else:
    DBHOST=os.environ['LOCAL_HOST']
    DBNAME='test'
    DBUSER=os.environ['LOCAL_USERNAME']
    DBPASS=os.environ['LOCAL_PASSWORD']
    DATABASE_URI = f'postgresql://{DBUSER}:{DBPASS}@{DBHOST}/{DBNAME}'

    DATABASE_BINDS = {}
    bind_names = ['test']

    for bind in bind_names:
        DATABASE_BINDS[bind] = DATABASE_URI.replace(DBNAME, bind)


USER_ENABLE_EMAIL = False

TIME_ZONE = 'UTC'

STATICFILES_DIRS = (str(BASE_DIR.joinpath('static')),)
STATIC_URL = 'static/'


CMS_REDIRECT_URL = 'http://localhost:8000'
APP_REDIRECT_URL = 'http://localhost:3000'
WEB_REDIRECT_URL = 'http://localhost:3000'
