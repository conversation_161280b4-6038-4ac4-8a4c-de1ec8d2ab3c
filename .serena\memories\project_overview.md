# XAPA Backend Project Overview

## Purpose
XAPA is a Flask-based web application backend that provides APIs for:
- User authentication and account management with SSO support
- Content Management System (CMS) for managing clients, users, programs, experiences, quests, attributes, and nodes
- Mobile app features including user management, quest system, social features ("Xircle"), and activity feeds
- File handling capabilities

## Key Features
- Multi-tenant architecture with client-specific database bindings
- JWT-based authentication with SSO integration
- Comprehensive API documentation via Swagger UI
- Azure cloud integration (Storage, Key Vault, App Insights, etc.)
- Real-time features with WebSocket support
- Push notifications (APNS, FCM)
- Elastic APM monitoring

## Target Environment
- Development: Local development with .env configuration
- Production: Azure deployment with Key Vault integration
- Database: PostgreSQL with SQLAlchemy ORM
- Caching: Redis
- File Storage: Azure Blob Storage