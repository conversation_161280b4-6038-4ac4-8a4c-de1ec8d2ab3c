import datetime
from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response, format_datetime_with_timezone
from api.common.file import FileService
from app import storage
from clientmodels import MASTER_TENANT, Asset, Chest, ChestAssetAssociation, ClientXperienceCategoryAssociation, Node, User, UserXperience, Xperience, Category, Tag, \
    Facet, Badge
from clientmodels import Quest, XperienceQuestAssociation, ContentPublishLog, PublishedXperience
from clientmodels import XperienceCategoryAssociation, XperienceTagAssociation, XperienceFacetAssociation, \
    XperienceBadgeAssociation, EntitlementGroupXperienceAssignment, EntitlementGroup
from api.common.publish_utils import release_xperience, release_quest

cms_xperiences_api = Namespace('api_cms_xperiences', description='Xperiences API')

xperience_model = cms_xperiences_api.model('Xperience', {
    'name': fields.String(required=True, description='The xperience name'),
    'learning_objective': fields.String(required=False, description='Learning Objective'),
    'level': fields.Integer(required=False, description='Xperience Level', default=1),
    'description': fields.String(required=True, description='The xperience description'),
    'category_ids': fields.List(fields.String, required=False, description='Category IDs'),
    'tag_ids': fields.List(fields.String, required=False, description='Tag IDs'),
    'facet_ids': fields.List(fields.String, required=False, description='Facet IDs'),
    'badge_ids': fields.List(fields.String, required=False, description='The badge identifiers'),
    'image': fields.String(required=False, description='Thumbnail Image'),
    'image_webapp': fields.String(required=False, description='Web Application Image'),
    'quest_ids': fields.List(fields.String, required=False, description='Quest IDs'),
    'chest': fields.Nested(cms_xperiences_api.model('XperienceChest', {
        'xp': fields.Integer(required=False, description='The quest xp', default=0),
        'coins': fields.Integer(required=False, description='The quest coins', default=0),
        'gems': fields.Integer(required=False, description='The quest gems', default=0),
        'keys': fields.Integer(required=False, description='The quest gems', default=0),
        'asset_ids': fields.List(fields.String, required=False, description='The asset identifiers'),
        'name': fields.String(required=False, description='The chest name'),
        'button_label': fields.String(required=False, description='The button label'),
        'facet_xp': fields.List(fields.Nested(cms_xperiences_api.model('Facet', {
            'id': fields.String(required=True, description='The facet id'),
            'xp': fields.Integer(required=False, description='The facet xp', default=0)
        })))
    })),
    'requires_quest_completion': fields.Boolean(required=False, description='Requires Quest Completion in order', default=True),
    'keys_required': fields.Integer(required=False, description='Keys Required to Unlock', default=0)
})


xperiences_parser = cms_xperiences_api.parser()
xperiences_parser.add_argument('page', type=int, help='Page number', location='args', default=1)
xperiences_parser.add_argument('limit', type=int, help='Items per page', location='args', default=10)
xperiences_parser.add_argument('search', type=str, help='Search keyword', location='args', default=None)
xperiences_parser.add_argument('status', type=str, help='Filter by status', location='args', default=None)
xperiences_parser.add_argument('sort', type=str, help='Sort by', location='args', default=None)

@cms_xperiences_api.doc(security='bearer')
@cms_xperiences_api.route('/list')
class XperienceList(Resource):
    @cms_xperiences_api.expect(xperiences_parser)
    def get(self):
        args = xperiences_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        status = args.get('status', '')
        sort = args.get('sort', None)
        offset = (page - 1) * limit

        if g.tenant_id != MASTER_TENANT:
            ## get global API from app apis
            from api.app.xperience import get_xperience_query
            query = get_xperience_query(search=search, user_id=None)
            if not query:
                return create_response("No xperience found", data=[], total=0, page=page, limit=limit)
            
            if sort:
                if sort.startswith('-'):
                    query = query.order_by(getattr(Xperience, sort[1:]).desc())
                else:
                    query = query.order_by(getattr(Xperience, sort))
            
            results = query.offset(offset).limit(limit).all()
            total = query.count()

            data = []
            for xperience, published_xperience in results:
                xperience_dict = xperience.to_dict()
                if xperience.create_user:
                    create_user = g.db_session.query(User).get(xperience.create_user)
                    xperience_dict['create_user'] = create_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if create_user else None
                else:
                    xperience_dict['create_user'] = None
                if xperience.update_user:
                    update_user = g.db_session.query(User).get(xperience.update_user)
                    xperience_dict['update_user'] = update_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if update_user else None
                else:
                    xperience_dict['update_user'] = None

                ## get xperience entitlement group list
                entitlement_groups = g.client_db_session.query(
                    EntitlementGroupXperienceAssignment.entitlement_group_id,
                    EntitlementGroup.name
                ).join(
                    EntitlementGroup,
                    EntitlementGroup.id == EntitlementGroupXperienceAssignment.entitlement_group_id
                ).filter(
                    EntitlementGroupXperienceAssignment.xperience_id == xperience.id
                ).all()
                xperience_dict['entitlement_group_ids'] = [eg.entitlement_group_id for eg in entitlement_groups]
                xperience_dict['entitlement_group_names'] = ", ".join([eg.name for eg in entitlement_groups])

                data.append(xperience_dict)

            return create_response("success", data=data, total=total, page=page, limit=limit)
        
        from sqlalchemy.orm import aliased
        from sqlalchemy import or_, func
        create_user = aliased(User)
        update_user = aliased(User)
        
        # Subquery to get the latest published date for each xperience
        latest_publish_subquery = g.client_db_session.query(
            ContentPublishLog.content_id,
            func.max(ContentPublishLog.date_published).label('last_published_date')
        ).filter(
            ContentPublishLog.content_type == 'Xperience'
        ).group_by(ContentPublishLog.content_id).subquery()
        
        # Subquery to get the latest published xperience with is_latest flag
        latest_published_xperience_subquery = g.client_db_session.query(
            PublishedXperience.xperience_id,
            PublishedXperience.is_latest
        ).filter(
            PublishedXperience.is_latest == True
        ).subquery()
        
        query = g.client_db_session.query(
            Xperience,
            create_user,
            update_user,
            latest_publish_subquery.c.last_published_date,
            latest_published_xperience_subquery.c.is_latest
        ).outerjoin(
            create_user,
            create_user.id == Xperience.create_user
        ).outerjoin(
            update_user,
            update_user.id == Xperience.update_user
        ).outerjoin(
            latest_publish_subquery,
            latest_publish_subquery.c.content_id == Xperience.id
        ).outerjoin(
            latest_published_xperience_subquery,
            latest_published_xperience_subquery.c.xperience_id == Xperience.id
        )
        
        if search:
            query = query.filter(
                or_(
                    Xperience.name.ilike(f'%{search}%'),
                    Xperience.description.ilike(f'%{search}%')
                )
            )

        if status:
            query = query.filter(Xperience.status == status)

        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(Xperience, sort[1:]).desc())
            else:
                query = query.order_by(getattr(Xperience, sort))
        else:
            query = query.order_by(Xperience.date_updated.desc())

        total = query.count()
        xperiences = query.offset(offset).limit(limit).all()

        data = []
        for xperience, create_user, update_user, last_published_date, is_published_to_clients in xperiences:
            xperience_dict = xperience.to_dict()
            xperience_dict['create_user'] = create_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if create_user else None
            xperience_dict['update_user'] = update_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if update_user else None
            xperience_dict['last_published_date'] = format_datetime_with_timezone(last_published_date) if last_published_date else None
            xperience_dict['is_published_to_clients'] = bool(is_published_to_clients) if is_published_to_clients is not None else False
            data.append(xperience_dict)

        return create_response("success", data=data, total=total, page=page, limit=limit)
    

delete_parser = cms_xperiences_api.parser()
delete_parser.add_argument('force', type=bool, help='Force delete', location='args', default=False)    

@cms_xperiences_api.doc(security='bearer')
@cms_xperiences_api.route('/', methods=['POST'])
@cms_xperiences_api.route('/<string:xperience_id>', methods=['GET', 'PUT', 'DELETE'])
class XperienceItem(Resource):
    @cms_xperiences_api.expect(xperience_model)
    def post(self):
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')
        learning_objective = data.get('learning_objective', '')
        level = data.get('level', 1)
        image = data.get('image', '')
        image_webapp = data.get('image_webapp', '')
        category_ids = data.get('category_ids', [])
        tag_ids = data.get('tag_ids', [])
        facet_ids = data.get('facet_ids', [])
        badge_ids = data.get('badge_ids', [])
        quest_ids = data.get('quest_ids', [])
        requires_quest_completion = data.get('requires_quest_completion', True)
        keys_required = data.get('keys_required', 0)

        ## check if the name is already exists
        xperience = g.client_db_session.query(Xperience).filter(Xperience.name.ilike(name)).first()
        if xperience is not None:
            return create_response("Xperience Name Already Exists", status=400)
        
        xperience = Xperience(name=name, description=description, learning_objective=learning_objective, level=level)
        xperience.requires_quest_completion = requires_quest_completion
        xperience.keys_required = keys_required
        g.client_db_session.add(xperience)
        g.client_db_session.commit()

        FileService.process_entity_image(xperience, image, 'xperience', xperience.id)
        
        FileService.process_entity_image(xperience, image_webapp, 'xperience', xperience.id, 'image_webapp')

        ## save categories
        for category_id in category_ids:
            category = g.client_db_session.query(Category).get(category_id)
            if category is not None:
                association = XperienceCategoryAssociation(xperience_id=xperience.id, category_id=category_id)
                g.client_db_session.add(association)

        ## save tags
        for tag_id in tag_ids:
            tag = g.client_db_session.query(Tag).get(tag_id)
            if tag is not None:
                association = XperienceTagAssociation(xperience_id=xperience.id, tag_id=tag_id)
                g.client_db_session.add(association)

        ## save facets
        for facet_id in facet_ids:
            facet = g.client_db_session.query(Facet).get(facet_id)
            if facet is not None:
                association = XperienceFacetAssociation(xperience_id=xperience.id, facet_id=facet_id)
                g.client_db_session.add(association)

        ## save badges
        for badge_id in badge_ids:
            badge = g.client_db_session.query(Badge).get(badge_id)
            if badge is not None:
                association = XperienceBadgeAssociation(xperience_id=xperience.id, badge_id=badge_id)
                g.client_db_session.add(association)

        ## save quests
        for quest_id in quest_ids:
            quest = g.client_db_session.query(Quest).get(quest_id)
            if quest is not None:
                association = XperienceQuestAssociation(xperience_id=xperience.id, quest_id=quest_id)
                association.order = quest_ids.index(quest_id)
                g.client_db_session.add(association)
        
        ## add chest
        chest = data.get('chest', {})
        if chest:
            xp = chest.get('xp', 0)
            coins = chest.get('coins', 0)
            gems = chest.get('gems', 0)
            keys = chest.get('keys', 0)
            asset_ids = chest.get('asset_ids', [])
            chest_name = chest.get('name', "YOUR REWARDS")
            button_label = chest.get('button_label', "Claim Rewards")
            facets = chest.get('facet_xp', [])

            ## create chest
            chest = Chest(
                xp=xp,
                coins=coins,
                gems=gems,
                keys=keys,
                name=chest_name,
                button_label=button_label,
                facet_xp = json.dumps(facets)
            )
            g.client_db_session.add(chest)
            g.client_db_session.commit()

            ## save chest assets
            for asset_id in asset_ids:
                asset = g.client_db_session.query(Asset).get(asset_id)
                if asset is not None:
                    association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                    g.client_db_session.add(association)

            ## save chest id in quest
            xperience.chest_id = chest.id

        g.client_db_session.add(xperience)
        g.client_db_session.commit()

        data = xperience.to_dict()

        return create_response("Xperience Created", data=data)


    def get(self, xperience_id):
        ## check which tenant is using the API
        if g.tenant_id != MASTER_TENANT:
            content_db_session = g.db_session
        else:
            content_db_session = g.client_db_session

        xperience = content_db_session.query(Xperience).get(xperience_id)
        if xperience is None:
            return create_response("Xperience Not Found", status=404)
        
        data = xperience.to_dict()
        data['category_ids'] = [category.id for category in xperience.categories]

        ## get categories for client
        if g.tenant_id != MASTER_TENANT:
            client_categories = g.client_db_session.query(ClientXperienceCategoryAssociation).filter(ClientXperienceCategoryAssociation.xperience_id == xperience.id).all()
            client_category_ids = [cat.category_id for cat in client_categories]
            data['category_ids'] = client_category_ids if client_category_ids else data['category_ids']

        data['tag_ids'] = [tag.id for tag in xperience.tags]
        data['facet_ids'] = [facet.id for facet in xperience.facets]
        data['badge_ids'] = [badge.id for badge in xperience.badges]
        
        ## get quests
        quest_ids = []
        data['quests'] = []
        quests = content_db_session.query(XperienceQuestAssociation).filter(XperienceQuestAssociation.xperience_id == xperience.id).order_by(XperienceQuestAssociation.order).all()
        for quest in quests:
            quest_ids.append(quest.quest_id)
            data['quests'].append({
                "id": quest.quest_id,
                "order": quest.order
            })
        data['quest_ids'] = quest_ids
        

        ## get chest
        chest = {}
        if xperience.chest_id:
            chest = content_db_session.query(Chest).get(xperience.chest_id)
            if chest:
                chest_data = chest.to_dict()
                assets = []
                for asset in chest.assets:
                    assets.append(asset.id)
                chest_data['asset_ids'] = assets
                data['chest'] = chest_data
        else:
            data['chest'] = {
                'xp': 0,
                'coins': 0,
                'gems': 0,
                'keys': 0,
                'asset_ids': [],
                'name': "YOUR REWARDS",
                'button_label': "Claim Rewards",
                'facet_xp': []
            }

        return create_response("success", data=data)
    

    @cms_xperiences_api.expect(xperience_model)
    def put(self, xperience_id):
        ## we only allow users to edit categories if it's not super admin tenant
        if g.tenant_id != MASTER_TENANT:
            xperience = g.db_session.query(Xperience).get(xperience_id)
            if xperience is None:
                return create_response("Xperience Not Found", status=200)
            
            data = request.json
            category_ids = data.get('category_ids', [])

            ## save client categories
            g.client_db_session.query(ClientXperienceCategoryAssociation).filter(ClientXperienceCategoryAssociation.xperience_id == xperience.id).delete()
            for category_id in category_ids:
                category = g.client_db_session.query(Category).get(category_id)
                if category is not None:
                    association = ClientXperienceCategoryAssociation(xperience_id=xperience.id, category_id=category_id)
                    g.client_db_session.add(association)

            g.client_db_session.commit()
            return create_response("Xperience Categories Updated", data=xperience.to_dict())

        xperience = g.client_db_session.query(Xperience).get(xperience_id)
        if xperience is None:
            return create_response("Xperience Not Found", status=200)
        
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')
        learning_objective = data.get('learning_objective', '')
        level = data.get('level', 1)
        image = data.get('image', '')
        image_webapp = data.get('image_webapp', '')
        category_ids = data.get('category_ids', [])
        tag_ids = data.get('tag_ids', [])
        facet_ids = data.get('facet_ids', [])
        badge_ids = data.get('badge_ids', [])
        requires_quest_completion = data.get('requires_quest_completion', True)
        keys_required = data.get('keys_required', 0)

        ## check if the name is already exists
        if name != xperience.name:
            existing_xperience = g.client_db_session.query(Xperience).filter(Xperience.name.ilike(name)).first()
            if existing_xperience:
                return create_response("Xperience Name Already Exists", status=400)
        
        xperience.name = name
        xperience.description = description
        xperience.learning_objective = learning_objective
        xperience.level = level
        xperience.requires_quest_completion = requires_quest_completion
        xperience.keys_required = keys_required

        FileService.process_entity_image(xperience, image, 'xperience', xperience.id)
        
        FileService.process_entity_image(xperience, image_webapp, 'xperience', xperience.id, 'image_webapp')

        ## save categories
        g.client_db_session.query(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.xperience_id == xperience.id).delete()
        for category_id in category_ids:
            category = g.client_db_session.query(Category).get(category_id)
            if category is not None:
                association = XperienceCategoryAssociation(xperience_id=xperience.id, category_id=category_id)
                g.client_db_session.add(association)

        ## save tags
        g.client_db_session.query(XperienceTagAssociation).filter(XperienceTagAssociation.xperience_id == xperience.id).delete()
        for tag_id in tag_ids:
            tag = g.client_db_session.query(Tag).get(tag_id)
            if tag is not None:
                association = XperienceTagAssociation(xperience_id=xperience.id, tag_id=tag_id)
                g.client_db_session.add(association)

        ## save facets
        g.client_db_session.query(XperienceFacetAssociation).filter(XperienceFacetAssociation.xperience_id == xperience.id).delete()
        for facet_id in facet_ids:
            facet = g.client_db_session.query(Facet).get(facet_id)
            if facet is not None:
                association = XperienceFacetAssociation(xperience_id=xperience.id, facet_id=facet_id)
                g.client_db_session.add(association)

        ## save badges, add new badges and remove deleted badges
        existing_badges = [badge.id for badge in xperience.badges]
        for badge_id in badge_ids:
            if badge_id not in existing_badges:
                badge = g.client_db_session.query(Badge).get(badge_id)
                if badge is not None:
                    association = XperienceBadgeAssociation(xperience_id=xperience.id, badge_id=badge_id)
                    g.client_db_session.add(association)
        for badge_id in existing_badges:
            if badge_id not in badge_ids:
                association = g.client_db_session.query(XperienceBadgeAssociation).filter(XperienceBadgeAssociation.xperience_id == xperience.id, XperienceBadgeAssociation.badge_id == badge_id).first()
                if association is not None:
                    g.client_db_session.delete(association)
        
        ## save quests
        g.client_db_session.query(XperienceQuestAssociation).filter(XperienceQuestAssociation.xperience_id == xperience.id).delete()
        quest_ids = data.get('quest_ids', [])
        for quest_id in quest_ids:
            quest = g.client_db_session.query(Quest).get(quest_id)
            if quest is not None:
                association = XperienceQuestAssociation(xperience_id=xperience.id, quest_id=quest_id)
                association.order = quest_ids.index(quest_id)
                g.client_db_session.add(association)

        ## update chest if exists, add new assets and remove deleted assets. If chest does not exist, create a new chest
        chest = data.get('chest', {})
        if chest:
            xp = chest.get('xp', 0)
            coins = chest.get('coins', 0)
            gems = chest.get('gems', 0)
            keys = chest.get('keys', 0)
            asset_ids = chest.get('asset_ids', [])
            chest_name = chest.get('name', "YOUR REWARDS")
            button_label = chest.get('button_label', "Claim Rewards")
            facets = chest.get('facet_xp', [])

            ## check if chest exists
            if xperience.chest_id:
                chest = g.client_db_session.query(Chest).get(xperience.chest_id)
                if chest:
                    chest.xp = xp
                    chest.coins = coins
                    chest.gems = gems
                    chest.keys = keys
                    chest.name = chest_name
                    chest.button_label = button_label
                    chest.facet_xp = json.dumps(facets)

                ## check if chest assets changed, add new assets and remove deleted assets
                existing_assets = [asset.id for asset in chest.assets]
                for asset_id in asset_ids:
                    if asset_id not in existing_assets:
                        asset = g.client_db_session.query(Asset).get(asset_id)
                        if asset is not None:
                            association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                            g.client_db_session.add(association)
                for asset_id in existing_assets:
                    if asset_id not in asset_ids:
                        association = g.client_db_session.query(ChestAssetAssociation).filter(ChestAssetAssociation.chest_id == chest.id, ChestAssetAssociation.asset_id == asset_id).first()
                        if association is not None:
                            g.client_db_session.delete(association)

            else:
                ## create chest
                chest = Chest(
                    xp=xp,
                    coins=coins,
                    gems=gems,
                    keys=keys,
                    name=chest_name,
                    button_label=button_label,
                    facet_xp = json.dumps(facets)
                )
                g.client_db_session.add(chest)
                g.client_db_session.commit()

                ## save chest assets
                for asset_id in asset_ids:
                    asset = g.client_db_session.query(Asset).get(asset_id)
                    if asset is not None:
                        association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                        g.client_db_session.add(association)

                ## save chest id in program
                xperience.chest_id = chest.id

        ## update xperience last modified date
        xperience.update_user = g.user_id
        xperience.date_updated = datetime.datetime.utcnow()

        g.client_db_session.add(xperience)
        g.client_db_session.commit()
        
        data = xperience.to_dict()

        return create_response("Xperience Updated", data=data)
    

    @cms_xperiences_api.expect(delete_parser)
    def delete(self, xperience_id):
        xperience = g.client_db_session.query(Xperience).get(xperience_id)
        if xperience is None:
            return create_response("Xperience Not Found", status=404)

        force_delete = request.args.get('force', 'false')
        if force_delete and force_delete == 'true':
            ## remove all associations
            g.client_db_session.query(XperienceCategoryAssociation).filter(XperienceCategoryAssociation.xperience_id == xperience_id).delete()
            g.client_db_session.query(XperienceTagAssociation).filter(XperienceTagAssociation.xperience_id == xperience_id).delete()
            g.client_db_session.query(XperienceFacetAssociation).filter(XperienceFacetAssociation.xperience_id == xperience_id).delete()
            g.client_db_session.query(XperienceBadgeAssociation).filter(XperienceBadgeAssociation.xperience_id == xperience_id).delete()
            g.client_db_session.query(XperienceQuestAssociation).filter(XperienceQuestAssociation.xperience_id == xperience_id).delete()

            ## remove chest assets
            chest_id = xperience.chest_id
            if chest_id:
                xperience.chest_id = None
                g.client_db_session.query(ChestAssetAssociation).filter(ChestAssetAssociation.chest_id == chest_id).delete()
                g.client_db_session.query(Chest).filter(Chest.id == chest_id).delete()

            ## remove user record
            g.db_session.query(UserXperience).filter(UserXperience.xperience_id == xperience_id).delete()

            ## remove xperience
            g.client_db_session.delete(xperience)
            g.client_db_session.commit()
            
            return create_response("Xperience Deleted")
        else:
            xperience.status = 'Archived'
            g.client_db_session.commit()

        return create_response("Xperience Archived")



xperience_status_model = cms_xperiences_api.model('XperienceStatus', {
    'status': fields.String(required=True, description='The xperience status', enum=['Draft', 'Ready', 'Published', 'Archived'])
})

@cms_xperiences_api.doc(security='bearer')
@cms_xperiences_api.route('/<string:xperience_id>/status', methods=['PUT'])
class XperienceStatus(Resource):
    @cms_xperiences_api.expect(xperience_status_model)
    def put(self, xperience_id):

        xperience = g.client_db_session.query(Xperience).get(xperience_id)
        if xperience is None:
            return create_response("Xperience Not Found", status=404)
        
        data = request.json
        status = data.get('status', 'Draft')
            
        xperience.status = status
        g.client_db_session.commit()

        ## if the status is ""Published", Need to save the records to publish log table
        if status == "Published":
            publish_log = ContentPublishLog(content_id=xperience.id, content_type='Xperience')
            publish_log.user_id = g.user_id
            user = g.client_db_session.query(User).get(g.user_id)
            if user:
                publish_log.user_name = user.first_name + " " + user.last_name

            ## get current version number and increase by 1
            current_version = g.client_db_session.query(ContentPublishLog).filter(ContentPublishLog.content_id == xperience.id).count()
            publish_log.version = current_version + 1

            ## release xperience
            release_xperience(xperience, is_latest=False)

            xperience_data = xperience.to_dict()
            quests = []
            print("Xperience Quests IDs:", [quest.id for quest in xperience.quests])
            
            for quest in xperience.quests:
                ## update a new revision in the publish quest table
                release_quest(quest, is_latest=False)

                quest_data = quest.to_dict()

                nodes = g.db_session.query(Node).filter_by(quest_id=quest.id).order_by(Node.date_created).all()
                node_data = []
                for node in nodes:
                    node_data.append(node.to_dict())
                quest_data['nodes'] = node_data

                chest_id = quest.chest_id
                if chest_id:
                    chest = g.client_db_session.query(Chest).get(chest_id)
                    if chest:
                        chest_data = chest.to_dict()
                        assets = []
                        for asset in chest.assets:
                            assets.append(asset.to_dict())
                        chest_data['assets'] = assets
                        quest_data['chest'] = chest_data

                quests.append(quest_data)

            xperience_data['quests'] = quests
            
            chest_id = xperience.chest_id
            if chest_id:
                chest = g.client_db_session.query(Chest).get(chest_id)
                if chest:
                    chest_data = chest.to_dict()
                    assets = []
                    for asset in chest.assets:
                        assets.append(asset.to_dict())
                    chest_data['assets'] = assets
                    xperience_data['chest'] = chest_data

            ## save xperience data to storage
            
            folder = f"backup"
            filename = f"xperience/{xperience.id}/version_{publish_log.version}.json"
            storage.upload_file(folder, filename, json.dumps(xperience_data), overwrite=True)
            publish_log.backup_path = f"{folder}/{filename}"

            g.client_db_session.add(publish_log)
            g.client_db_session.commit()


        data = xperience.to_dict()
        return create_response("Xperience Status Updated", data=data)





## sync xperience to client
@cms_xperiences_api.doc(security='bearer')
@cms_xperiences_api.route('/sync/<string:client_id>/<string:xperience_id>', methods=['POST'])
class XperienceSync(Resource):
    def post(self, client_id, xperience_id):
        from api.common.publish_utils import sync_xperience_to_client
        from clientmodels import Client

        xperience = g.db_session.query(Xperience).get(xperience_id)
        if xperience is None:
            return create_response("Xperience Not Found", status=404)
        
        ## check if the client exists
        client = g.db_session.query(Client).get(client_id)
        if client is None:
            return create_response("Client Not Found", status=404)

        ## check if the xperience already exists in the client
        sync_xperience_to_client(xperience, client)

        return create_response("Xperience Synced")
