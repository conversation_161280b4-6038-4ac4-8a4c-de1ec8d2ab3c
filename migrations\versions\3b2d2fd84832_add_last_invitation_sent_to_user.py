"""add last_invitation_sent to user

Revision ID: 3b2d2fd84832
Revises: 66aee7ced1b0
Create Date: 2025-06-20 10:54:40.963636

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3b2d2fd84832'
down_revision = '66aee7ced1b0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('last_invitation_sent', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('last_invitation_sent')

    # ### end Alembic commands ###
