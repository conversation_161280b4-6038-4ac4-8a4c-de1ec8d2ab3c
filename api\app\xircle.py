from flask import g, request
from flask_restx import Namespace, Resource, fields, reqparse
from sqlalchemy import alias, and_

from api.common.file import FileService
from api.common.helper import create_response
from api.common.notification import NotificationFunction
from clientmodels import Xircle, XircleMember, User, Feed

from api.app.badge import check_badge

app_xircle_api = Namespace('api_app_xircle', description='Xircle related operations')


## xircle model
xircle_model = app_xircle_api.model('XircleItem', {
    'name': fields.String(required=True, description='Xircle Name'),
    'description': fields.String(required=True, description='Xircle Description'),
    'image': fields.String(required=True, description='Xircle Image'),
    'is_public': fields.Boolean(required=True, description='Is Xircle Public', default=True),
    'include_leaderboard': fields.Boolean(required=True, description='Include Leaderboard', default=True)
})

## create, edit, delete and get xircle
@app_xircle_api.doc(security='bearer')
@app_xircle_api.route('/', methods=['POST'])
@app_xircle_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class XircleObject(Resource):
    def get(self, id):
        ## get xircle by id 
        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id, Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        # Get member count
        member_count = g.client_db_session.query(XircleMember).filter(XircleMember.xircle_id==id).count()
        
        # Get member preview with user details
        member_preview = g.client_db_session.query(User).join(XircleMember).filter(
            XircleMember.xircle_id == id
        ).limit(5).all()
        
        # Convert member preview to list of dictionaries
        member_preview_list = [m.to_dict(["id", "first_name", "last_name",  "preferred_name", "image", "title"]) for m in member_preview]
        is_member = g.client_db_session.query(XircleMember).filter(XircleMember.xircle_id==id, XircleMember.user_id==g.user_id).first() is not None
        is_creator = xircle.creator_id == g.user_id
        
        data = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard'])
        
        # Get creator info
        creator = g.client_db_session.query(User).filter(User.id==xircle.creator_id).first()
        data['creator'] = creator.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title']) if creator else None
        
        data['is_creator'] = is_creator
        data['is_member'] = is_member
        # Determine permissions based on xircle type and user role
        data['can_invite'] = is_creator or (xircle.is_public and is_member)  # Creator can always invite, members can invite in public xircles
        data['can_delete'] = is_creator  # Only creator can delete
        data['can_edit'] = is_creator  # Only creator can delete
        data['can_leave'] = is_member  # Only members can leave
        data['can_join'] = xircle.is_public and not is_member  # Only non-members can join
        data['can_post'] = is_member  # Only members can post
        data['member_count'] = member_count
        data['member_preview'] = member_preview_list
        
        return create_response("Get xircle", data=data)

    @app_xircle_api.expect(xircle_model)
    def post(self):
        ## create a xircle
        data = request.json

        name = data.get('name').strip()
        description = data.get('description')
        image = data.get('image', '')
        is_public = data.get('is_public', True)
        include_leaderboard = data.get('include_leaderboard', True)

        ## check if name is already taken, case insensitive
        xircle = g.client_db_session.query(Xircle).filter(Xircle.name.ilike(name), Xircle.is_deleted == False).first()
        if xircle is not None:
            return create_response("Xircle name already taken", status=400)
        
        ## check if user exist in current db
        user = g.client_db_session.query(User).filter(User.id==g.user_id).first()
        if user is None:
            return create_response("You are not entitled to perform this action.", status=404)
        
        xircle = Xircle()
        xircle.name = name
        xircle.description = description
        xircle.is_public = is_public
        xircle.include_leaderboard = include_leaderboard

        ## creator is the current user
        xircle.creator_id = g.user_id

        g.client_db_session.add(xircle)
        g.client_db_session.flush()

        ## save image
        ## move the image from the temp folder to user's xircle folder
        FileService.process_entity_image(xircle, image, 'xircle', xircle.id)

        g.client_db_session.add(xircle)
        g.client_db_session.commit()

        ## add creator to member list
        xircle_member = XircleMember()
        xircle_member.xircle_id = xircle.id
        xircle_member.user_id = g.user_id
        
        g.client_db_session.add(xircle_member)
        g.client_db_session.commit()
        
        ## check xircle related badge
        rewards = {'badges': []}
        first_xircle_badge = check_badge('', 'first_xircle')
        if first_xircle_badge:
            rewards['badges'].append(first_xircle_badge)

        return create_response("Xircle created", data=xircle.to_dict(), rewards=rewards)


    @app_xircle_api.expect(xircle_model)
    def put(self, id):
        ## edit a xircle
        data = request.json

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id, Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        # Check if user is the owner of the xircle
        if xircle.creator_id != g.user_id:
            return create_response("You don't have permission to edit this xircle", status=403)
        
        name = data.get('name').strip()
        description = data.get('description')
        image = data.get('image', '')
        is_public = data.get('is_public', True)
        include_leaderboard = data.get('include_leaderboard', True)

        ## check if name is already taken, case insensitive
        xircle_check = g.client_db_session.query(Xircle).filter(Xircle.name.ilike(name), Xircle.is_deleted == False).first()
        if xircle_check is not None and xircle_check.id != xircle.id:
            return create_response("Xircle name already taken", status=400)
        
        xircle.name = name
        xircle.description = description
        xircle.is_public = is_public
        xircle.include_leaderboard = include_leaderboard

        ## save image if it is different
        FileService.process_entity_image(xircle, image, 'xircle', xircle.id)

        g.client_db_session.add(xircle)
        g.client_db_session.commit()

        return create_response("Edit xircle")

    def delete(self, id):
        ## delete a xircle
        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id, Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        # Check if user is the creator of the xircle
        if xircle.creator_id != g.user_id:
            return create_response("Only the creator can delete this xircle", status=403)

        # Soft delete all related feeds
        feeds = g.client_db_session.query(Feed).filter(Feed.xircle_id==id, Feed.is_deleted==False).all()
        for feed in feeds:
            feed.delete()
        
        # Soft delete the xircle
        xircle.delete()
        
        # Commit all changes
        g.client_db_session.commit()

        return create_response("Xircle and all related feeds have been deleted")


## get xircle list
parser = reqparse.RequestParser()
parser.add_argument('page', type=int, help='Page number', default=1)
parser.add_argument('limit', type=int, help='Items per page', default=10)
parser.add_argument('is_public', type=bool, help='Filter by public status', default=None)
parser.add_argument('member_id', type=str, default=None, help='Filter by member id to find xircles with this member')
parser.add_argument('type', type=str, default=None, help='Filter by xircle type (created, joined, other), if xircle type is not specified, all xircles are returned', choices=['created', 'joined', 'other'])
parser.add_argument('search', type=str, help='Search term', default=None)

@app_xircle_api.doc(security='bearer')
@app_xircle_api.route('/list', methods=['GET'])
class XircleList(Resource):
    @app_xircle_api.expect(parser)
    def get(self):
        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        offset = (page - 1) * limit
        is_public = args.get('is_public', None)
        member_id = args.get('member_id', None)
        xircle_type = args.get('type', None)
        search = args.get('search', None)

        # Base query with non-deleted xircles
        query = g.client_db_session.query(
            Xircle
        ).filter(
            Xircle.name.notilike('xapa'), 
            Xircle.id != '00000000-0000-0000-0000-000000000000',
            Xircle.is_deleted == False
        )

        # 1. Filter by public status if specified
        if is_public is not None:
            query = query.filter(Xircle.is_public == is_public)

        # 2. Filter by member if specified
        if member_id:
            member_alias = alias(XircleMember)
            query = query.join(
                member_alias,
                and_(
                    member_alias.c.xircle_id == Xircle.id,
                    member_alias.c.user_id == member_id
                )
            )

        # 3. Filter by xircle type
        if xircle_type:
            if xircle_type == 'created':
                # Show xircles created by current user
                query = query.filter(Xircle.creator_id == g.user_id)
            
            elif xircle_type == 'joined':
                # Show xircles where current user is a member
                joined_alias = alias(XircleMember)
                query = query.join(
                    joined_alias,
                    and_(
                        joined_alias.c.xircle_id == Xircle.id,
                        joined_alias.c.user_id == g.user_id
                    )
                )
            
            elif xircle_type == 'other':
                # Show xircles where user is neither creator nor member
                # First, get all xircles where user is a member
                member_subquery = (
                    g.client_db_session.query(XircleMember.xircle_id)
                    .filter(XircleMember.user_id == g.user_id)
                    .subquery()
                )
                # Exclude xircles where user is creator or member
                query = query.filter(
                    and_(
                        Xircle.creator_id != g.user_id,
                        ~Xircle.id.in_(member_subquery)
                    )
                )

        # 4. Apply search filter if specified
        if search:
            query = query.filter(Xircle.name.ilike(f"%{search}%"))

        # Get total count before pagination
        total = query.count()

        # Apply pagination and ordering
        xircles = query.order_by(Xircle.date_updated.desc()).limit(limit).offset(offset).all()
        
        # Get xircle IDs for member preview
        xircle_ids = [x.id for x in xircles]

        # Get all creators for these xircles in a single query
        creators = {
            user.id: user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title'])
            for user in g.client_db_session.query(User).filter(
                User.id.in_([x.creator_id for x in xircles])
            ).all()
        }

        # Get all xircle memberships for current user in a single query
        user_memberships = set(
            xircle_id for (xircle_id,) in 
            g.client_db_session.query(XircleMember.xircle_id)
            .filter(
                XircleMember.user_id == g.user_id,
                XircleMember.xircle_id.in_(xircle_ids)
            ).all()
        )

        # Get member counts and previews using CTE
        from sqlalchemy import text
        member_preview_query = text("""
            WITH RankedMembers AS (
                SELECT 
                    xm.xircle_id,
                    COUNT(*) OVER (PARTITION BY xm.xircle_id) as member_count,
                    ROW_NUMBER() OVER (PARTITION BY xm.xircle_id ORDER BY xm.date_created) as rn,
                    u.id as user_id,
                    u.first_name,
                    u.last_name,
                    u.preferred_name,
                    u.title,
                    u.image
                FROM xircle_member xm
                JOIN "user" u ON u.id = xm.user_id
                WHERE xm.xircle_id = ANY(:xircle_ids)
            )
            SELECT * FROM RankedMembers WHERE rn <= 5
        """)
        
        member_results = g.client_db_session.execute(
            member_preview_query, 
            {'xircle_ids': xircle_ids}
        ).fetchall()

        # Process member preview results
        member_previews = {}
        member_counts = {}
        
        for row in member_results:
            xircle_id = row.xircle_id
            
            # Initialize member preview list for this xircle
            if xircle_id not in member_previews:
                member_previews[xircle_id] = []
                member_counts[xircle_id] = row.member_count
            
            # Add member to preview
            member_previews[xircle_id].append({
                'id': row.user_id,
                'first_name': row.first_name,
                'last_name': row.last_name,
                'preferred_name': row.preferred_name,
                'title': row.title,
                'image': row.image
            })
        
        # Process results
        data = []
        for xircle in xircles:
            item = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard', 'date_updated'])
            
            # Add member count and preview
            item['member_count'] = member_counts.get(xircle.id, 0)
            item['member_preview'] = member_previews.get(xircle.id, [])

            # Add creator info
            item['creator'] = creators.get(xircle.creator_id)

            # Add user-specific properties
            item['is_creator'] = xircle.creator_id == g.user_id
            item['is_member'] = xircle.id in user_memberships
            item['can_invite'] = item['is_creator'] or (xircle.is_public and item['is_member'])
            item['can_edit'] = item['is_creator']
            item['can_delete'] = item['is_creator']
            item['can_leave'] = item['is_member']
            item['can_join'] = xircle.is_public and not item['is_member']
            item['can_post'] = item['is_member']

            data.append(item)

        return create_response("Get Xircle list", data=data, total=total, page=page, limit=limit)


xircle_member_model = app_xircle_api.model('XircleMember', {
    'user_ids': fields.List(fields.String, required=False, description='User IDs')
})


## join, leave and get xircle member
@app_xircle_api.doc(security='bearer')
@app_xircle_api.route('/<string:id>/members', methods=['POST', 'DELETE', 'GET'])
class XircleMemberObject(Resource):
    def get(self, id):
        ## get xircle members
        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id, Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)
        
        members = xircle.members
        data = [m.to_dict(["id", "first_name", "last_name", "preferred_name", "image", "title"]) for m in members]

        return create_response("Get xircle members", data=data)

    @app_xircle_api.expect(xircle_member_model)
    def post(self, id):
        ## join a xircle
        data = request.json

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id, Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)

        user_ids = data.get('user_ids', [])
        
        # If no user_ids provided, assume self-join
        if not user_ids:
            if not xircle.is_public:
                return create_response("Cannot self-join a private xircle", status=403)
            user_ids = [g.user_id]
        
        # Check permissions based on Xircle type
        if not xircle.is_public and xircle.creator_id != g.user_id:
            return create_response("Only the creator can add members to a private xircle", status=403)
        
        ## get current xircle members
        members = xircle.members
        existing_member_ids = [m.id for m in members]
        
        # For self-join, check if already a member
        if len(user_ids) == 1 and user_ids[0] == g.user_id and g.user_id in existing_member_ids:
            return create_response("You are already a member of this xircle", status=400)

        added_members = []
        skipped_members = []
        for user_id in user_ids:
            ## check if user is already a member
            if user_id in existing_member_ids:
                user = g.client_db_session.query(User).filter(User.id==user_id).first()
                if user:
                    skipped_members.append(user.to_dict(['id', 'first_name', 'last_name', 'preferred_name']))
                continue

            # Verify user exists
            user = g.client_db_session.query(User).filter(User.id==user_id).first()
            if not user:
                continue

            xircle_member = XircleMember()
            xircle_member.xircle_id = id
            xircle_member.user_id = user_id
            g.client_db_session.add(xircle_member)
            added_members.append(user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title']))

        g.client_db_session.commit()

        invited_member_ids = [x['id'] for x in added_members if x['id'] != g.user_id]
        # Send notifications to added members
        if invited_member_ids and len(invited_member_ids) > 0:
            nf = NotificationFunction() 
            nf.new_xircle_notification(xircle.id, g.user_id, invited_member_ids)

        # Customize message based on operation type
        message = "Successfully joined the xircle" if len(user_ids) == 1 and user_ids[0] == g.user_id else "Members added to xircle"
        
        return create_response(
            message,
            data={
                'added_members': added_members,
                'skipped_members': skipped_members,
                'total_added': len(added_members)
            }
        )

    @app_xircle_api.expect(xircle_member_model)
    def delete(self, id):
        ## leave or remove members from a xircle
        data = request.json

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==id, Xircle.is_deleted==False).first()
        if xircle is None:
            return create_response("Xircle not found", status=404)

        user_ids = data.get('user_ids', [])
        
        # If no user_ids provided, assume self-leave
        if not user_ids:
            user_ids = [g.user_id]
        
        # Check if the user is actually a member before attempting to leave
        if len(user_ids) == 1 and user_ids[0] == g.user_id:
            member = g.client_db_session.query(XircleMember).filter(
                XircleMember.xircle_id==id,
                XircleMember.user_id==g.user_id
            ).first()
            if not member:
                return create_response("You are not a member of this xircle", status=400)

        removed_members = []
        skipped_members = []
        
        for user_id in user_ids:
            # Check if the member exists
            member = g.client_db_session.query(XircleMember).filter(
                XircleMember.xircle_id==id,
                XircleMember.user_id==user_id
            ).first()
            
            if not member:
                user = g.client_db_session.query(User).filter(User.id==user_id).first()
                if user:
                    skipped_members.append(user.to_dict(['id', 'first_name', 'last_name', 'preferred_name']))
                continue

            # Determine if the current user has permission to remove this member
            can_remove = (
                user_id == g.user_id or  # Users can always remove themselves
                xircle.creator_id == g.user_id  # Creator can remove anyone
            )

            if not can_remove:
                return create_response(
                    "You can only remove yourself from the xircle. Only the creator can remove other members.",
                    status=403
                )
            
            # If creator is leaving, transfer ownership to xapa admin
            if user_id == xircle.creator_id:
                xircle.creator_id = '00000000-0000-0000-0000-000000000000'

            user = g.client_db_session.query(User).filter(User.id==user_id).first()
            if user:
                removed_members.append(user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title']))
            
            g.client_db_session.delete(member)

        g.client_db_session.commit()

        # Customize message based on operation type
        message = "Successfully left the xircle" if len(user_ids) == 1 and user_ids[0] == g.user_id else "Members removed from xircle"
        if g.user_id == xircle.creator_id:
            message += ". Ownership transferred to Xapa Admin"

        return create_response(
            message,
            data={
                'removed_members': removed_members,
                'skipped_members': skipped_members,
                'total_removed': len(removed_members)
            }
        )


@app_xircle_api.doc(security='bearer')
@app_xircle_api.route('/xapa')
class XapaXircleObject(Resource):
    def get(self):
        # get xapa admin user
        user = g.client_db_session.query(User).filter(User.id=='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.client_db_session.add(user)
            g.client_db_session.commit()

        # get xapa xircle
        xircle = g.client_db_session.query(Xircle).filter(Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if not xircle:
            xircle = Xircle()
            xircle.name = 'Xapa'
            xircle.description = 'Global Xapa Community'
            xircle.is_public = True
            xircle.include_leaderboard = True
            xircle.creator_id = user.id
            g.client_db_session.add(xircle)
            g.client_db_session.commit()

        data = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard'])
        
        data['creator'] = None
        data['is_creator'] = False
        data['is_member'] = False
        data['can_invite'] = False
        data['can_delete'] = False
        data['can_edit'] = False
        data['can_leave'] = False
        data['can_post'] = False
        data['can_join'] = False
        data['member_count'] = 0
        data['member_preview'] = []

        return create_response("Get Xapa xircle", data=data)


@app_xircle_api.doc(security='bearer')
@app_xircle_api.route('/company')
class CompanyXircleObject(Resource):
    def get(self):
        # get xapa admin user
        user = g.client_db_session.query(User).filter(User.id=='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.client_db_session.add(user)
            g.client_db_session.commit()

        # get company xircle
        xircle = g.client_db_session.query(Xircle).filter(Xircle.id=='00000000-0000-0000-0000-000000000000', Xircle.is_deleted==False).first()
        if not xircle:
            xircle = Xircle()
            xircle.id = '00000000-0000-0000-0000-000000000000'
            xircle.name = 'Company'
            xircle.description = 'Company Community'
            xircle.is_public = True
            xircle.include_leaderboard = True
            xircle.creator_id = user.id
            g.client_db_session.add(xircle)
            g.client_db_session.commit()

        data = xircle.to_dict(['id', 'name', 'description', 'image', 'is_public', 'include_leaderboard'])
        
        data['creator'] = None
        data['is_creator'] = False
        data['is_member'] = False
        data['can_invite'] = False
        data['can_delete'] = False
        data['can_edit'] = False
        data['can_leave'] = False
        data['can_post'] = False
        data['can_join'] = False
        data['member_count'] = 0
        data['member_preview'] = []

        return create_response("Get Xapa xircle", data=data)
