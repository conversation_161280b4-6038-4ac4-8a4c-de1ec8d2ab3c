["tests/test_cms_asset_update.py::TestCMSAssetUpdate::test_asset_update_should_not_process_file_when_no_file_path", "tests/test_cms_asset_update.py::TestCMSAssetUpdate::test_asset_update_should_process_file_when_same_path_provided", "tests/test_cms_xperience_list.py::TestCMSXperienceList::test_imports_available", "tests/test_cms_xperience_list.py::TestCMSXperienceList::test_new_fields_structure", "tests/test_cms_xperience_list.py::TestCMSXperienceList::test_syntax_check", "tests/test_program_xperience_association.py::TestAPIFunctionality::test_program_model_includes_xperience_ids", "tests/test_program_xperience_association.py::TestAPIFunctionality::test_xperience_association_management", "tests/test_program_xperience_association.py::TestAPIFunctionality::test_xperience_ordering_endpoint", "tests/test_program_xperience_association.py::TestProgramXperienceAssociation::test_api_endpoints_documented", "tests/test_program_xperience_association.py::TestProgramXperienceAssociation::test_association_ordering", "tests/test_program_xperience_association.py::TestProgramXperienceAssociation::test_models_have_association_relationships", "tests/test_program_xperience_association.py::TestProgramXperienceAssociation::test_program_xperience_association_creation", "tests/test_quest_completion_integration.py::TestQuestCompletionWithXipCode::test_quest_completion_with_style_update", "tests/test_quest_completion_integration.py::TestQuestCompletionWithXipCode::test_quest_completion_without_style_update", "tests/test_user_style_service.py::TestUserStyleService::test_update_user_style_create_new_record", "tests/test_user_style_service.py::TestUserStyleService::test_update_user_style_empty_mapping_configuration", "tests/test_user_style_service.py::TestUserStyleService::test_update_user_style_invalid_json_mapping", "tests/test_user_style_service.py::TestUserStyleService::test_update_user_style_no_mapping", "tests/test_user_style_service.py::TestUserStyleService::test_update_user_style_no_matching_nodes", "tests/test_user_style_service.py::TestUserStyleService::test_update_user_style_update_existing_record"]