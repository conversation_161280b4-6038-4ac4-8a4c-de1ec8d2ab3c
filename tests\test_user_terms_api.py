import unittest
from unittest.mock import Mock, patch
from flask import <PERSON>lask, g

from api.app.user_terms import app_user_terms_api
from api.common.helper import create_response
from clientmodels import Client, UserAgreement


class TestUserTermsAPI(unittest.TestCase):
    def setUp(self):
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        
        # Mock the namespace
        self.api = app_user_terms_api
        
    @patch('api.app.user_terms.g')
    @patch('api.app.user_terms.Client')
    @patch('api.app.user_terms.UserAgreement')
    def test_get_user_terms_success_with_agreement(self, mock_user_agreement, mock_client_model, mock_g):
        """Test successful retrieval of user terms when user has agreed"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        mock_g.user_id = 'test-user-id'
        
        # Mock client data with AI preferences
        mock_client = Mock()
        mock_client.id = 'client-123'
        mock_client.settings = {
            'feature_configuration': {
                'ai': {
                    'enabled': True,
                    'description': 'Enabled AI',
                    'preferences': {
                        'agreement_title': 'AI Usage Agreement',
                        'agreement_terms_body': '<p>AI terms</p>',
                        'checkbox_label': 'I agree to AI terms',
                        'button_label': 'Accept AI',
                        'version': '1.0'
                    }
                }
            }
        }
        
        # Mock user agreement (user has agreed)
        mock_agreement = Mock()
        
        # Mock database queries
        def mock_query_side_effect(model):
            query_mock = Mock()
            filter_mock = Mock()
            query_mock.filter.return_value = filter_mock
            
            if model == Client:
                filter_mock.first.return_value = mock_client
            elif model == UserAgreement:
                filter_mock.first.return_value = mock_agreement
            
            return query_mock
        
        mock_db_session.query.side_effect = mock_query_side_effect
        
        # Import and instantiate the resource
        from api.app.user_terms import UserTerms
        resource = UserTerms()
        
        # Call the method
        response = resource.get('AI')
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['agreement_type'], 'AI')
        self.assertEqual(response_data['data']['agreement_title'], 'AI Usage Agreement')
        self.assertEqual(response_data['data']['user_agreement_status'], True)

    @patch('api.app.user_terms.g')
    @patch('api.app.user_terms.Client')
    @patch('api.app.user_terms.UserAgreement')
    def test_get_user_terms_success_without_agreement(self, mock_user_agreement, mock_client_model, mock_g):
        """Test successful retrieval of user terms when user has not agreed"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        mock_g.user_id = 'test-user-id'
        
        # Mock client data with AI preferences
        mock_client = Mock()
        mock_client.settings = {
            'feature_configuration': {
                'ai': {
                    'enabled': True,
                    'preferences': {
                        'agreement_title': 'AI Usage Agreement',
                        'agreement_terms_body': '<p>AI terms</p>',
                        'checkbox_label': 'I agree to AI terms',
                        'button_label': 'Accept AI',
                        'version': '1.0'
                    }
                }
            }
        }
        
        # Mock database queries
        def mock_query_side_effect(model):
            query_mock = Mock()
            filter_mock = Mock()
            query_mock.filter.return_value = filter_mock
            
            if model == Client:
                filter_mock.first.return_value = mock_client
            elif model == UserAgreement:
                filter_mock.first.return_value = None  # No agreement found
            
            return query_mock
        
        mock_db_session.query.side_effect = mock_query_side_effect
        
        # Import and instantiate the resource
        from api.app.user_terms import UserTerms
        resource = UserTerms()
        
        # Call the method
        response = resource.get('AI')
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['agreement_type'], 'AI')
        self.assertEqual(response_data['data']['user_agreement_status'], False)

    @patch('api.app.user_terms.g')
    @patch('api.app.user_terms.Client')
    def test_get_user_terms_fallback_to_global(self, mock_client_model, mock_g):
        """Test fallback to global client settings when local client is missing AI preferences"""
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        mock_g.user_id = 'test-user-id'
        
        # Mock client data without AI preferences
        mock_client = Mock()
        mock_client.settings = {
            'feature_configuration': {
                'ai': {
                    'enabled': True
                    # Missing preferences
                }
            }
        }
        
        # Mock global client with AI preferences
        mock_global_client = Mock()
        mock_global_client.settings = {
            'feature_configuration': {
                'ai': {
                    'preferences': {
                        'agreement_title': 'Global AI Agreement',
                        'agreement_terms_body': '<p>Global AI terms</p>',
                        'checkbox_label': 'Global AI checkbox',
                        'button_label': 'Global AI button',
                        'version': '1.0'
                    }
                }
            }
        }
        
        # Mock database queries
        call_count = 0
        def mock_query_side_effect(model):
            nonlocal call_count
            query_mock = Mock()
            filter_mock = Mock()
            query_mock.filter.return_value = filter_mock
            
            if model == Client:
                call_count += 1
                if call_count == 1:
                    filter_mock.first.return_value = mock_client
                else:
                    filter_mock.first.return_value = mock_global_client
            else:
                filter_mock.first.return_value = None
            
            return query_mock
        
        mock_db_session.query.side_effect = mock_query_side_effect
        
        # Import and instantiate the resource
        from api.app.user_terms import UserTerms
        resource = UserTerms()
        
        # Call the method
        response = resource.get('AI')
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['agreement_title'], 'Global AI Agreement')
        self.assertEqual(response_data['data']['user_agreement_status'], False)

    @patch('api.app.user_terms.request')
    @patch('api.app.user_terms.g')
    @patch('api.app.user_terms.Client')
    @patch('api.app.user_terms.UserAgreement')
    def test_post_user_agreement_success(self, mock_user_agreement_model, mock_client_model, mock_g, mock_request):
        """Test successful recording of user agreement"""
        
        # Mock request data
        mock_request.get_json.return_value = {'agreement_type': 'AI'}
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'test-tenant-id'
        mock_g.user_id = 'test-user-id'
        
        # Mock client data with AI preferences
        mock_client = Mock()
        mock_client.settings = {
            'feature_configuration': {
                'ai': {
                    'preferences': {
                        'agreement_title': 'AI Usage Agreement',
                        'agreement_terms_body': '<p>AI terms</p>',
                        'checkbox_label': 'I agree to AI terms',
                        'button_label': 'Accept AI',
                        'version': '1.0'
                    }
                }
            }
        }
        
        # Mock that no existing agreement exists
        def mock_query_side_effect(model):
            query_mock = Mock()
            filter_mock = Mock()
            query_mock.filter.return_value = filter_mock
            
            if model == Client:
                filter_mock.first.return_value = mock_client
            elif model == UserAgreement:
                filter_mock.first.return_value = None  # No existing agreement
            
            return query_mock
        
        mock_db_session.query.side_effect = mock_query_side_effect
        
        # Mock UserAgreement constructor
        mock_agreement_instance = Mock()
        mock_agreement_instance.id = 'agreement-123'
        mock_user_agreement_model.return_value = mock_agreement_instance
        
        # Import and instantiate the resource
        from api.app.user_terms import UserAgreementRecord
        resource = UserAgreementRecord()
        
        # Call the method
        response = resource.post()
        
        # Verify the result
        self.assertEqual(response.status_code, 200)
        response_data = response.get_json()
        self.assertEqual(response_data['data']['agreement_id'], 'agreement-123')
        
        # Verify that add and commit were called
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @patch('api.app.user_terms.request')
    @patch('api.app.user_terms.g')
    @patch('api.app.user_terms.Client')
    def test_post_user_agreement_client_not_found(self, mock_client_model, mock_g, mock_request):
        """Test posting agreement when client is not found"""
        
        # Mock request data
        mock_request.get_json.return_value = {'agreement_type': 'AI'}
        
        # Mock the flask g object
        mock_db_session = Mock()
        mock_g.db_session = mock_db_session
        mock_g.tenant_id = 'non-existent-tenant'
        mock_g.user_id = 'test-user-id'
        
        # Mock database query returning None for client
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        mock_filter.first.return_value = None
        mock_db_session.query.return_value = mock_query
        
        # Import and instantiate the resource
        from api.app.user_terms import UserAgreementRecord
        resource = UserAgreementRecord()
        
        # Call the method
        response = resource.post()
        
        # Verify the result
        self.assertEqual(response.status_code, 404)
        response_data = response.get_json()
        self.assertEqual(response_data['message'], 'Client not found')


if __name__ == '__main__':
    unittest.main()