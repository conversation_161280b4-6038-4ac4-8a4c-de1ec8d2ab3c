from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from clientmodels import MASTER_TENANT, Program, Tag, Xperience, Quest, User, Xircle
from clientmodels import Quest, PublishedQuest
from clientmodels import Client, ClientPackageAssociation, PackageXperienceAssociation, PackageProgramAssociation
from clientmodels import XperienceQuestAssociation, ProgramQuestAssociation, PublishedQuest


app_global_search_api = Namespace('api_app_global_search', description='Global Search related API')

search_model = app_global_search_api.model('Search', {
    'search': fields.String(required=True, description='Search term'),
})


@app_global_search_api.doc(security='bearer')
@app_global_search_api.route('/search')
class Search(Resource):
    @app_global_search_api.expect(search_model)
    def post(self):
        search = request.json.get('search', '')
        if not search:
            return create_response("Search term is required", status=400)

        data = []

        from sqlalchemy import func
        from api.app.xperience import get_xperience_query
        
        # xperiences = g.client_db_session.query(Xperience).filter(Xperience.status == 'Published').filter(Xperience.name.ilike(f'%{search}%')).all()
        query = get_xperience_query(search=search, user_id=None)
        xperience_results = None
        if query:
            xperience_results = query.all()
        
        if xperience_results:
            result = {
                'title': 'Xperience',
                'type': 'xperience',
            }
            items = []
            for xperience, published_xperience in xperience_results:
                xperience_dict = published_xperience.to_dict(['name', 'image'])
                xperience_dict['id'] = xperience.id
                xperience_dict['action'] = f'/xperiences/xperience/{xperience.id}'
                items.append(xperience_dict)

            result['items'] = items

            data.append(result)

        from api.app.program import get_program_query
        query = get_program_query(search=search, user_id=None)
        program_results = None

        if query:
            program_results = query.all()

        if program_results:
            result = {
                'title': 'Program',
                'type': 'program',
            }
            items = []
            for program, published_program in program_results:
                program_dict = program.to_dict(['name', 'image'])
                program_dict['id'] = program.id
                program_dict['action'] = f'/xperiences/program/{program.id}'
                items.append(program_dict)

            result['items'] = items

            data.append(result)


        ## Search for quests
        if g.tenant_id == MASTER_TENANT:
            latest_revision_subq = (
                g.db_session.query(
                    PublishedQuest.quest_id,
                    func.max(PublishedQuest.revision).label("max_revision")
                )
                .group_by(PublishedQuest.quest_id)
                .subquery()
            )

            quest_results = (
                g.db_session.query(Quest, PublishedQuest)
                .join(PublishedQuest, PublishedQuest.quest_id == Quest.id)
                .filter(
                    Quest.status == 'Ready',
                    (Quest.name.ilike(f'%{search}%') | Quest.tags.any(Tag.name.ilike(f'%{search}%')))
                )
                .join(latest_revision_subq, 
                    (PublishedQuest.quest_id == latest_revision_subq.c.quest_id) &
                    (PublishedQuest.revision == latest_revision_subq.c.max_revision))
            ).all()
               

        else:
            client_id = g.db_session.query(Client).filter(Client.id_key == g.tenant_id).first()
            client_package_ids = g.db_session.query(ClientPackageAssociation.package_id).filter(
                ClientPackageAssociation.client_id == client_id.id
            ).subquery()

            client_xperience_ids = g.db_session.query(PackageXperienceAssociation.xperience_id).filter(
                PackageXperienceAssociation.package_id.in_(client_package_ids.select())
            ).subquery()

            client_program_ids = g.db_session.query(PackageProgramAssociation.program_id).filter(
                PackageProgramAssociation.package_id.in_(client_package_ids.select())
            ).subquery()

            # Find quests linked to allowed xperiences or programs via association tables
            client_quest_ids = g.db_session.query(Quest.id).filter(
                Quest.status == 'Ready',
                (Quest.name.ilike(f'%{search}%') | Quest.tags.any(Tag.name.ilike(f'%{search}%')))
            ).filter(
                (Quest.id.in_(
                    g.db_session.query(XperienceQuestAssociation.quest_id).filter(
                        XperienceQuestAssociation.xperience_id.in_(client_xperience_ids.select())
                    )
                )) 
            ).subquery()

            quest_results = g.db_session.query(Quest, PublishedQuest).join(
                PublishedQuest, PublishedQuest.quest_id == Quest.id
            ).filter(
                PublishedQuest.is_latest == True,
                Quest.id.in_(client_quest_ids.select())
            ).all() 

        if quest_results:
            result = {
                'title': 'Quest',
                'type': 'quest',
            }
            items = []

            if g.tenant_id == MASTER_TENANT:
                client_xperience_ids = []
            else:
                client_xperience_ids = g.db_session.query(PackageXperienceAssociation.xperience_id).filter(
                    PackageXperienceAssociation.package_id.in_(
                        g.db_session.query(ClientPackageAssociation.package_id).filter(
                            ClientPackageAssociation.client_id == client_id.id
                        )
                    )
                ).all()
                client_xperience_ids = [x[0] for x in client_xperience_ids]
            
            for quest, publish_quest in quest_results:
                quest_dict = publish_quest.to_dict(['name', 'image'])
                quest_dict['id'] = quest.id
                quest_dict['action'] = f'/xperiences/quest/{quest.id}'
                ## get the quest first xperience
                xperiences = quest.xperiences
                if xperiences:
                    ## find the first xperience in client_xperience_ids
                    for xperience in xperiences:
                        if g.tenant_id == MASTER_TENANT or xperience.id in client_xperience_ids:
                            if xperience.status == 'Published':
                                quest_dict['action'] = f'/xperiences/xperience/{xperience.id}?quest_id={quest.id}'
                                items.append(quest_dict)
                                break

                else:
                    continue

            result['items'] = items

            data.append(result)

        users = g.client_db_session.query(User).filter(
            User.is_deleted == False,
            User.is_active == True,
            func.concat(User.first_name, ' ', User.last_name).ilike(f"%{search}%") | 
            User.email.ilike(f"%{search}%") | 
            User.preferred_name.ilike(f"%{search}%")
        ).all()
        if users:
            result = {
                'title': 'People',
                'type': 'user',
            }
            items = []
            for user in users:
                name = f"{user.first_name} {user.last_name}"
                preferred_name = user.preferred_name
                user_dict = {
                    'id': user.id,
                    'name': name if search.lower() in name.lower() else preferred_name,
                    'image': user.image
                }
                user_dict['action'] = f'/people/{user.id}'
                items.append(user_dict)

            result['items'] = items

            data.append(result)

        xircles = g.client_db_session.query(Xircle).filter(Xircle.is_public == True, Xircle.is_deleted == False).filter(Xircle.name.ilike(f'%{search}%')).all()
        if xircles:
            result = {
                'title': 'Xircle',
                'type': 'xircle',
            }
            items = []
            for xircle in xircles:
                xircle_dict = xircle.to_dict(['id', 'name', 'image'])
                xircle_dict['action'] = f'/community?xircle_id={xircle.id}#feed'
                items.append(xircle_dict)

            result['items'] = items

            data.append(result)
        
        return create_response("Search results retrieved successfully", data=data)