import hashlib
import json
import uuid
from io import BytesIO

from flask import request
from flask_restx import Namespace, Resource, fields

from app import cache
from api.common.helper import create_response
from app import storage
from clientmodels import Setting, get_db_session

cms_global_settings_api = Namespace('api_cms_global_settings', description='Global settings related operations')


def generate_version_id():
    """Generate a unique version ID using UUID and a shorter hash"""
    # Generate a UUID
    unique_id = str(uuid.uuid4())
    # Create a shorter hash (first 8 characters)
    short_hash = hashlib.sha256(unique_id.encode()).hexdigest()[:8]
    return short_hash


# Parser for list endpoint
global_settings_parser = cms_global_settings_api.parser()
global_settings_parser.add_argument('page', type=int, default=1, help='Page number')
global_settings_parser.add_argument('limit', type=int, default=20, help='Items per page')


version_model = cms_global_settings_api.model('CityVersion', {
    'content': fields.Raw(required=True, description='City JSON content')
})


@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/city/version')
class CityVersionList(Resource):
    @cms_global_settings_api.expect(global_settings_parser)
    def get(self):
        """Get list of city JSON versions"""
        
        versions = storage.list_files('global_settings/city/versions')
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        
        return create_response("City versions retrieved successfully", data=versions)

    @cms_global_settings_api.expect(version_model)
    @cache.invalidate_cache(pattern='global_setting:city')
    def post(self):
        """Create a new city JSON version"""
        data = request.json
        file_content = data.get('content')

        if not file_content:
            return create_response("File content is required", status=400)

        try:
            # Validate JSON content
            if isinstance(file_content, str):
                json_content = json.loads(file_content)
            else:
                json_content = file_content

            # Generate unique version ID
            version_id = generate_version_id()

            # Create version file name with timestamp
            filename = f"{version_id}.json"
            
            # Save to storage
            
            json_bytes = json.dumps(json_content).encode('utf-8')
            json_data = BytesIO(json_bytes)
            storage.upload_file(
                folder_name="global_settings/city/versions",
                file_name=filename,
                data=json_data
            )

            db_session = get_db_session()

            # Update settings table with latest version
            try:
                setting = db_session.query(Setting).filter(
                    Setting.key == 'current_city_version',
                    Setting.is_deleted == False
                ).first()
                
                if setting:
                    setting.value = version_id
                else:
                    setting = Setting(
                        key='current_city_version',
                        value=version_id,
                        description='Current city settings version'
                    )
                    db_session.add(setting)
                
                db_session.commit()
            except Exception as e:
                print(f"Error updating settings table: {str(e)}")
                # Don't fail the entire request if settings update fails
                pass
            
            return create_response("City version created successfully", 
                                data={"version": version_id, "filename": filename})
        except json.JSONDecodeError:
            return create_response("Invalid JSON content", status=400)
        except Exception as e:
            return create_response(f"Error creating version: {str(e)}", status=500)


@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/city/version/<string:version>')
class CityVersionItem(Resource):
    def get(self, version):
        """Get specific city JSON version"""
        
        files = storage.list_files('global_settings/city/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        data = storage.download_file('global_settings/city/versions', f"{version_file['name']}")
        content = json.loads(data.read().decode('utf-8'))
        return create_response("Version retrieved successfully", data={"version": version, "content": content})

    @cache.invalidate_cache(pattern='global_setting:city')
    def delete(self, version):
        """Delete a specific city JSON version"""
        
        files = storage.list_files('global_settings/city/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        storage.delete_file('global_settings/city/versions', f"{version_file['name']}")
        
        return create_response("City version deleted successfully")


school_version_model = cms_global_settings_api.model('SchoolVersion', {
    'content': fields.Raw(required=True, description='School JSON content')
})


@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/school/version')
class SchoolVersionList(Resource):
    @cms_global_settings_api.expect(global_settings_parser)
    def get(self):
        """Get list of school JSON versions"""
        
        versions = storage.list_files('global_settings/school/versions')
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        
        return create_response("School versions retrieved successfully", data=versions)

    @cms_global_settings_api.expect(school_version_model)
    @cache.invalidate_cache(pattern='global_setting:school')
    def post(self):
        """Create a new school JSON version"""
        data = request.json
        file_content = data.get('content')

        if not file_content:
            return create_response("File content is required", status=400)

        try:
            # Validate JSON content
            if isinstance(file_content, str):
                json_content = json.loads(file_content)
            else:
                json_content = file_content

            # Generate unique version ID
            version_id = generate_version_id()

            # Create version file name with timestamp
            filename = f"{version_id}.json"
            
            # Save to storage
            
            json_bytes = json.dumps(json_content).encode('utf-8')
            json_data = BytesIO(json_bytes)
            storage.upload_file(
                folder_name="global_settings/school/versions",
                file_name=filename,
                data=json_data
            )

            db_session = get_db_session()

            # Update settings table with latest version
            try:
                setting = db_session.query(Setting).filter(
                    Setting.key == 'current_school_version',
                    Setting.is_deleted == False
                ).first()
                
                if setting:
                    setting.value = version_id
                else:
                    setting = Setting(
                        key='current_school_version',
                        value=version_id,
                        description='Current school settings version'
                    )
                    db_session.add(setting)
                
                db_session.commit()
            except Exception as e:
                print(f"Error updating settings table: {str(e)}")
                # Don't fail the entire request if settings update fails
                pass
            
            return create_response("School version created successfully", 
                                data={"version": version_id, "filename": filename})
        except json.JSONDecodeError:
            return create_response("Invalid JSON content", status=400)
        except Exception as e:
            return create_response(f"Error creating version: {str(e)}", status=500)


@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/school/version/<string:version>')
class SchoolVersionItem(Resource):
    def get(self, version):
        """Get specific school JSON version"""
        
        files = storage.list_files('global_settings/school/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        data = storage.download_file('global_settings/school/versions', f"{version_file['name']}")
        content = json.loads(data.read().decode('utf-8'))
        return create_response("Version retrieved successfully", data={"version": version, "content": content})

    @cache.invalidate_cache(pattern='global_setting:school')
    def delete(self, version):
        """Delete a specific school JSON version"""
        
        files = storage.list_files('global_settings/school/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        storage.delete_file('global_settings/school/versions', f"{version_file['name']}")
        
        return create_response("School version deleted successfully")


@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/', methods=['GET', 'PUT'])
@cms_global_settings_api.route('/<string:key>', methods=['DELETE'])
class Settings(Resource):
    def get(self):
        """Get all settings"""
        db_session = get_db_session()
        settings = db_session.query(Setting).filter(Setting.is_deleted == False).all()
        
        def to_boolean(value):
            if isinstance(value, str):
                if value.lower() == 'true':
                    return True
                elif value.lower() == 'false':
                    return False
            return value

        result = {setting.key: to_boolean(setting.value) for setting in settings}
        return create_response("Settings retrieved successfully", data=result)

    @cache.invalidate_cache(pattern='global_setting:settings')
    def put(self):
        """Update settings"""
        data = request.json
        db_session = get_db_session()

        try:
            for key, value in data.items():
                setting = db_session.query(Setting).filter(
                    Setting.key == key,
                    Setting.is_deleted == False
                ).first()

                if setting:
                    setting.value = str(value)
                else:
                    setting = Setting(
                        key=key,
                        value=str(value)
                    )
                    db_session.add(setting)
            
            db_session.commit()
            return create_response("Settings updated successfully")
        except Exception as e:
            db_session.rollback()
            return create_response(f"Failed to update settings: {str(e)}", status=500)

    @cache.invalidate_cache(pattern='global_setting:settings')
    def delete(self, key):
        """Delete a specific setting by key"""
        if not key:
            return create_response("Key is required", status=400)

        db_session = get_db_session()

        try:
            setting = db_session.query(Setting).filter(
                Setting.key == key,
                Setting.is_deleted == False
            ).first()

            if not setting:
                return create_response("Setting not found", status=404)

            setting.is_deleted = True
            db_session.commit()
            return create_response("Setting deleted successfully")
        except Exception as e:
            db_session.rollback()
            return create_response(f"Failed to delete setting: {str(e)}", status=500)


@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/styles/list')
class GlobalStylesList(Resource):
    def get(self):
        """Get list of user styles"""
        from clientmodels import UserStyleMapping

        db_session = get_db_session()
        styles = db_session.query(UserStyleMapping).filter(
            UserStyleMapping.is_deleted == False
        ).all() 

        style_list = [style.to_dict() for style in styles]

        return create_response("Styles retrieved successfully", data=style_list)


style_model = cms_global_settings_api.model('Style', {
    'style_name': fields.String(required=True, description='Style name'),
    'mapping_quest_id': fields.String(required=True, description='Mapping quest ID'),
    'mapping_configuration': fields.List(fields.Nested(cms_global_settings_api.model('NodeBranches', {
        'node_id': fields.String(required=True, description='Node ID'),
        'value': fields.String(required=True, description='Style Value')
    })), required=False, description='Mapping configuration'),
})  

@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/styles/<string:style_id>', methods=['GET', 'DELETE', 'PUT'])
@cms_global_settings_api.route('/styles', methods=['POST'])

class GlobalStyleItem(Resource):
    def get(self, style_id):
        """Get specific user style"""
        from clientmodels import UserStyleMapping

        db_session = get_db_session()
        style = db_session.query(UserStyleMapping).filter(
            UserStyleMapping.id == style_id,
            UserStyleMapping.is_deleted == False
        ).first()

        if not style:
            return create_response("Style not found", status=404)

        return create_response("Style retrieved successfully", data=style.to_dict())
    
    @cache.invalidate_cache(pattern='global_setting:styles')
    def delete(self, style_id):
        """Delete a specific user style"""
        from clientmodels import UserStyleMapping

        db_session = get_db_session()
        style = db_session.query(UserStyleMapping).filter(
            UserStyleMapping.id == style_id,
            UserStyleMapping.is_deleted == False
        ).first()

        if not style:
            return create_response("Style not found", status=404)

        style.is_deleted = True
        db_session.commit()

        return create_response("Style deleted successfully")
    
    @cms_global_settings_api.expect(style_model)
    def put(self, style_id):
        """Update a specific user style"""
        from clientmodels import UserStyleMapping

        data = request.json
        style_name = data.get('style_name')
        mapping_quest_id = data.get('mapping_quest_id')

        if not style_name or not mapping_quest_id:
            return create_response("Required fields missing", status=400)

        db_session = get_db_session()
        style = db_session.query(UserStyleMapping).filter(
            UserStyleMapping.id == style_id,
            UserStyleMapping.is_deleted == False
        ).first()

        if not style:
            return create_response("Style not found", status=404)

        style.style_name = style_name
        style.mapping_configuration = json.dumps(data.get('mapping_configuration', []))
        style.mapping_quest_id = mapping_quest_id
        db_session.commit()

        return create_response("Style updated successfully", data=style.to_dict())
    

    @cms_global_settings_api.expect(style_model)
    def post(self):
        """Create a new user style"""
        from clientmodels import UserStyleMapping

        data = request.json
        style_name = data.get('style_name')
        mapping_quest_id = data.get('mapping_quest_id')

        if not style_name or not mapping_quest_id:
            return create_response("Required fields missing", status=400)

        db_session = get_db_session()
        new_style = UserStyleMapping(
            style_name=style_name,
            mapping_configuration=json.dumps(data.get('mapping_configuration', [])),
            mapping_quest_id=mapping_quest_id
        )
        db_session.add(new_style)
        db_session.commit()

        return create_response("Style created successfully", data=new_style.to_dict())
    
    

user_style_model = cms_global_settings_api.model('UserStyle', {
    'style_id': fields.String(required=True, description='Style mapping record id'),
})

@cms_global_settings_api.doc(security='bearer')
@cms_global_settings_api.route('/update_user_style', methods=['POST'])
class UpdateUserStyle(Resource):
    @cms_global_settings_api.expect(user_style_model)
    def post(self):
        """Update user style by quest ID"""
        from clientmodels import UserStyleMapping, UserStyle, User, UserNodeHistory

        data = request.json
        style_id = data.get('style_id')

        if not style_id:
            return create_response("Style ID is required", status=400)
        
        db_session = get_db_session()
        style = db_session.query(UserStyleMapping).filter(
            UserStyleMapping.id == style_id,
            UserStyleMapping.is_deleted == False
        ).first()

        if not style:
            return create_response("Style not found", status=404)
        
        ## Get Style mapping configuration
        mapping_configuration = json.loads(style.mapping_configuration) if style.mapping_configuration else []

        ## revert list to dict
        mapping_configuration_dict = {item['node_id']: item['value'] for item in mapping_configuration}

        ## Get Communication Style Id List
        communication_style_node_ids = list(mapping_configuration_dict.keys())

        # Search in the user node history table where node_id in communication_style_node_ids
        records = (
            db_session.query(UserNodeHistory, User)
            .join(User, UserNodeHistory.user_id == User.id)
            .filter(UserNodeHistory.node_id.in_(communication_style_node_ids))
            .order_by(UserNodeHistory.date_completed.desc())
            .all()
        )

        user_styles = {}
        for record, user in records:
            user_id = record.user_id
            if user_id not in user_styles:
                style_name = mapping_configuration_dict.get(record.node_id, "")
                if style_name:
                    user_style = db_session.query(UserStyle).filter(
                        UserStyle.user_id == user_id,
                        UserStyle.style_id == style.id,
                    ).first()

                    if not user_style:
                        user_style = UserStyle(
                            user_id=user_id,
                            style_id=style.id,
                            style_value=style_name
                        )
                        db_session.add(user_style)

                    else:
                        user_style.style_value = style_name

                    user_styles[user_id] = {
                        "user_id": user_id,
                        "first_name": user.first_name,
                        "last_name": user.last_name if user.last_name else "",
                        "style_id": style.id,
                        "style_name": style.style_name,
                        "style_value": style_name
                    }

        # Commit all changes to the database
        db_session.commit()

        data = list(user_styles.values())

        return create_response("Style updated successfully", data=data)
        