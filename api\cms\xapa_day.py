from flask import g, request
from flask_restx import Namespace, Resource, fields, reqparse
import werkzeug
from api.common.helper import create_response
from datetime import datetime, timedelta
import re

from clientmodels import XapaDay

cms_xapa_day_api = Namespace('api_cms_xapa_day', description='Xapa Day related operations')

xapa_day_model = cms_xapa_day_api.model('XapaDay', {
    'date': fields.String(required=True, description='Date for Xapa Day in YYYY-MM-DD format'),
    'text': fields.String(required=True, description='Quote for Xapa Day'),
})


@cms_xapa_day_api.doc(security='bearer')
@cms_xapa_day_api.route('/', methods=['POST'])
@cms_xapa_day_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class XapaDayItem(Resource):
    @cms_xapa_day_api.expect(xapa_day_model)
    def post(self):
        """
        Create or update a Xapa Day quote.
        """
        data = request.get_json()
        if not data:
            return create_response('No data provided', status=400)

        date = data.get('date')
        text = data.get('text')

        if not date or not text:
            return create_response('Both date and text are required', status=400)

        # Validate date format (YYYY-MM-DD)
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
            return create_response('Date must be in YYYY-MM-DD format', status=400)

        ## upsert the Xapa Day quote into the database
        item = g.client_db_session.query(XapaDay).filter_by(date=date, is_deleted=False).first()
        if item:
            # Update existing Xapa Day
            # item.text = text
            # g.client_db_session.add(item)
            
            ## also return the existing item id
            data = {
                "id": item.id
            }

            ## get the last Xapa Day quote
            last_xapa_day = g.client_db_session.query(XapaDay).filter_by(is_deleted=False).order_by(XapaDay.date.desc()).first()
            if last_xapa_day:
                ## return the next date for the next Xapa Day quote
                next_date = last_xapa_day.date + timedelta(days=1)
                data['last_date'] = next_date.strftime('%Y-%m-%d')
            else:
                ## if there is no last Xapa Day quote, set the next date to today
                data['last_date'] = datetime.now().strftime('%Y-%m-%d')

            return create_response("Quote already exist", data=data, status=400)
        else:
            # Create new Xapa Day
            item = XapaDay(date=date, text=text)
            g.client_db_session.add(item)

        try:
            g.client_db_session.commit()
            return create_response('Xapa Day quote created/updated successfully', status=200)
        except Exception as e:
            g.client_db_session.rollback()
            return create_response(f'Error saving Xapa Day quote: {str(e)}', status=500)
        

    def get(self, id):
        """
        Retrieve a Xapa Day quote by date.
        """
        if not id:
            return create_response('Date is required', status=400)

        item = g.client_db_session.query(XapaDay).filter_by(id=id, is_deleted=False).first()
        if not item:
            return create_response('Xapa Day quote not found', status=200)
        
        data = item.to_dict(["id", "date", "text"])

        return create_response("Xapa Day Retrived", data=data, status=200)
    

    @cms_xapa_day_api.expect(xapa_day_model)
    def put(self, id):
        """
        Update a Xapa Day quote by date.
        """
        if not id:
            return create_response('Date is required', status=400)

        data = request.get_json()
        if not data or 'text' not in data:
            return create_response('Text is required to update Xapa Day quote', status=400)

        item = g.client_db_session.query(XapaDay).filter_by(id=id, is_deleted=False).first()
        if not item:
            return create_response('Xapa Day quote not found', status=200)

        date = data.get('date')
        text = data.get('text')

        if not date or not text:
            return create_response('Both date and text are required', status=400)
        
        # Validate date format (YYYY-MM-DD)
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
            return create_response('Date must be in YYYY-MM-DD format', status=400)
        
        ## check if the date is already used by another Xapa Day quote
        existing_item = g.client_db_session.query(XapaDay).filter_by(date=date, is_deleted=False).first()
        if existing_item and existing_item.id != id:
            ## also return the existing item id
            data = {
                "id": existing_item.id
            }

            ## get the last Xapa Day quote
            last_xapa_day = g.client_db_session.query(XapaDay).filter_by(is_deleted=False).order_by(XapaDay.date.desc()).first()
            if last_xapa_day:
                ## return the next date for the next Xapa Day quote
                next_date = last_xapa_day.date + timedelta(days=1)
                data['last_date'] = next_date.strftime('%Y-%m-%d')
            else:
                ## if there is no last Xapa Day quote, set the next date to today
                data['last_date'] = datetime.now().strftime('%Y-%m-%d')

            return create_response('Date already exists for another Xapa Day quote', data=data, status=400)
        # Update the Xapa Day quote
        item.date = date
        item.text = text
        
        try:
            g.client_db_session.commit()
            return create_response('Xapa Day quote updated successfully', status=200)
        except Exception as e:
            g.client_db_session.rollback()
            return create_response(f'Error updating Xapa Day quote: {str(e)}', status=500)
        

    def delete(self, id):
        """
        Delete a Xapa Day quote by date.
        """
        if not id:
            return create_response('Date is required', status=400)

        item = g.client_db_session.query(XapaDay).filter_by(id=id).first()
        if not item:
            return create_response('Xapa Day quote not found', status=200)

        try:
            ## Update Xapa Day status to Deleted
            item.is_deleted = True
            g.client_db_session.add(item)
            g.client_db_session.commit()
            return create_response('Xapa Day quote deleted successfully', status=200)
        except Exception as e:
            g.client_db_session.rollback()
            return create_response(f'Error deleting Xapa Day quote: {str(e)}', status=500)
        



parser = reqparse.RequestParser()
## add pagination parameters
parser.add_argument('page', type=int, default=1, help='Page number for pagination')
parser.add_argument('limit', type=int, default=20, help='Number of items per page')
parser.add_argument('sort', type=str, choices=['date', 'text', 'date_updated'], default='date', help='Sort by field')
parser.add_argument('order', type=str, choices=['asc', 'desc'], default='asc', help='Sort order')
parser.add_argument('search', type=str, help='Search term for Xapa Day quotes')
parser.add_argument('start_date', type=str, help='Start date for filtering (YYYY-MM-DD)')
parser.add_argument('end_date', type=str, help='End date for filtering (YYYY-MM-DD)')
parser.add_argument('filter', type=str, help='Filter by specific criteria, e.g., "show_all=true" to show all quotes including old ones, by default only active quotes are shown')

@cms_xapa_day_api.doc(security='bearer')
@cms_xapa_day_api.route('/list', methods=['GET'])
class XapaDayList(Resource):
    @cms_xapa_day_api.expect(parser)
    def get(self):
        """
        Retrieve all Xapa Day quotes.
        """
        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        sort = args.get('sort', 'date')
        order = args.get('order', 'asc')
        search = args.get('search', '')
        start_date = args.get('start_date')
        end_date = args.get('end_date')
        filter_param = args.get('filter', '')
        show_all = 'show_all=true' in filter_param if filter_param else False

        query = g.client_db_session.query(XapaDay).filter_by(is_deleted=False)
        if not show_all:
            ## only filter active quotes, which date is future or today
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)
            query = query.filter((XapaDay.date >= yesterday) | (XapaDay.date == None))

        if search:
            query = query.filter(XapaDay.text.ilike(f'%{search}%'))

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(XapaDay.date >= start_date)
            except ValueError:
                return create_response('Invalid start date format. Use YYYY-MM-DD.', status=400)
            
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(XapaDay.date <= end_date)
            except ValueError:
                return create_response('Invalid end date format. Use YYYY-MM-DD.', status=400)
            
        if sort:
            if order == 'asc':
                query = query.order_by(getattr(XapaDay, sort).asc())
            else:
                query = query.order_by(getattr(XapaDay, sort).desc())

        ## get total
        total = query.count()

        xapa_days = query.offset((page - 1) * limit).limit(limit).all()

        if not xapa_days:
            return create_response('No Xapa Day quotes found', status=200)

        data = [x.to_dict(["id", "date", "text", "date_updated"]) for x in xapa_days]
        return create_response("Xapa Days Retrieved", data=data, status=200, total=total, page=page, limit=limit)
    

download_parser = reqparse.RequestParser()
download_parser.add_argument('sort', type=str, choices=['date'], default='date', help='Sort by field')
download_parser.add_argument('order', type=str, choices=['asc', 'desc'], default='asc', help='Sort order')
download_parser.add_argument('start_date', type=str, help='Start date for filtering (YYYY-MM-DD)')
download_parser.add_argument('end_date', type=str, help='End date for filtering (YYYY-MM-DD)')
download_parser.add_argument('filter', type=str, help='Filter by specific criteria, e.g., "show_all=true" to show all quotes including old ones, by default only active quotes are shown')

@cms_xapa_day_api.doc(security='bearer')
@cms_xapa_day_api.route('/download_csv', methods=['GET'])
## download the Xapa Day quotes as a CSV file, using the same query as in the list endpoint
class XapaDayDownloadCSV(Resource):
    @cms_xapa_day_api.expect(download_parser)
    def get(self):
        """
        Download all Xapa Day quotes as a CSV file.
        """
        from flask import make_response
        import csv
        from io import StringIO

        args = download_parser.parse_args()
        start_date = args.get('start_date')
        end_date = args.get('end_date')
        filter_param = args.get('filter', '')
        show_all = 'show_all=true' in filter_param if filter_param else False
        sort = args.get('sort', 'date')
        order = args.get('order', 'asc')

        query = g.client_db_session.query(XapaDay).filter_by(is_deleted=False)
        if not show_all:
            ## only filter active quotes, which date is future or today
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)
            query = query.filter((XapaDay.date >= yesterday) | (XapaDay.date == None))


        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(XapaDay.date >= start_date)
            except ValueError:
                return create_response('Invalid start date format. Use YYYY-MM-DD.', status=400)
            
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(XapaDay.date <= end_date)
            except ValueError:
                return create_response('Invalid end date format. Use YYYY-MM-DD.', status=400)
            
        if sort:
            if order == 'asc':
                query = query.order_by(getattr(XapaDay, sort).asc())
            else:
                query = query.order_by(getattr(XapaDay, sort).desc())

        xapa_days = query.all()
        
        if not xapa_days:
            return create_response('No Xapa Day quotes found', status=200)

        output = StringIO()
        writer = csv.writer(output)
        writer.writerow(['Date', 'Text'])

        for item in xapa_days:
            writer.writerow([item.date, item.text])

        output.seek(0)
        response = make_response(output.getvalue())
        response.headers['Content-Disposition'] = 'attachment; filename=xapa_days.csv'
        response.headers['Content-Type'] = 'text/csv'
        return response 
    

file_upload_parser = reqparse.RequestParser()
file_upload_parser.add_argument('file', type=werkzeug.datastructures.FileStorage, location='files', required=True, help='File to upload')

@cms_xapa_day_api.doc(security='bearer')
@cms_xapa_day_api.route('/upload_csv', methods=['POST'])
## Upload Xapa Day quotes from a CSV file.
class XapaDayUploadCSV(Resource):
    @cms_xapa_day_api.expect(file_upload_parser)
    def post(self):
        """
        Upload Xapa Day quotes from a CSV file.
        """
        from flask import request
        import csv
        from io import StringIO

        args = file_upload_parser.parse_args()
        file = args.get('file')

        if not file:
            return create_response('No file provided', status=400)
        
        if not isinstance(file, werkzeug.datastructures.FileStorage):
            return create_response('Invalid file type', status=400)
    
        if file.filename == '':
            return create_response('No selected file', status=400)

        if not file.filename.endswith('.csv'):
            return create_response('File must be a CSV', status=400)

        try:
            # Read the file content as text
            content = file.stream.read().decode('utf-8')
            if not content:
                return create_response('File is empty', status=400)
            
            csv_reader = csv.reader(StringIO(content))
            next(csv_reader, None)  # Skip header row if present

            for row in csv_reader:
                if len(row) != 2:
                    continue  # Skip invalid rows

                date, text = row

                ## Validate date format (mm/dd/yyyy)
                ## update for YYYY-MM-DD format
                date = date.strip()
                text = text.strip()
                if not date or not text:
                    print(f"Skipping row with invalid date or text: {row}")
                    continue

                ## update from mm/dd/yyyy to YYYY-MM-DD format
                
                try:
                    ## check what date format is used
                    if '/' in date:
                        # Assume mm/dd/yyyy format
                        date = datetime.strptime(date, '%m/%d/%Y').date()
                        date = date.strftime('%Y-%m-%d')  # Convert to YYYY-MM-DD format
                    else:
                        # Assume YYYY-MM-DD format
                        date = datetime.strptime(date, '%Y-%m-%d').date()
                        date = date.strftime('%Y-%m-%d')

                except ValueError:
                    print(f"Skipping row with invalid date format: {date}")
                    continue  # Skip invalid date format

                if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
                    ## skip invalid date format
                    print(f"Skipping row with invalid date format: {date}")
                    continue

                ## if text is empty, skip the row
                if not text:
                    print(f"Skipping row with empty text: {row}")
                    continue
                
                item = g.client_db_session.query(XapaDay).filter_by(date=date, is_deleted=False).first()
                if item:
                    item.text = text
                else:
                    item = XapaDay(date=date, text=text)
                    g.client_db_session.add(item)

            g.client_db_session.commit()
            return create_response('Xapa Day quotes uploaded successfully', status=200)
        except Exception as e:
            g.client_db_session.rollback()
            return create_response(f'Error uploading Xapa Day quotes: {str(e)}', status=500)