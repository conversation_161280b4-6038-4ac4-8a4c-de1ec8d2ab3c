<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[XAPA] Welcome to XAPA - Your Journey Begins!</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* Header */
        .header {
            text-align: center;
            padding: 30px 30px 20px;
            background-color: #ffffff;
        }
        
        .logo {
            max-width: 200px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        
        /* Content */
        .content {
            padding: 10px 30px 30px;
            color: #333333;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333333;
        }
        
        .intro-text {
            margin-bottom: 20px;
            font-size: 16px;
            color: #555555;
        }
        
        /* Features list */
        .features-box {
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #6700bb;
            border-radius: 4px;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features-list li {
            margin-bottom: 8px;
            font-size: 16px;
            color: #333333;
            padding-left: 5px;
        }
        
        
        /* CTA Button */
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .cta-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #6700bb;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        
        .cta-button:hover {
            background-color: #5200a0;
        }
        
        /* Login info box */
        .login-info {
            background-color: #e8f4f8;
            border: 1px solid #b3d9e8;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
            color: #2c5282;
        }
        
        /* Divider */
        .divider {
            border: none;
            border-top: 1px solid #dddddd;
        }
        
        /* Footer */
        .footer {
            padding: 20px 30px 30px;
            font-size: 14px;
            color: #666666;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        .footer p {
            margin-bottom: 15px;
        }
        
        .footer a {
            color: #6700bb;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .signature {
            font-weight: bold;
            margin-top: 20px;
            font-size: 16px;
            color: #333333;
        }
        
        .excitement {
            margin: 20px 0;
            font-size: 16px;
            color: #555555;
            text-align: center;
            font-style: italic;
        }
        
        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header,
            .content,
            .footer {
                padding-left: 20px;
                padding-right: 20px;
            }
            
            .logo {
                max-width: 150px;
            }
            
            .greeting {
                font-size: 16px;
            }
            
            .cta-button {
                display: block;
                text-align: center;
                margin: 20px auto;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header with Logo -->
        <div class="header">
            <img src="https://xapabackend.blob.core.windows.net/public/xapa.png" alt="Xapa" class="logo">
        </div>
        
        <!-- Main Content -->
        <div class="content">
            <div class="greeting">Hi {{ user_name }}!</div>
            
            <p class="intro-text">
                Great news! You've just unlocked access to <strong>XAPA</strong> — your all-in-one platform to level up your professional and personal growth!
            </p>
            
            <!-- Features List -->
            <div class="features-box">
                <ul class="features-list">
                    <li>✅ Gain exclusive insights</li>
                    <li>✅ Access powerful development tools</li>
                    <li>✅ Connect with like-minded professionals with Xircles</li>
                    <li>✅ Earn rewards as you grow!</li>
                </ul>
            </div>
            
            <!-- Call to Action Button -->
            <div class="cta-container">
                <a href="https://apps.apple.com/us/app/xapa-world/id6741739860" class="cta-button">Download the App Now</a>
            </div>
            
            <!-- Login Information -->
            <div class="login-info">
                Log in using your {{ user_email }} account
            </div>
            
            <!-- Excitement Message -->
            <div class="excitement">
                We can't wait for you to dive in and start your Xapa journey! 🚀
            </div>
        </div>
        
        <!-- Divider -->
        <hr class="divider">
        
        <!-- Footer -->
        <div class="footer">
            <p>
                Have questions? We've got you! Contact us at 
                <a href="mailto:<EMAIL>"><EMAIL></a>.
            </p>
            
            <div class="signature">
                <p>- The Xapa Team</p>
            </div>
            <br>
            <p style="font-size: 12px; color: #999999;">
                © Xapa World, Inc. | All Rights Reserved | Proprietary and Confidential
            </p>
        </div>
    </div>
</body>
</html>