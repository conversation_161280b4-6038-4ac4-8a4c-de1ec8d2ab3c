import datetime

from flask import g, json
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from clientmodels import Asset, Store, User, UserAsset, UserBoost, UserPowerUp, UserSpending, UserStats, UserStore

from api.app.badge import check_badge
from app import cache

app_store_api = Namespace('api_app_store', description='Store related operations')


@app_store_api.doc(security='bearer')
@app_store_api.route('/list')
class ListStores(Resource):
    @cache.cached(timeout=3600, key_prefix=lambda: f'{g.tenant_id}:store:list')
    def get(self):
        stores = g.db_session.query(Store).all()
        data = [store.to_dict() for store in stores]

        grouped_data = {}
        for store in data:
            store_type = store['store_type']
            
            ## if item type is destop, find the asset id from store item name with user login type
            if store['item_type'] == "desktop":
                user_login_type = g.user_login_type if g.user_login_type else "app"
                if user_login_type == "app":
                    name = store['name'] + " (Mobile)"
                else:
                    name = store['name'] + " (Desktop)"
                asset = g.db_session.query(Asset).filter_by(name=name).first()
                if asset:
                    store['asset_id'] = asset.id

            if store_type not in grouped_data:
                grouped_data[store_type] = []
                grouped_data[store_type].append(store)
            else:
                grouped_data[store_type].append(store)

        grouped_data_list = [{'type': store_type, 'items': sorted(stores, key=lambda x: x['index'])} for store_type, stores in grouped_data.items()]
        grouped_data = grouped_data_list

        data = grouped_data

        return create_response("Stores retrieved successfully", data=data)
    


@app_store_api.doc(security='bearer')
@app_store_api.route('/buy/<string:store_item_id>')
class BuyStoreItem(Resource):
    def post(self, store_item_id):
        user_id = g.user_id

        user = g.db_session.query(User).filter_by(id=user_id).first()
        if not user:
            return create_response("User not found", status=404)

        item_id = store_item_id

        item = g.db_session.query(Store).filter_by(id=item_id).first()
        if not item:
            return create_response("Item not found in store", success=False, status_code=404)
        
        ## get store item price and unit, check if user has enough resources
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)

        coins = user_stats.coins_count if user_stats.coins_count else 0
        gems = user_stats.gems_count if user_stats.gems_count else 0

        price = item.price
        unit = item.unit

        if unit == "coins":
            if coins < price:
                return create_response("Not enough coins", status=400)
            user_stats.coins_count = coins - price
            user_spending = UserSpending(user_id=user_id, coins=price, source="store", source_id=item.id)

        elif unit == "gems":
            if gems < price:
                return create_response("Not enough gems", status=400)
            user_stats.gems_count = gems - price
            user_spending = UserSpending(user_id=user_id, gems=price, source="store", source_id=item.id)

        else:
            return create_response("Invalid unit", status=400)
        
        # Record the spending in the UserSpending table
        g.db_session.add(user_spending)
        g.db_session.commit()

        msg = ""
        data = {}

        ## check item type
        item_type = item.item_type
        store_type = item.store_type
        if item_type == "powerup":
            msg, data = buy_powerup(item)
        elif item_type == "boost":
            msg, data = buy_boost(item)
        elif store_type == "digital_assets":
            msg, data = buy_digital_assets(item)
        elif store_type == "currency":
            msg, data = buy_currency(item)
        else:
            return create_response("Invalid item type", status=400)
        
        rewards = {'badges': []}
        if data:
            ## check store related badges
            first_store_purchase_badge = check_badge("", "first_store_purchase")
            if first_store_purchase_badge:
                rewards['badges'].append(first_store_purchase_badge)

        return create_response(msg, data=data, rewards=rewards)




def buy_powerup(store_item):
    user_id = g.user_id

    msg = ""
    data = {}

    user_powerup = g.db_session.query(UserPowerUp).filter(
        UserPowerUp.user_id == user_id,
        UserPowerUp.source == "store",
        UserPowerUp.source_id == store_item.id,
        UserPowerUp.end > datetime.datetime.utcnow()
    ).first()
    if user_powerup:
        msg = "Power Up already active and not yet ended"
        return msg, data
    
    addon_config = json.loads(store_item.config) if store_item.config else {}
    hours = addon_config.get("hours", None)
    if not hours:
        msg = "Invalid powerup"
        return msg, data
    try:
        hours = int(hours)
    except ValueError:
        msg = "Invalid powerup"
        return msg, data

    user_powerup = UserPowerUp(user_id=user_id)
    user_powerup.start = datetime.datetime.utcnow()
    user_powerup.end = datetime.datetime.utcnow() + datetime.timedelta(hours=hours)
    user_powerup.source = "store"
    user_powerup.source_id = store_item.id
    g.db_session.add(user_powerup)
    g.db_session.commit()
    data = user_powerup.to_dict()

    return msg, data



def buy_boost(store_item):
    user_id = g.user_id

    msg = ""
    data = {}

    user_boost = g.db_session.query(UserBoost).filter(
        UserBoost.user_id == user_id,
        UserBoost.source == "store",
        UserBoost.source_id == store_item.id,
        UserBoost.end > datetime.datetime.utcnow()
    ).first()
    if user_boost:
        msg = "Boost already active and not yet ended"
        return msg, data

    addon_config = json.loads(store_item.config) if store_item.config else {}
    hours = addon_config.get("hours", None)
    if not hours:
        msg = "Invalid boost"
        return msg, data
    
    try:
        hours = int(hours)
    except ValueError:
        msg = "Invalid boost"
        return msg, data

    ## add boost to user
    user_boost = UserBoost(user_id=user_id)
    user_boost.start = datetime.datetime.utcnow()
    user_boost.end = datetime.datetime.utcnow() + datetime.timedelta(hours=hours)
    user_boost.source = "store"
    user_boost.source_id = store_item.id
    g.db_session.add(user_boost)
    g.db_session.commit()

    msg = "Boost purchased successfully"
    data = user_boost.to_dict()

    return msg, data



def buy_digital_assets(store_item):
    user_id = g.user_id

    msg = ""
    data = {}

    asset_id = store_item.asset_id
    ## if store item is desktop, find the asset id from store item name with user login type
    user_login_type = g.user_login_type if g.user_login_type else "app"
    if store_item.item_type == "desktop":
        if user_login_type == "app":
            name = store_item.name + " (Mobile)"
        else:
            name = store_item.name + " (Desktop)"
        asset = g.db_session.query(Asset).filter_by(name=name).first()
        if asset:
            asset_id = asset.id

    if not asset_id:
        msg = "Invalid digital asset"
        return msg, data
    
    asset = g.db_session.query(Asset).filter_by(id=asset_id).first()
    if not asset:
        msg = "Digital asset not found"
        return msg, data

    user_store = g.db_session.query(UserStore).filter_by(user_id=user_id, store_item_id=store_item.id).first()
    if user_store:
        msg = "Asset already owned"
        return msg, data

    user_store = UserStore(user_id=user_id, store_item_id=store_item.id)
    g.db_session.add(user_store)
    g.db_session.commit()

    user_asset = g.db_session.query(UserAsset).filter_by(user_id=user_id, asset_id=asset_id).first()
    if not user_asset:
        user_asset = UserAsset(user_id=user_id, asset_id=asset_id)
        g.db_session.add(user_asset)
        g.db_session.commit()

    msg = "Digital asset purchased successfully"
    data = user_asset.to_dict()

    return msg, data



def buy_currency(store_item):
    user_id = g.user_id

    user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
    if not user_stats:
        user_stats = UserStats(user_id=user_id)
        g.db_session.add(user_stats)

    coins = user_stats.coins_count if user_stats.coins_count else 0
    gems = user_stats.gems_count if user_stats.gems_count else 0

    price = store_item.price
    unit = store_item.unit

    config = json.loads(store_item.config) if store_item.config else {}
    get_unit = config.get("unit", None)
    get_value = config.get("value", None)

    if not get_unit or not get_value:
        return "Invalid currency", {}

    if get_unit == "coins":
        user_stats.coins_count = coins + get_value
    elif get_unit == "gems":
        user_stats.gems_count = gems + get_value
    else:
        return "Invalid unit", {}
    
    ## add spending to spending table
    if unit == "coins":
        user_spending = UserSpending(user_id=user_id, coins=price, source="store", source_id=store_item.id)
        g.db_session.add(user_spending)
    elif unit == "gems":
        user_spending = UserSpending(user_id=user_id, gems=price, source="store", source_id=store_item.id)
        g.db_session.add(user_spending)

    g.db_session.commit()

    data = user_stats.to_dict()
    return "Currency purchased successfully", data