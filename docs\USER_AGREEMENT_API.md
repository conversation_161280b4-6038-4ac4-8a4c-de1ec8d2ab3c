# User Agreement API Documentation

This document describes the new User Agreement API endpoints that allow tracking user acceptance of terms and conditions.

## Overview

The User Agreement system tracks when users agree to various types of terms (e.g., AI usage terms). It supports:

- Multiple agreement types (AI, Privacy, etc.)
- Version tracking
- Client-specific and global fallback configurations
- User agreement status tracking

## Database Schema

### UserAgreement Table

| Field | Type | Description |
|-------|------|-------------|
| id | String(36) | Primary key (UUID) |
| user_id | String(36) | Foreign key to user.id |
| client_id | String(36) | Client ID (NULL for global) |
| terms_type | String(255) | Type of agreement (e.g., "AI") |
| terms_version | String(255) | Version of the terms (e.g., "1.0") |
| date_created | DateTime | When the agreement was created |
| date_updated | DateTime | When the agreement was last updated |
| is_deleted | Boolean | Soft delete flag |
| status | String(255) | Record status |

## API Endpoints

### GET /api/app/user-terms/terms/{terms_type}

Retrieves the current terms information and user agreement status for a specific terms type.

**Parameters:**
- `terms_type` (path): The type of terms to retrieve (e.g., "AI")

**Headers:**
- `Authorization: Bearer <JWT_TOKEN>`

**Response:**
```json
{
  "status": "success",
  "message": "Terms retrieved successfully",
  "data": {
    "agreement_type": "AI",
    "agreement_title": "AI Usage Agreement",
    "agreement_terms_body": "<p>By using AI features, you agree to our terms and conditions. <a href='#' target='_blank'>View full terms (PDF)</a></p>",
    "checkbox_label": "I agree to the AI usage terms and conditions",
    "button_label": "Accept and Continue",
    "user_agreement_status": false
  }
}
```

**Field Descriptions:**
- `agreement_type`: The type of agreement requested
- `agreement_title`: Title to display in the UI
- `agreement_terms_body`: HTML content of the terms
- `checkbox_label`: Text for the agreement checkbox
- `button_label`: Text for the acceptance button
- `user_agreement_status`: `true` if user has agreed to current version, `false` otherwise

### POST /api/app/user-terms/agreement

Records that a user has agreed to specific terms.

**Headers:**
- `Authorization: Bearer <JWT_TOKEN>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "agreement_type": "AI"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Agreement recorded successfully",
  "data": {
    "agreement_id": "agreement-uuid-here"
  }
}
```

## Configuration

Terms configurations are stored in the client settings under `feature_configuration.{terms_type}.preferences`:

```json
{
  "feature_configuration": {
    "ai": {
      "enabled": true,
      "description": "Enabled AI",
      "preferences": {
        "agreement_title": "AI Usage Agreement",
        "agreement_terms_body": "<p>By using AI features, you agree to our terms and conditions. <a href='#' target='_blank'>View full terms (PDF)</a></p>",
        "checkbox_label": "I agree to the AI usage terms and conditions",
        "button_label": "Accept and Continue",
        "version": "1.0"
      }
    }
  }
}
```

### Global Fallback

If a client doesn't have complete terms configuration, the system automatically falls back to the "global" client settings. This ensures all required fields are available.

## Usage Flow

1. **Check Agreement Status**: Call `GET /api/app/user-terms/terms/AI` to get current terms and user status
2. **Display Terms**: Show the agreement UI if `user_agreement_status` is `false`
3. **Record Agreement**: When user accepts, call `POST /api/app/user-terms/agreement` with the agreement type
4. **Update UI**: The `user_agreement_status` will be `true` on subsequent calls

## Error Responses

- `404 Client not found`: The client specified in the JWT token doesn't exist
- `404 Settings for {terms_type} not found`: No configuration found for the terms type
- `404 Preferences for {terms_type} not found`: Configuration exists but preferences are missing
- `400 agreement_type is required`: Missing required field in POST request
- `500 Internal Server Error`: Unexpected server error

## Testing

### Manual Testing Steps

1. Set up a client with AI preferences in the settings
2. Authenticate and get a JWT token
3. Test the GET endpoint to retrieve terms
4. Verify `user_agreement_status` is `false` initially
5. Test the POST endpoint to record agreement
6. Test the GET endpoint again to verify `user_agreement_status` is now `true`
7. Test with a client that has no AI preferences to verify global fallback

### Test Client Configuration

Example global client settings for testing:

```json
{
  "feature_configuration": {
    "ai": {
      "enabled": true,
      "description": "Enabled AI",
      "preferences": {
        "agreement_title": "AI Usage Agreement",
        "agreement_terms_body": "<p>By using AI features, you agree to our terms and conditions. <a href='#' target='_blank'>View full terms (PDF)</a></p>",
        "checkbox_label": "I agree to the AI usage terms and conditions",
        "button_label": "Accept and Continue",
        "version": "1.0"
      }
    }
  }
}
```

## Migration

The database migration `aa5ae284aba8_add_user_agreement_table.py` creates the required table. Run it with:

```bash
alembic upgrade head
```