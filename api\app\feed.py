
from flask import g, request
from flask_restx import Namespace, Resource, fields, reqparse

from api.common.helper import create_response, format_datetime_with_timezone, parse_mentions
from api.common.notification import NotificationFunction
from api.common.file import FileService

from clientmodels import Feed, FeedComment, FeedLike, FeedFlag, Xircle, XircleMember, User, FeedSeniority, \
    UserStreak, UserBlock

from api.app.badge import check_badge

app_feed_api = Namespace('api_app_feed', description='Feed related operations')


## feed model
feed_model = app_feed_api.model('FeedItem', {
    'message': fields.String(required=True, description='Feed Message'),
    'image': fields.String(required=False, description='Feed Image'),
    'media': fields.String(required=False, description='Feed Media, Video'),
    'xircle_id': fields.String(required=False, description='Xircle Id, optional'),
})

## create, edit, delete and get feed
@app_feed_api.doc(security='bearer')
@app_feed_api.route('/', methods=['POST'])
@app_feed_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class FeedObject(Resource):
    def get(self, id):
        """Get a feed"""
        from sqlalchemy import func, or_, and_, not_

        # Subqueries for counts and interactions
        likes_count = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('likes_count')
            )
            .filter(FeedLike.feed_id == id)
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        comments_count = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('comments_count')
            )
            .filter(FeedComment.feed_id == id, FeedComment.is_deleted == False)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        flags_count = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('flags_count')
            )
            .filter(FeedFlag.feed_id == id)
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        has_liked = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('has_liked')
            )
            .filter(
                FeedLike.feed_id == id,
                FeedLike.user_id == g.user_id
            )
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        has_commented = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('has_commented')
            )
            .filter(
                FeedComment.feed_id == id,
                FeedComment.user_id == g.user_id,
                FeedComment.is_deleted == False
            )
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        has_flagged = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('has_flagged')
            )
            .filter(
                FeedFlag.feed_id == id,
                FeedFlag.user_id == g.user_id
            )
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        # Subquery for xircle creators
        xircle_creators = (
            g.client_db_session.query(
                Xircle.id.label('xircle_id'),
                Xircle.creator_id
            ).filter(Xircle.is_deleted == False).subquery()
        )

        # Main query combining all subqueries
        feed_query = (
            g.client_db_session.query(
                Feed,
                User,
                Xircle,
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count'),
                func.coalesce(has_liked.c.has_liked > 0, False).label('has_liked'),
                func.coalesce(has_commented.c.has_commented > 0, False).label('has_commented'),
                func.coalesce(has_flagged.c.has_flagged > 0, False).label('has_flagged'),
                (Feed.user_id == g.user_id).label('can_edit'),  # Only feed owner can edit
                or_(
                    Feed.user_id == g.user_id,  # Feed owner can delete
                    and_(
                        Feed.xircle_id.isnot(None),
                        xircle_creators.c.xircle_id == Feed.xircle_id,
                        xircle_creators.c.creator_id == g.user_id  # Xircle owner can delete
                    )
                ).label('can_delete'),
                (not_(Xircle.name.ilike('xapa'))).label('can_comment')  # Cannot comment if xircle is Xapa
            )
            .join(User, Feed.user_id == User.id)
            .join(Xircle, Feed.xircle_id == Xircle.id)
            .outerjoin(likes_count, Feed.id == likes_count.c.feed_id)
            .outerjoin(flags_count, Feed.id == flags_count.c.feed_id)
            .outerjoin(comments_count, Feed.id == comments_count.c.feed_id)
            .outerjoin(has_liked, Feed.id == has_liked.c.feed_id)
            .outerjoin(has_commented, Feed.id == has_commented.c.feed_id)
            .outerjoin(has_flagged, Feed.id == has_flagged.c.feed_id)
            .outerjoin(xircle_creators, Feed.xircle_id == xircle_creators.c.xircle_id)
            .filter(Feed.id == id, Feed.is_deleted == False, Feed.status == 'active', Xircle.is_deleted == False)
            .first()
        )

        if feed_query is None:
            return create_response("Feed not found", status=404)
        
        feed, user, xircle, likes, flags, comments, has_liked, has_commented, has_flagged, can_edit, can_delete, can_comment = feed_query
        
        data = feed.to_dict(['id', 'message', 'image', 'media', 'date_created', 'date_updated', 'link', 'link_type', 'label'])
        data['likes_count'] = int(likes)
        data['flags_count'] = int(flags)
        data['comments_count'] = int(comments)
        data['has_liked'] = bool(has_liked)
        data['has_commented'] = bool(has_commented)
        data['has_flagged'] = bool(has_flagged)
        data['can_edit'] = bool(can_edit)  # Only feed owner can edit
        data['can_delete'] = bool(can_delete)  # Both feed owner and xircle owner can delete
        data['can_comment'] = bool(can_comment)  # Cannot comment if xircle is Xapa
        data['user'] = user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title'])
        data['xircle'] = xircle.to_dict(['id', 'name', 'image'])

        return create_response("Get feed", data=data)

    @app_feed_api.expect(feed_model)
    def post(self):
        """Create a new feed"""
        data = request.json

        message = data.get('message', '').strip()
        image = data.get('image', '')
        media = data.get('media', '')
        xircle_id = data.get('xircle_id', None)

        # Check Xircle permissions if posting to a Xircle
        if xircle_id:
            xircle = g.client_db_session.query(Xircle).filter(Xircle.id==xircle_id, Xircle.is_deleted==False).first()
            if not xircle:
                return create_response("Xircle not found", status=404)

            if xircle.name.lower() == 'xapa':
                return create_response("You cannot post to Xapa", status=403)
            
            # Check if user is a member of the Xircle
            is_member = g.client_db_session.query(XircleMember).filter(
                XircleMember.xircle_id==xircle_id,
                XircleMember.user_id==g.user_id
            ).first() is not None

            if not is_member:
                return create_response("You must be a member of the Xircle to post", status=403)

        feed = Feed()
        feed.message = message
        feed.xircle_id = xircle_id

        ## creator is the current user
        feed.user_id = g.user_id

        ## save image
        ## move the image from the temp folder to user's feed folder
        FileService.process_entity_image(feed, image, 'feed', g.user_id)

        ## save media
        ## move the media from the temp folder to user's feed folder
        # TODO: save media

        g.client_db_session.add(feed)
        g.client_db_session.commit()

        # Update xircle's date_updated if feed is posted to a xircle
        nf = NotificationFunction()
        nf.new_feed_notification(xircle.id, feed.id, g.user_id)

        # Parse and send notifications to mentioned users
        mentioned_user_ids = parse_mentions(message)
        if mentioned_user_ids:
            if xircle_id:
                # Filter mentions to only include xircle members
                xircle_member_ids = [
                    member.user_id for member in g.client_db_session.query(XircleMember).filter(
                        XircleMember.xircle_id == xircle_id,
                        XircleMember.user_id.in_(mentioned_user_ids)
                    ).all()
                ]
                if xircle_member_ids:
                    nf = NotificationFunction()
                    nf.new_feed_mention_notification(feed.id, g.user_id, xircle_member_ids)

        # Parse and send notifications to mentioned users
        mentioned_user_ids = parse_mentions(message)
        if mentioned_user_ids:
            if xircle_id:
                # Filter mentions to only include xircle members
                xircle_member_ids = [
                    member.user_id for member in g.client_db_session.query(XircleMember).filter(
                        XircleMember.xircle_id == xircle_id,
                        XircleMember.user_id.in_(mentioned_user_ids)
                    ).all()
                ]
                if xircle_member_ids:
                    nf = NotificationFunction()
                    nf.new_feed_mention_notification(feed.id, g.user_id, xircle_member_ids)

        ## check xircle related badge
        rewards = {'badges': []}
        first_xircle_post_badge = check_badge('', 'first_xircle_post')
        if first_xircle_post_badge:
            rewards['badges'].append(first_xircle_post_badge)

        data = feed.to_dict()
        return create_response("Feed created", data=data, rewards=rewards)

    @app_feed_api.expect(feed_model)
    def put(self, id):
        """Update a feed"""
        data = request.json

        feed = g.client_db_session.query(Feed).filter(Feed.id==id, Feed.is_deleted==False).first()
        if feed is None:
            return create_response("Feed not found", status=404)

        # Check if user has edit permission (feed creator or xircle owner)
        can_edit = feed.user_id == g.user_id  # User is feed creator
        if feed.xircle_id:  # If feed belongs to a xircle
            xircle = g.client_db_session.query(Xircle).filter(Xircle.id==feed.xircle_id, Xircle.is_deleted==False).first()
            if xircle:
                can_edit = can_edit or xircle.creator_id == g.user_id  # User is xircle owner

        if not can_edit:
            return create_response("You don't have permission to edit this feed", status=403)

        message = data.get('message', '').strip()
        image = data.get('image', '')
        media = data.get('media', '')

        feed.message = message

        ## save image
        ## move the image from the temp folder to user's feed folder
        FileService.process_entity_image(feed, image, 'feed', g.user_id)

        ## save media
        ## TODO: save media

        g.client_db_session.add(feed)
        g.client_db_session.commit()

        # Parse mentions from updated message and send notifications
        mentioned_user_ids = parse_mentions(message)
        if mentioned_user_ids:
            if feed.xircle_id:
                # Filter mentions to only include xircle members
                xircle_member_ids = [
                    member.user_id for member in g.client_db_session.query(XircleMember).filter(
                        XircleMember.xircle_id == feed.xircle_id,
                        XircleMember.user_id.in_(mentioned_user_ids)
                    ).all()
                ]
                if xircle_member_ids:
                    nf = NotificationFunction()
                    nf.new_feed_mention_notification(feed.id, g.user_id, xircle_member_ids)

        data = feed.to_dict()
        return create_response("Feed updated", data=data)
    
    def delete(self, id):
        """Delete a feed"""
        feed = g.client_db_session.query(Feed).filter(Feed.id==id, Feed.is_deleted==False).first()
        if feed is None:
            return create_response("Feed not found", status=404)

        # Check if user has delete permission (feed creator or xircle owner)
        deletable = feed.user_id == g.user_id  # User is feed creator
        if feed.xircle_id:  # If feed belongs to a xircle
            xircle = g.client_db_session.query(Xircle).filter(Xircle.id==feed.xircle_id, Xircle.is_deleted==False).first()
            if xircle:
                deletable = deletable or xircle.creator_id == g.user_id  # User is xircle owner

        if not deletable:
            return create_response("You don't have permission to delete this feed", status=403)

        # Soft delete all comments first
        comments = g.client_db_session.query(FeedComment).filter(FeedComment.feed_id==id, FeedComment.is_deleted==False).all()
        for comment in comments:
            comment.delete()
        
        # Soft delete the feed
        feed.delete()

        # Commit all changes
        g.client_db_session.commit()

        return create_response("Feed deleted")


## feed comment model
feed_comment_model = app_feed_api.model('FeedCommentItem', {
    'message': fields.String(required=True, description='Feed Comment Message')
})

comment_parser = reqparse.RequestParser()
comment_parser.add_argument('page', type=int, help='Page Number', default=1)
comment_parser.add_argument('limit', type=int, help='Limit per page', default=20)


## create, edit, delete and get feed comment
@app_feed_api.doc(security='bearer')
@app_feed_api.route('/<string:feed_id>/comment', methods=['POST'])
@app_feed_api.route('/<string:feed_id>/comment/<string:id>', methods=['DELETE'])
@app_feed_api.route('/<string:feed_id>/comment/list', methods=['GET'])
class FeedCommentObject(Resource):
    @app_feed_api.expect(comment_parser)
    def get(self, feed_id):
        """Get list of comments for a feed"""
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        offset = (page - 1) * limit

        # Query comments with user information
        feed_comments_query = (
            g.client_db_session.query(FeedComment, User)
            .outerjoin(User, FeedComment.user_id == User.id)
            .filter(FeedComment.feed_id == feed_id, FeedComment.is_deleted == False)
        )

        total = feed_comments_query.count()
        feed_comments = feed_comments_query.order_by(FeedComment.date_created.desc()).limit(limit).offset(offset).all()
        
        data = []
        for comment, user in feed_comments:
            comment_dict = comment.to_dict(['id', 'message', 'date_created', 'date_updated'])
            comment_dict['can_delete'] = comment.user_id == g.user_id
            comment_dict['user'] = user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title'])
            data.append(comment_dict)

        return create_response("Feed Comment list", data=data, total=total, page=page, limit=limit)

    @app_feed_api.expect(feed_comment_model)
    def post(self, feed_id):
        """Create a new feed comment"""
        data = request.json

        message = data.get('message', '').strip()

        feed_comment = FeedComment()
        feed_comment.message = message
        feed_comment.feed_id = feed_id

        # creator is the current user
        feed_comment.user_id = g.user_id

        g.client_db_session.add(feed_comment)
        g.client_db_session.commit()

        # Send notification to the feed creator
        nf = NotificationFunction()
        nf.new_feed_comment_notification(feed_id, g.user_id)

        # Send notification to users who have previously commented on this feed
        nf.new_comment_thread_notification(feed_id, g.user_id)

        # Parse and send notifications to mentioned users
        mentioned_user_ids = parse_mentions(message)
        if mentioned_user_ids:
            # Get the feed to check if it belongs to a xircle
            feed = g.client_db_session.query(Feed).filter(Feed.id == feed_id, Feed.is_deleted == False).first()
            if feed and feed.xircle_id:
                # Filter mentions to only include xircle members
                xircle_member_ids = [
                    member.user_id for member in g.client_db_session.query(XircleMember).filter(
                        XircleMember.xircle_id == feed.xircle_id,
                        XircleMember.user_id.in_(mentioned_user_ids)
                    ).all()
                ]
                if xircle_member_ids:
                    nf.new_mention_notification(feed_id, g.user_id, xircle_member_ids)

        ## check feed comment related badge
        rewards = {'badges': []}
        first_xircle_post_comment_badge = check_badge('', 'first_xircle_post_comment')
        if first_xircle_post_comment_badge:
            rewards['badges'].append(first_xircle_post_comment_badge)

        data = feed_comment.to_dict()
        return create_response("Feed Comment created", data=data, rewards=rewards)

    def delete(self, feed_id, id):
        """Delete a feed comment"""
        feed_comment = g.client_db_session.query(FeedComment).filter(FeedComment.id==id, FeedComment.is_deleted==False).first()
        if feed_comment is None:
            return create_response("Feed Comment not found", status=404)

        ## user can only delete their own comment
        if feed_comment.user_id != g.user_id:
            return create_response("Unauthorized to delete comment", status=422)
        
        feed_comment.delete()
        g.client_db_session.commit()

        return create_response("Feed Comment deleted")


## create, delete and get feed like
@app_feed_api.doc(security='bearer')
@app_feed_api.route('/<string:feed_id>/like', methods=['PUT'])
class FeedLikeObject(Resource):
    def put(self, feed_id):
        """Toggle like/unlike for a feed"""
        # Check if user has already liked the feed
        existing_like = g.client_db_session.query(FeedLike).filter(
            FeedLike.feed_id==feed_id,
            FeedLike.user_id==g.user_id
        ).first()

        if existing_like:
            # Unlike: Remove the like
            g.client_db_session.delete(existing_like)
            action = "unliked"
        else:
            # Like: Create new like
            new_like = FeedLike(
                feed_id=feed_id,
                user_id=g.user_id
            )
            g.client_db_session.add(new_like)
            action = "liked"

        g.client_db_session.commit()

        # Get updated likes count
        likes_count = g.client_db_session.query(FeedLike).filter(FeedLike.feed_id==feed_id).count()

        return create_response(
            f"Feed {action}",
            data={
                "has_liked": action == "liked",
                "likes_count": likes_count
            }
        )

## get feed list
parser = reqparse.RequestParser()
parser.add_argument('page', type=int, help='Page Number', default=1)
parser.add_argument('limit', type=int, help='Limit per page', default=20)
parser.add_argument('search', type=str, help='Search query', default='')
parser.add_argument('xircle_id', type=str, help='Xircle Id, optional')

@app_feed_api.doc(security='bearer')
@app_feed_api.route('/list', methods=['GET'])
class FeedList(Resource):
    @app_feed_api.expect(parser)
    def get(self):
        """Get list of feeds"""
        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        offset = (page - 1) * limit
        search = args.get('search', '')
        xircle_id = args.get('xircle_id', None)

        from sqlalchemy import func, or_, and_, desc, case, not_

        # Subquery for total likes count
        likes_count = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('likes_count')
            )
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        # Subquery for total flags count
        flags_count = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('flags_count')
            )
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        # Subquery for total comments count
        comments_count = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('comments_count')
            ).filter(FeedComment.is_deleted == False)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        # Check if current user has liked each feed
        has_liked = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('has_liked')
            )
            .filter(FeedLike.user_id == g.user_id)
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        # Check if current user has commented on each feed
        has_commented = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('has_commented')
            ).filter(FeedComment.is_deleted == False)
            .filter(FeedComment.user_id == g.user_id)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        # Check if current user has flagged each feed
        has_flagged = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('has_flagged')
            )
            .filter(FeedFlag.user_id == g.user_id)
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        # Subquery to get xircle creators
        xircle_creators = (
            g.client_db_session.query(
                Xircle.id.label('xircle_id'),
                Xircle.creator_id
            ).filter(Xircle.is_deleted == False).subquery()
        )

        # Calculate days since first login and get streak dates from main db
        user_streaks = g.db_session.query(UserStreak).filter_by(user_id=g.user_id).order_by(UserStreak.date_created).all()
        days_active = len(user_streaks)
        
        # Create a mapping of day number to streak date
        streak_dates_map = {i+1: streak.date_created for i, streak in enumerate(user_streaks)}

        # Main query with all joins
        common_query = (
            g.client_db_session.query(
                Feed.id,
                Feed.message,
                Feed.image,
                Feed.media,
                Feed.link,
                Feed.link_type,
                Feed.label,
                User.id.label('user_id'),
                User.first_name.label('user_first_name'),
                User.last_name.label('user_last_name'),
                User.preferred_name.label('user_preferred_name'),
                User.image.label('user_image'),
                User.title.label('user_title'),
                Xircle.id.label('xircle_id'),
                Xircle.name.label('xircle_name'),
                Xircle.image.label('xircle_image'),
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count'),
                func.coalesce(has_liked.c.has_liked > 0, False).label('has_liked'),
                func.coalesce(has_commented.c.has_commented > 0, False).label('has_commented'),
                func.coalesce(has_flagged.c.has_flagged > 0, False).label('has_flagged'),
                (Feed.user_id == g.user_id).label('can_edit'),  # Only feed owner can edit
                or_(
                    Feed.user_id == g.user_id,  # Feed owner can delete
                    and_(
                        Feed.xircle_id.isnot(None),
                        xircle_creators.c.xircle_id == Feed.xircle_id,
                        xircle_creators.c.creator_id == g.user_id  # Xircle owner can delete
                    )
                ).label('can_delete'),
                (not_(Xircle.name.ilike('xapa'))).label('can_comment'),  # Cannot comment if xircle is Xapa
                Feed.date_created.label('sort_date')
            )
            .outerjoin(User, User.id == Feed.user_id)
            .outerjoin(Xircle, Xircle.id == Feed.xircle_id)
            .outerjoin(likes_count, Feed.id == likes_count.c.feed_id)
            .outerjoin(flags_count, Feed.id == flags_count.c.feed_id)
            .outerjoin(comments_count, Feed.id == comments_count.c.feed_id)
            .outerjoin(has_liked, Feed.id == has_liked.c.feed_id)
            .outerjoin(has_commented, Feed.id == has_commented.c.feed_id)
            .outerjoin(has_flagged, Feed.id == has_flagged.c.feed_id)
            .outerjoin(xircle_creators, Feed.xircle_id == xircle_creators.c.xircle_id)
            .filter(
                Feed.is_deleted == False,
                Feed.status == 'active',
                Xircle.is_deleted == False,
            )
        )

        # Add seniority feed query - now including all achieved milestones
        seniority_query = (
            g.client_db_session.query(
                FeedSeniority.id,
                FeedSeniority.message,
                FeedSeniority.image,
                FeedSeniority.media,
                FeedSeniority.link,
                FeedSeniority.link_type,
                FeedSeniority.label,
                User.id.label('user_id'),
                User.first_name.label('user_first_name'),
                User.last_name.label('user_last_name'),
                User.preferred_name.label('user_preferred_name'),
                User.image.label('user_image'),
                User.title.label('user_title'),
                Xircle.id.label('xircle_id'),
                Xircle.name.label('xircle_name'),
                Xircle.image.label('xircle_image'),
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count'),
                func.coalesce(has_liked.c.has_liked > 0, False).label('has_liked'),
                func.coalesce(has_commented.c.has_commented > 0, False).label('has_commented'),
                func.coalesce(has_flagged.c.has_flagged > 0, False).label('has_flagged'),
                (FeedSeniority.user_id == g.user_id).label('can_edit'),
                (FeedSeniority.user_id == g.user_id).label('can_delete'),
                (not_(Xircle.name.ilike('xapa'))).label('can_comment'),  # Cannot comment if xircle is Xapa
                (case(
                    *[(FeedSeniority.days == day, date) for day, date in (streak_dates_map or {}).items()],
                    else_=FeedSeniority.date_created
                ) if streak_dates_map else FeedSeniority.date_created).label('sort_date')
            )
            .outerjoin(User, User.id == FeedSeniority.user_id)
            .outerjoin(Xircle, Xircle.id == FeedSeniority.xircle_id)
            .outerjoin(likes_count, FeedSeniority.id == likes_count.c.feed_id)
            .outerjoin(flags_count, FeedSeniority.id == flags_count.c.feed_id)
            .outerjoin(comments_count, FeedSeniority.id == comments_count.c.feed_id)
            .outerjoin(has_liked, FeedSeniority.id == has_liked.c.feed_id)
            .outerjoin(has_commented, FeedSeniority.id == has_commented.c.feed_id)
            .outerjoin(has_flagged, FeedSeniority.id == has_flagged.c.feed_id)
            .filter(
                FeedSeniority.days < days_active,
                FeedSeniority.is_deleted == False,
                FeedSeniority.status == 'active',
                Xircle.is_deleted == False,
            )
        )

        # Filter regular feeds
        if xircle_id:
            common_query = common_query.filter(Feed.xircle_id == xircle_id)
            seniority_query = seniority_query.filter(FeedSeniority.xircle_id == xircle_id)
        else:
            # Get list of xircle IDs where user is a member
            member_xircles = g.client_db_session.query(XircleMember.xircle_id).filter(XircleMember.user_id == g.user_id).scalar_subquery()
            
            # Get the global Xapa xircle ID
            global_xircle = g.client_db_session.query(Xircle.id).filter(Xircle.name.ilike('xapa'), Xircle.is_deleted == False).first()
            global_xircle_id = global_xircle.id if global_xircle else None

            if global_xircle_id:
                # Show feeds from user's xircles OR the global xircle
                common_query = common_query.filter(or_(Feed.xircle_id.in_(member_xircles), Feed.xircle_id == global_xircle_id))
                seniority_query = seniority_query.filter(or_(FeedSeniority.xircle_id.in_(member_xircles), FeedSeniority.xircle_id == global_xircle_id))
            else:
                # If no global xircle exists, just show feeds from user's xircles
                common_query = common_query.filter(Feed.xircle_id.in_(member_xircles))
                seniority_query = seniority_query.filter(FeedSeniority.xircle_id.in_(member_xircles))

        # Apply search filter if provided
        if search:
            common_query = common_query.filter(Feed.message.ilike(f'%{search}%'))
            seniority_query = seniority_query.filter(FeedSeniority.message.ilike(f'%{search}%'))

        # Filter out feeds from blocked users
        blocked_users = g.client_db_session.query(UserBlock.blocked_user_id).filter(UserBlock.user_id == g.user_id).subquery()
        blocked_users_select = blocked_users.select()
        common_query = common_query.filter(Feed.user_id.notin_(blocked_users_select))
        seniority_query = seniority_query.filter(FeedSeniority.user_id.notin_(blocked_users_select))

        # Combine regular feeds with seniority feeds and order by date
        query = common_query.union_all(seniority_query)
        
        # Create a subquery from the union for proper counting and pagination
        subquery = query.subquery()
        final_query = g.client_db_session.query(subquery).order_by(desc(subquery.c.sort_date))

        # Get total count and paginated results from the subquery
        total = final_query.count()
        results = final_query.limit(limit).offset(offset).all()
        
        data = []
        for (
            id, message, image, media, link, link_type, label,
            user_id, user_first_name, user_last_name, user_preferred_name, user_image, user_title, 
            xircle_id, xircle_name, xircle_image,
            likes, flags, comments, has_liked, has_commented, has_flagged, can_edit, can_delete, can_comment, date_created
        ) in results:
            feed_dict = {
                'id': id,
                'message': message,
                'image': image,
                'media': media,
                'link': link,
                'link_type': link_type,
                'label': label,
                'date_created': format_datetime_with_timezone(date_created),
                'likes_count': int(likes),
                'flags_count': int(flags),
                'comments_count': int(comments),
                'has_liked': bool(has_liked),
                'has_commented': bool(has_commented),
                'has_flagged': bool(has_flagged),
                'can_edit': bool(can_edit),
                'can_delete': bool(can_delete),
                'can_comment': bool(can_comment),
                'user': {
                    'id': user_id,
                    'first_name': user_first_name,
                    'last_name': user_last_name,
                    'preferred_name': user_preferred_name,
                    'image': user_image,
                    'title': user_title
                },
                'xircle': {
                    'id': xircle_id,
                    'name': xircle_name,
                    'image': xircle_image
                },
            }
            data.append(feed_dict)
        
        return create_response("Feed list", data=data, total=total, page=page, limit=limit)


## feed flag model
feed_flag_model = app_feed_api.model('FeedFlagItem', {
    'message': fields.String(required=False, description='Feed Flag Message')
})

## create, delete and get feed flag
@app_feed_api.doc(security='bearer')
@app_feed_api.route('/<string:feed_id>/flag', methods=['PUT'])
class FeedFlagObject(Resource):
    @app_feed_api.expect(feed_flag_model)
    def put(self, feed_id):
        """Toggle flag/unflag for a feed"""
        data = request.json

        message = data.get('message', '')

        # Check if user has already liked the feed
        existing_flag = g.client_db_session.query(FeedFlag).filter(
            FeedFlag.feed_id==feed_id,
            FeedFlag.user_id==g.user_id
        ).first()

        if existing_flag:
            # Unlike: Remove the like
            g.client_db_session.delete(existing_flag)
            action = "unflagged"
        else:
            # Like: Create new like
            new_flag = FeedFlag(
                feed_id=feed_id,
                user_id=g.user_id,
                message=message
            )
            g.client_db_session.add(new_flag)
            action = "flagged"

        g.client_db_session.commit()

        # Get updated likes count
        flags_count = g.client_db_session.query(FeedFlag).filter(FeedFlag.feed_id==feed_id).count()

        return create_response(
            f"Feed {action}",
            data={
                "has_flagged": action == "flagged",
                "flags_count": flags_count
            }
        )


@app_feed_api.doc(security='bearer')
@app_feed_api.route('/seniority')
class UserSeniorityFeed(Resource):
    def get(self):
        """Get seniority feed for specific user"""
        from sqlalchemy import func

        # Calculate days since first login
        days_active = g.db_session.query(UserStreak).filter_by(user_id=g.user_id).count()

        # Subqueries for counts and interactions
        likes_count = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('likes_count')
            )
            .filter(FeedLike.feed_id == FeedSeniority.id)
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        comments_count = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('comments_count')
            )
            .filter(FeedComment.feed_id == FeedSeniority.id, FeedComment.is_deleted == False)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        flags_count = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('flags_count')
            )
            .filter(FeedFlag.feed_id == FeedSeniority.id)
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        has_liked = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('has_liked')
            )
            .filter(
                FeedLike.feed_id == FeedSeniority.id,
                FeedLike.user_id == g.user_id
            )
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        has_commented = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('has_commented')
            )
            .filter(
                FeedComment.feed_id == FeedSeniority.id,
                FeedComment.user_id == g.user_id,
                FeedComment.is_deleted == False
            )
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        has_flagged = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('has_flagged')
            )
            .filter(
                FeedFlag.feed_id == FeedSeniority.id,
                FeedFlag.user_id == g.user_id
            )
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        # Find matching seniority with all counts and interactions
        feeds = (
            g.client_db_session.query(
                FeedSeniority,
                User,
                Xircle,
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count'),
                func.coalesce(has_liked.c.has_liked > 0, False).label('has_liked'),
                func.coalesce(has_commented.c.has_commented > 0, False).label('has_commented'),
                func.coalesce(has_flagged.c.has_flagged > 0, False).label('has_flagged')
            )
            .outerjoin(User, User.id == FeedSeniority.user_id)
            .outerjoin(Xircle, Xircle.id == FeedSeniority.xircle_id)
            .outerjoin(likes_count, FeedSeniority.id == likes_count.c.feed_id)
            .outerjoin(flags_count, FeedSeniority.id == flags_count.c.feed_id)
            .outerjoin(comments_count, FeedSeniority.id == comments_count.c.feed_id)
            .outerjoin(has_liked, FeedSeniority.id == has_liked.c.feed_id)
            .outerjoin(has_commented, FeedSeniority.id == has_commented.c.feed_id)
            .outerjoin(has_flagged, FeedSeniority.id == has_flagged.c.feed_id)
            .filter(
                FeedSeniority.days == days_active,
                FeedSeniority.is_deleted == False
            ).all()
        )

        if not feeds:
            return create_response("No seniority feed available", data={})

        data = []
        for feed, user, xircle, likes, flags, comments, has_liked, has_commented, has_flagged in feeds:
            feed_dict = feed.to_dict(['id', 'days', 'message', 'image', 'media', 'date_created', 'date_updated', 'link', 'link_type', 'label'])
            feed_dict['user'] = user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title'])
            feed_dict['xircle'] = xircle.to_dict(['id', 'name', 'image'])
            feed_dict['likes_count'] = int(likes)
            feed_dict['flags_count'] = int(flags)
            feed_dict['comments_count'] = int(comments)
            feed_dict['has_liked'] = bool(has_liked)
            feed_dict['has_commented'] = bool(has_commented)
            feed_dict['has_flagged'] = bool(has_flagged)
            feed_dict['can_edit'] = feed.user_id == g.user_id  # Only feed owner can edit
            feed_dict['can_delete'] = feed.user_id == g.user_id  # Only feed owner can delete
            data.append(feed_dict)
        
        return create_response("Seniority feed retrieved", data=data)
