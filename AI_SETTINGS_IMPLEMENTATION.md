# AI Settings for Clients - Implementation Summary

## Overview
This implementation adds AI preference settings for clients with automatic fallback to global client settings when specific preferences are missing.

## Changes Made

### 1. Default Client Settings Template
**File:** `static/files/client_settings.json`
- Added `preferences` object under `feature_configuration.ai`
- Includes required fields:
  - `agreement_title`: Title for the AI usage agreement
  - `agreement_terms_body`: HTML content for terms (with PDF link support)
  - `checkbox_label`: Label for the acceptance checkbox
  - `button_label`: Label for the action button
  - `version`: Version number for the agreement

### 2. App Client Settings API
**File:** `api/app/client_settings.py`
- Added fallback logic in `_ensure_ai_preferences()` method
- Automatically retrieves missing AI preference fields from global client settings
- Preserves existing client-specific preferences
- Handles partial configurations by filling only missing fields

### 3. CMS Client API
**File:** `api/cms/clients.py`
- Added `ensure_ai_preferences()` helper function
- Integrated fallback logic in client GET endpoint
- Ensures consistent behavior between app and CMS APIs

### 4. Comprehensive Tests
**File:** `tests/test_client_settings_api.py`
- Added tests for AI preferences fallback scenarios
- Tests for preserving existing preferences
- Tests for partial configuration handling
- Tests for empty settings scenarios

## API Behavior

### App Client Settings API (`/api/app/client_settings/settings`)
When a client requests their settings:
1. Retrieves client-specific settings from database
2. Checks if AI preferences contain all required fields
3. If fields are missing, queries global client (`id_key == "global"`) for defaults
4. Fills only missing fields, preserving any existing client-specific values
5. Returns merged settings to client

### CMS Client API (`/api/cms/clients/{id}`)
When CMS retrieves client details:
1. Gets client data including settings
2. Applies same fallback logic as app API
3. Returns client data with complete AI preferences

## Required Fields
The following fields are required in AI preferences:
- `agreement_title`
- `agreement_terms_body`
- `checkbox_label`
- `button_label`
- `version`

## Global Fallback Mechanism
- Global client must have `id_key == "global"`
- Global client settings serve as the default template
- Only missing fields are filled from global settings
- Client-specific settings always take precedence
- Fallback is non-destructive (doesn't modify original client settings)

## Error Handling
- If global client doesn't exist, original settings are returned unchanged
- If global client has no settings, original settings are returned unchanged
- Any errors in fallback logic return original settings to prevent API failures
- Non-destructive approach ensures system stability

## Example Usage

### Client with Missing AI Preferences
```json
// Client settings (before fallback)
{
  "feature_configuration": {
    "ai": {
      "enabled": true,
      "description": "Enabled AI"
    }
  }
}

// Client settings (after fallback)
{
  "feature_configuration": {
    "ai": {
      "enabled": true,
      "description": "Enabled AI",
      "preferences": {
        "agreement_title": "AI Usage Agreement",
        "agreement_terms_body": "<p>By using AI features, you agree to our terms and conditions. <a href='#' target='_blank'>View full terms (PDF)</a></p>",
        "checkbox_label": "I agree to the AI usage terms and conditions",
        "button_label": "Accept and Continue",
        "version": "1.0"
      }
    }
  }
}
```

### Client with Custom AI Preferences
```json
// Client settings (preserved as-is)
{
  "feature_configuration": {
    "ai": {
      "enabled": true,
      "description": "Enabled AI",
      "preferences": {
        "agreement_title": "Custom Company AI Agreement",
        "agreement_terms_body": "<p>Our company-specific AI terms.</p>",
        "checkbox_label": "I accept our company's AI usage policy",
        "button_label": "Proceed with AI",
        "version": "2.1"
      }
    }
  }
}
```

## Testing
Run tests with:
```bash
python -m unittest tests.test_client_settings_api -v
```

## Implementation Notes
- Uses deep copy to avoid modifying original settings objects
- Minimal performance impact (only processes when fields are missing)
- Backward compatible with existing client settings
- No database schema changes required
- Graceful degradation if global client is not available