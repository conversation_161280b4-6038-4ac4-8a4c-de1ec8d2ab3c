# XAPA Backend

XAPA is a web application backend built with Flask that provides APIs for:

- User authentication and account management with SSO support
- Content Management System (CMS) for managing:
  - Clients
  - Users 
  - Programs
  - Experiences ("Xperiences")
  - Quests
  - Attributes
  - Nodes
- Mobile app features including:
  - User management
  - Quest system
  - Social features ("Xircle")
  - Activity feeds
- File handling capabilities

The backend uses SQLAlchemy for database management with support for multiple database bindings. It provides comprehensive API documentation via Swagger UI and implements JWT-based authentication.

## Technical Stack

- **Framework**: Flask
- **Database**: SQLAlchemy with multi-tenant support
- **Authentication**: JWT tokens + SSO integration
- **API Documentation**: Flask-RestX (Swagger UI)
- **Security Features**:
  - Bearer token authentication
  - Proxy support with security headers
  - CORS configuration
  - Environment-based configurations (dev/prod)

## Getting Started

1. **Setup Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows use: venv\Scripts\activate
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment Configuration**
   - Copy `.env.example` to `.env`
   - Configure your environment variables:
     - `LOCAL_BINDS`: Local database bindings
     - `LOCAL_HOST`: Local database host
     - `LOCAL_DATABASE`: Local database name
     - `LOCAL_USERNAME`: Local database username
     - `LOCAL_PASSWORD`: Local database password
     - Other required environment variables
   - Setup the local environment variables: `LOCAL_SECRET_KEY`
   ```bash
   export LOCAL_SECRET_KEY={LOCAL_SECRET_KEY} # On Windows use: # On Windows use: venv\Scripts\activate
   ```

4. **Database Setup**
   ```bash
   flask db init
   flask db migrate
   flask db upgrade
   ```

5. **Run Development Server**
   ```bash
   python app.py
   # or
   flask run
   ```
   The application will start on http://localhost:5000

6. **Access API Documentation**
   - Global API docs: http://localhost:5000/
   - CMS API docs: http://localhost:5000/api/cms/document/
   - Account API docs: http://localhost:5000/api/account/document/
   - App API docs: http://localhost:5000/api/app/document/
   - File API docs: http://localhost:5000/api/file/document/

## Development

The application supports both development and production environments:
- Development mode includes environment variable loading from .env
- Production mode is configured for Azure deployment
- Database migrations are handled through Flask-Migrate
- API documentation is available at the /document/ endpoint for each API section

## API Structure

The API is organized into several main sections:

1. **Account API** (`/api/account`)
   - Authentication endpoints
   - Admin management
   - User token handling

2. **CMS API** (`/api/cms`)
   - Client management
   - User administration
   - Content management (Programs, Xperiences, Quests)
   - Attribute and node configuration

3. **App API** (`/api/app`)
   - Quest management
   - User interactions
   - Social features (Xircle)
   - Feed management

4. **File API** (`/api/file`)
   - File handling operations

5. **Global API**
   - System-wide functionality
   - Common utilities
6.